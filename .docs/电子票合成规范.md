# 电子票合成规范文档

## 1. 概述

本文档详细描述了电子票合成系统的技术规范，包括布局设计、尺寸要求、样式定义、数据处理和实现细节，用于指导其他编程语言的实现。

## 2. 整体画布规范

### 2.1 基本参数

- **画布宽度**: 520rpx (小程序单位，约 260px)
- **画布高度**: 动态高度 `ticketInfo.ticketH`
- **像素比例**: 2（高清显示）
- **圆角半径**: 40rpx (约 20px)
- **背景**: 使用 `ticketInfo.coverFrontUrl` 作为背景图片
- **输出格式**: Canvas 转临时文件路径

### 2.2 单位换算

- rpx 是微信小程序的响应式像素单位
- 1rpx = 设备屏幕宽度/750
- 在 iPhone6 上：1rpx = 0.5px
- 建议其他平台实现时按 2 倍像素密度处理

## 3. 布局结构

电子票分为三个主要区域：

1. **背景层**: 全覆盖背景图片
2. **顶部内容区**: 高度 150rpx
3. **底部内容区**: 高度 313rpx

## 4. 顶部内容区规范

### 4.1 容器规范

- **位置**: 绝对定位，top: 0, left: 0
- **尺寸**: 宽度 100%，高度 150rpx
- **圆角**: 顶部圆角 40rpx（border-radius: 40rpx 40rpx 0 0）

### 4.2 内容容器

- **尺寸**: 宽度 448rpx，高度自适应
- **内边距**: 36rpx（顶部、左右）
- **位置**: 绝对定位，top: 0, left: 0

### 4.3 剧场头像

- **数据源**: `ticketInfo.repertoireCoverPictureUrl`
- **尺寸**: 37rpx × 37rpx
- **样式**: 圆形头像（border-radius: 50%）
- **显示**: inline-block
- **适配**: object-fit: cover

### 4.4 剧目名称

- **数据源**: `ticketInfo.repertoire + "电子票根"`
- **尺寸**: 宽度 401rpx
- **位置**: margin: 0 0 0 10rpx（左边距 10rpx）
- **字体**:
  - 颜色: #FFFFFF
  - 大小: 28rpx
  - 行高: 37rpx
  - 粗细: normal

### 4.5 专属编号

- **数据源**: `ticketInfo.serialNumber`
- **尺寸**: 高度 34rpx，宽度自适应
- **位置**: margin: 10rpx 0 0 0（顶部间距 10rpx）
- **内边距**: 0 10rpx
- **字体**:
  - 颜色: #392584
  - 大小: 26rpx
  - 行高: 34rpx
  - 粗细: normal
- **背景**: #E6DAFF
- **圆角**: 17rpx

## 5. 底部内容区规范

### 5.1 容器规范

- **位置**: 绝对定位，bottom: 0, left: 0
- **尺寸**: 宽度 100%，高度 313rpx
- **圆角**: 底部圆角 40rpx（border-radius: 0 0 40rpx 40rpx）

### 5.2 用户头像

- **数据源**: `userInfo.avatarUrl`
- **尺寸**: 59rpx × 59rpx
- **位置**: 绝对定位，top: 0, left: 34rpx
- **样式**: 圆形头像（border-radius: 50%）
- **适配**: object-fit: cover

### 5.3 用户昵称

- **数据源**: `userInfo.name`
- **位置**: 绝对定位，top: 8rpx, left: 106rpx
- **字体**:
  - 颜色: #FFFFFF
  - 大小: 32rpx
  - 行高: 44rpx
  - 粗细: normal

### 5.4 演出信息

- **数据源**: `ticketInfo.dateTime + " | " + ticketInfo.theater`
- **位置**: 绝对定位，bottom: 170rpx, left: 18rpx
- **尺寸**: 宽度 484rpx
- **字体**:
  - 颜色: #FFFFFF
  - 大小: 24rpx
  - 行高: 28rpx
  - 粗细: normal
  - 对齐: center

### 5.5 分隔线

- **位置**: 绝对定位，bottom: 146rpx, left: 34rpx
- **尺寸**: 宽度 446rpx，高度 2rpx
- **背景**: rgba(255, 255, 255, 0.2)

### 5.6 票价标签

- **文本**: "1 张 | 总票价"
- **位置**: 绝对定位，bottom: 86rpx, left: 34rpx
- **字体**:
  - 颜色: rgba(255, 255, 255, 0.6)
  - 大小: 24rpx
  - 行高: 34rpx
  - 粗细: normal

### 5.7 座位标签

- **文本**: "座位"
- **位置**: 绝对定位，bottom: 86rpx, left: 208rpx
- **字体**:
  - 颜色: rgba(255, 255, 255, 0.6)
  - 大小: 24rpx
  - 行高: 34rpx
  - 粗细: normal

### 5.8 总票价

- **位置**: 绝对定位，bottom: 22rpx, left: 34rpx
- **组成**:
  - 货币符号"￥":
    - 字体大小: 24rpx
    - 行高: 34rpx
    - 上边距: 12rpx
    - 颜色: #FFFFFF
  - 价格数值:
    - 数据源: 处理后的 `ticketInfo.price`
    - 字体大小: 28rpx
    - 行高: 56rpx
    - 粗细: bold
    - 颜色: #FFFFFF

### 5.9 座位信息

**条件渲染**: 根据座位信息长度选择不同样式

#### 5.9.1 短座位信息（≤10 个字符）

- **数据源**: `ticketInfo.seat`
- **位置**: 绝对定位，bottom: 32rpx, left: 200rpx
- **字体**:
  - 颜色: #FFFFFF
  - 大小: 28rpx
  - 行高: 40rpx
  - 粗细: normal

#### 5.9.2 长座位信息（>10 个字符）

- **数据源**: `ticketInfo.seat`
- **位置**: 绝对定位，bottom: 12rpx, left: 200rpx
- **尺寸**: 宽度 300rpx，最小高度 68rpx
- **字体**:
  - 颜色: #FFFFFF
  - 大小: 22rpx
  - 行高: 34rpx
  - 粗细: normal

## 6. 数据处理规范

### 6.1 必需数据结构

#### 6.1.1 ticketInfo 对象

```javascript
{
  ticketH: "票据高度",
  coverFrontUrl: "背景图片URL",
  repertoireCoverPictureUrl: "剧场头像URL",
  repertoire: "剧目名称",
  serialNumber: "专属编号",
  dateTime: "演出时间",
  theater: "演出地点",
  price: "票价（需要处理）",
  seat: "座位信息"
}
```

#### 6.1.2 userInfo 对象

```javascript
{
  avatarUrl: "用户头像URL",
  name: "用户昵称"
}
```

### 6.2 价格数据处理

票价数据需要进行以下处理：

1. **移除"元"字符**: `price.replace(/元/g, '')`
2. **移除"￥"符号**: `price.split('￥').join('')`
3. **移除"¥"符号**: `price.split('¥').join('')`
4. **数值转换**: 如果是有效数字则转换为 Number 类型
5. **零值处理**: 如果结果为 0，保持字符串格式"0"

```javascript
function processPrice(price) {
  if (!price) return '0'

  let temp = price
  temp = temp.replace(/元/g, '')
  temp = temp.split('￥').join('')
  temp = temp.split('¥').join('')

  if (!isNaN(Number(temp))) {
    temp = Number(temp)
  }

  if (temp === 0) {
    temp = temp + ''
  }

  return temp
}
```

## 7. 技术实现要求

### 7.1 图片处理

- **背景图片**: 需要支持 object-fit: cover 的裁剪方式
- **头像**: 必须处理为圆形，支持 object-fit: cover
- **跨域**: 可能需要处理图片跨域问题

### 7.2 字体渲染

- **中文字符**: 确保中文字符正确渲染
- **行高**: 严格按照指定行高渲染
- **颜色**: 支持 rgba 和十六进制颜色值

### 7.3 布局定位

- **绝对定位**: 所有元素使用绝对定位，确保精确位置
- **层级**: 确保正确的元素层级关系
- **圆角**: 支持不同圆角半径和组合圆角

### 7.4 输出要求

- **像素密度**: 输出 2 倍像素密度图片
- **格式**: 支持 PNG 格式输出
- **质量**: 确保输出图片质量

## 8. 错误处理

### 8.1 数据缺失处理

- **图片加载失败**: 需要有降级处理方案
- **文本数据缺失**: 使用默认值或空字符串
- **用户信息缺失**: 确保不会导致渲染失败

### 8.2 异常情况

- **网络异常**: 图片加载失败的处理
- **渲染失败**: 提供错误回调机制
- **数据格式错误**: 数据验证和容错处理

## 9. 性能优化建议

### 9.1 图片优化

- **预加载**: 提前加载必要图片
- **缓存**: 实现图片缓存机制
- **压缩**: 适当的图片压缩

### 9.2 渲染优化

- **异步渲染**: 避免阻塞主线程
- **进度反馈**: 提供渲染进度回调
- **内存管理**: 及时释放渲染资源

## 10. 测试验证

### 10.1 视觉验证

- **像素级对比**: 与原始实现进行像素级对比
- **不同设备**: 确保在不同分辨率设备上显示正确
- **边界情况**: 测试极长文本、特殊字符等情况

### 10.2 数据验证

- **各种价格格式**: 测试不同的价格数据格式
- **座位信息**: 测试长短不同的座位信息
- **特殊字符**: 测试特殊字符的处理

## 11. 实现参考

### 11.1 关键算法

本实现基于 lime-painter 库，核心是 Canvas 2D 渲染。其他平台可以参考：

- **Web**: HTML5 Canvas
- **iOS**: Core Graphics / UIKit
- **Android**: Canvas / Custom View
- **Flutter**: CustomPainter
- **React Native**: SVG / Canvas

### 11.2 坐标系统

- **原点**: 左上角(0,0)
- **X 轴**: 向右为正
- **Y 轴**: 向下为正
- **单位**: 建议使用 dp/pt 等密度无关像素

---

**注意**: 本规范基于微信小程序的 rpx 单位系统，实现时需要根据目标平台进行相应的单位转换和适配。
