# 演都 App 功能需求文档

## 应用整体架构

**底部导航栏模块布局**：首页、日历、扫票、票夹、我的（扫票居中突出显示）

---

## 一、首页功能详细需求

### 1.1 搜索功能模块

#### 1.1.1 AI 剧目评论智能分析

**核心功能**：

- 引导式问题设置
  - 预设引导问题示例："6 月有哪些好看的演出？"、"上海有什么推荐音乐会？"、"SIX 值得看吗？"
- AI 深度分析输出
  - 使用 DS 模型自带深度思考功能进行内容分析
  - 支持单个剧目分析和综合推荐

**数据源与引用规范**：

- 全部内容引用自数据库，严禁 AI 编撰虚假信息
- 引用来源支持折叠显示，确保可溯源互动

**分析内容结构**：

- **常规数据分析**：好差评数量统计、评论篇数统计、好评占比计算
- **AI 智能内容分析**：分为优点分析、缺点分析、总体评价三个部分

**数据缓存策略**：

- 每个剧目在评论数据无变化情况下保存 5 种 AI 分析结果
- 目标：减少 AI 接口访问量，降低运营成本

### 1.2 动态信息插件

#### 1.2.1 显示内容语义升级

**文案优化映射**：

```
原显示文案 → 新显示文案
------------------------
下班 → 距开演
发薪 → 保留原文案
周五 → 下场开票
节气 → 下场演出
今天赚了 → 看剧基金+
```

#### 1.2.2 UI 设计规范

- **分层设计**：框框底版和右侧图标设计为独立可调图层
- **个性化设置**：在设置页面支持用户自定义调整

### 1.3 我的榜单展示区

#### 1.3.1 显示位置与方式

- 位置：首页第一栏显示区域
- 交互方式：左右滑动浏览榜单内容
- 空状态处理：未添加榜单时显示空位+号，引导用户添加内容

### 1.4 足迹广场（观剧社区）

#### 1.4.1 展示样式设计

- **布局方式**：瀑布流布局（参考小红书样式）
- **信息层级**：
  - 顶部：剧目名称和具体场次信息
  - 主体：用户发布的多媒体内容
- **排序规则**：按时间倒序排列
- **内容标识**：支持精品、热帖、置顶标记

#### 1.4.2 内容发布机制

**发布权限控制**：

- 仅允许在已扫票场次下发布内容
- 确保每条内容都关联具体演出场次
- 同一场次支持用户多次发布

**内容类型支持**：

- 多媒体上传：照片、视频、文字评价
- 剧评导入：支持长截图 OCR 识别+大模型分析导入

**封面生成策略**：

- 优先级 1：用户上传图片
- 优先级 2：后台已有该剧目时使用官方海报
- 优先级 3：后台无剧目信息时使用默认封面

#### 1.4.3 隐私与编辑控制

- **隐私设置**：用户可选择内容公开或私密
- **内容管理**：发布后支持编辑修改内容

---

## 二、日历页面功能详细需求

### 2.1 双模式日历系统

#### 2.1.1 模式切换功能

- **看剧日历模式**：显示观演记录和计划
- **物料日历模式**：显示剧场物料信息
- **切换方式**：一键模式切换按钮

#### 2.1.2 看剧日历核心功能

**数据同步机制**：

- 扫票后自动同步票务信息到日历
- 区分显示：已观看剧目 vs 未观看剧目

**视图选择**：

- 默认：按月显示
- 可选：按季度显示、按年显示

**统计信息展示**：

- 观演场次统计：总计 XX 场
- 票房贡献统计：贡献票房 XXXX 元
- 隐私控制：统计信息可设置隐藏

**分享功能**：

- 支持生成分享图片功能

### 2.2 物料管理系统

#### 2.2.1 物料信息发布

**信息要素结构**：

1. 发布时间
2. 领取条件说明
3. 物料图片展示
4. 发放地点信息

**发布机制**：

- 用户手动添加物料信息
- 添加后自动在对应日期和场次下生成笔记信息
- 定位为公共信息共享

#### 2.2.2 物料展示与互动

**展示方式**：

- 在日历中直接展示当天物料情况
- 物料条目内置"想领"交互按钮

**权限管理**：

- 支持编辑和删除权限控制
- 同剧目观众可见性设置

**互动功能**：

- 想领登记系统：支持即时登记和取消
- 名单可视化：显示登记用户列表
- 多条目支持：支持并列显示多个物料条目

---

## 三、扫票页面功能需求

（保留现有扫票功能，具体需求待补充）

---

## 四、电子票夹页面功能详细需求

### 4.1 票夹展示系统

#### 4.1.1 展示样式设计

- **参考样式**：结合现有电子票夹形式 + 淘票票票夹册形式
- **详情展示**：每张票点击后显示详细信息，与足迹广场内容格式一致（排版差异化）

#### 4.1.2 票据类型支持

- **电子票据**：数字化票据管理
- **实体票据**：支持实体票上传和显示功能

---

## 五、我的页面功能详细需求

### 5.1 用户信息展示区

#### 5.1.1 基础信息模块

- 用户头像显示
- 头像背景设置
- 积分等级系统
- 关注数统计
- 粉丝数统计

### 5.2 内容管理整合区

#### 5.2.1 观剧足迹整合

- **整合内容**：观演历史、电子票夹、个人笔记
- **统一入口**：合并为"观剧足迹"单一入口

### 5.3 社交功能管理区

#### 5.3.1 关注系统升级

- **用户关注**：新增用户与用户之间的关注功能
- **现有保留**：保持对剧目和剧场的关注功能

#### 5.3.2 消息通知系统

**消息类型管理**：

- 我的关注消息
- 互动消息提醒
- 私信消息管理

**消息处理功能**：

- 红点提醒显示
- 一键已读功能：可清空所有红点提醒

#### 5.3.3 数据广场功能调整

**功能定位调整**：

- 剧目剧场功能重新定位为"数据广场"
- 取消关注后的私信对话处理提示

**内容更新提醒**：

- 当数据条目下有新笔记发布时触发内容更新提醒
- 提醒文案："更新 X 条笔记"
- 引导用户点击查看新内容
