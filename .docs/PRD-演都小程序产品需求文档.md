# 演都小程序产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位

演都是一个专注于舞台剧文化的综合性小程序平台，集剧院信息展示、演出票务、社交互动、收藏分享于一体的舞台剧文化生态系统。

### 1.2 目标用户

- **主要用户**：舞台剧爱好者、文艺青年、演出观众
- **次要用户**：剧院工作人员、演员、舞台剧从业者
- **潜在用户**：对文化娱乐感兴趣的普通用户

### 1.3 产品价值

- 为用户提供便捷的剧院信息查询和票务服务
- 构建舞台剧文化社交生态，促进用户交流互动
- 通过数字化收藏和成就系统，增强用户参与度
- 推广舞台剧文化，扩大舞台剧艺术影响力

## 2. 功能架构

### 2.1 核心功能模块

#### 2.1.1 首页模块 (Homepage)

**功能描述**：应用入口，展示推荐内容和快速导航

**主要功能**：

- 推荐剧院展示
- 推荐剧目展示
- 快速搜索入口
- 用户个性化推荐
- 城市切换功能

**页面路径**：`/pages/homepage/index`

#### 2.1.2 剧院模块 (Theater)

**功能描述**：剧院信息展示和管理

**主要功能**：

- 剧院列表展示（分页）
- 剧院详情查看
- 剧院搜索和筛选
- 地理位置定位
- 剧院收藏功能

**相关 API**：

- 查询首页推荐剧场：`$theaterListByIndex`
- 查询剧场列表：`$theaterListByPage`
- 查询剧场详情：`$theaterDetails`

**页面路径**：`/pages/sub/detail/theater`

#### 2.1.3 剧目模块 (Repertoire)

**功能描述**：演出剧目信息展示和管理

**主要功能**：

- 剧目列表展示
- 剧目详情查看
- 剧目标签分类
- 主创团队信息
- 演出场次信息
- 剧目收藏和分享

**相关 API**：

- 查询剧目列表：`$repertoireListByPage`
- 查询剧目详情：`$repertoireDetails`
- 查询剧目标签：`$repertoireLabelList`
- 查询主创团队：`$repertoireCreativeTeamList`

**页面路径**：`/pages/sub/detail/repertoire`

#### 2.1.4 票务模块 (Ticket)

**功能描述**：电子票管理和展示

**主要功能**：

- 电子票列表展示
- 电子票详情查看
- 电子票分组管理
- 票务统计信息
- 演员信息记录

**相关 API**：

- 查询用户电子票：`$electronicTicketByUserId`
- 查询电子票详情：`$ticketDetail`
- 电子票分组管理：`$ticketGroupList`、`$ticketGroupAdd`
- 查询纸质票总金额：`$sumPrice`

**页面路径**：`/pages/sub/ticket/list`、`/pages/sub/ticket/detail`

### 2.2 用户系统模块

#### 2.2.1 账户管理 (Account)

**功能描述**：用户注册、登录、个人信息管理

**主要功能**：

- 微信登录
- 用户信息管理
- 密码修改
- 手机号码修改
- 账户注销
- 实名认证

**相关 API**：

- 微信登录：`$loginByWechat`
- 获取用户信息：`$getUserInfo`
- 修改用户信息：`$userUpdate`
- 密码管理：`$updatePwd`、`$updatePwdByPhone`
- 手机号管理：`$updatePhone`

**页面路径**：

- 登录：`/pages/sub/account/login`
- 用户信息：`/pages/sub/account/userInfo`
- 设置：`/pages/sub/account/setting`

#### 2.2.2 个人中心 (Personal)

**功能描述**：用户个人主页和功能入口

**主要功能**：

- 个人信息展示
- 功能入口导航
- 用户统计信息
- 快速设置入口

**页面路径**：`/pages/account/personal`

### 2.3 社交互动模块

#### 2.3.1 评论系统 (Comment)

**功能描述**：用户评论和互动功能

**主要功能**：

- 剧目评论
- 评论回复
- 评论点赞
- 评论管理

**相关 API**：

- 评论相关 API（从 comment.ts 推断）

**页面路径**：`/pages/sub/comment/list`、`/pages/sub/comment/detail`

#### 2.3.2 关注系统 (Follow)

**功能描述**：用户关注和粉丝管理

**主要功能**：

- 用户关注
- 关注列表管理
- 粉丝列表查看
- 关注动态

**页面路径**：`/pages/sub/follow/list`

#### 2.3.3 互动功能 (Interaction)

**功能描述**：用户间互动和消息系统

**主要功能**：

- 点赞互动
- 消息通知
- 系统消息
- 互动历史

**页面路径**：`/pages/sub/interaction/list`

### 2.4 收藏系统模块

#### 2.4.1 收藏管理 (Collection)

**功能描述**：用户收藏内容管理

**主要功能**：

- 剧目收藏
- 剧院收藏
- 收藏分类
- 收藏分享

**页面路径**：`/pages/collection/list`、`/pages/sub/collection/detail`

#### 2.4.2 历史记录 (History)

**功能描述**：用户浏览和操作历史

**主要功能**：

- 浏览历史
- 评论历史
- 操作记录
- 历史清理

**页面路径**：`/pages/sub/history/list`、`/pages/sub/history/comment`

### 2.5 成就系统模块

#### 2.5.1 徽章系统 (Badge)

**功能描述**：纪念徽章收集和展示

**主要功能**：

- 徽章收集
- 徽章展示
- 徽章分享
- 佩戴状态管理

**相关 API**：

- 查询徽章详情：`$souvenirDetails`
- 修改佩戴状态：`$updateAdornStatus`

**页面路径**：`/pages/sub/badge/list`、`/pages/sub/badge/detail`

#### 2.5.2 勋章系统 (Medal)

**功能描述**：等级勋章和成就系统

**主要功能**：

- 等级勋章展示
- 成就解锁
- 勋章分组
- 进度跟踪

**相关 API**：

- 等级勋章列表：`$collectionListByRankMedal`
- 勋章详情：`$rankMedalDetails`

**页面路径**：`/pages/sub/medal/list`

#### 2.5.3 排行榜系统 (Rank)

**功能描述**：用户排行榜和竞争系统

**主要功能**：

- 用户排行榜
- 榜单编辑
- 榜单评论
- 排名统计

**相关 API**：

- 查询用户榜单：`$leaderboardList`
- 榜单管理：`$leaderboardAdd`、`$leaderboardUpdate`
- 榜单评论：`$LeaderboardComment`

**页面路径**：`/pages/sub/rank/index`、`/pages/sub/rank/list`

### 2.6 工具功能模块

#### 2.6.1 搜索功能 (Search)

**功能描述**：全局搜索和筛选功能

**主要功能**：

- 剧院搜索
- 剧目搜索
- 用户搜索
- 搜索历史
- 热门搜索

**页面路径**：`/pages/sub/tabulation/search`、`/pages/sub/tabulation/result`

#### 2.6.2 扫码功能 (Scan)

**功能描述**：二维码扫描和相关功能

**主要功能**：

- 票据扫描
- 信息录入
- 头像保存
- 演员添加
- 扫描结果处理

**页面路径**：

- 扫描结果：`/pages/sub/scan/result`
- 信息接收：`/pages/sub/scan/receive`
- 票据保存：`/pages/sub/scan/ticketSave`

#### 2.6.3 城市选择 (City)

**功能描述**：地理位置和城市切换

**主要功能**：

- 城市列表
- 定位功能
- 城市切换
- 地区筛选

**页面路径**：`/pages/sub/city/select`

### 2.7 订单管理模块

#### 2.7.1 订单系统 (Order)

**功能描述**：用户订单管理

**主要功能**：

- 订单列表
- 订单详情
- 订单状态跟踪
- 取消订单
- 支付管理

**相关 API**：

- 查询订单数量：`$userOrderNum`
- 订单列表：`$userOrderList`
- 订单详情：`$userOrderDetail`
- 取消订单：`$cancelOrder`

**页面路径**：`/pages/sub/order/list`、`/pages/sub/order/detail`

## 3. 技术架构

### 3.1 技术栈

- **前端框架**：uni-app + Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI 组件库**：uview-plus
- **样式框架**：Tailwind CSS + SCSS
- **路由管理**：uni-crazy-router
- **工具库**：lodash、dayjs
- **构建工具**：Vite

### 3.2 项目结构

```
src/
├── api/                 # API接口层
├── components/          # 公共组件
├── pages/              # 页面文件
├── stores/             # 状态管理
├── utils/              # 工具函数
├── router/             # 路由配置
├── static/             # 静态资源
└── uni_modules/        # 第三方组件
```

### 3.3 核心组件

- **请求封装**：基于 luch-request 的 HTTP 请求库
- **路由管理**：uni-crazy-router 路由封装
- **状态管理**：Pinia 全局状态管理
- **UI 组件**：uview-plus 组件库
- **工具函数**：防抖、节流、时间格式化等

## 4. 用户体验设计

### 4.1 交互设计原则

- **简洁直观**：界面简洁，操作直观易懂
- **一致性**：保持设计风格和交互模式的一致性
- **响应迅速**：快速响应用户操作，提供及时反馈
- **容错性**：提供友好的错误提示和处理机制

### 4.2 视觉设计

- **主题色彩**：深色主题（#07011D），紫色主题色（#9C75D3）
- **字体系统**：PingFang SC 字体族
- **图标系统**：统一的图标设计语言
- **布局规范**：基于 750rpx 设计稿的响应式布局

### 4.3 用户流程

1. **首次使用**：引导用户完成城市选择和基础设置
2. **日常使用**：首页浏览 → 内容查看 → 收藏/互动
3. **深度使用**：票务管理 → 成就收集 → 社交互动

## 5. 业务逻辑

### 5.1 用户权限体系

- **游客用户**：可浏览基础内容，无法进行互动操作
- **注册用户**：完整功能权限，可进行所有操作
- **认证用户**：通过实名认证，享有特殊权限

### 5.2 数据流转

1. **内容展示**：后端 API → 前端展示 → 用户浏览
2. **用户操作**：用户操作 → 前端处理 → API 调用 → 数据更新
3. **状态同步**：本地状态 ↔ 服务器状态 ↔ 缓存状态

### 5.3 核心业务流程

#### 5.3.1 票务流程

1. 用户扫描票据二维码
2. 系统识别票据信息
3. 保存电子票到用户账户
4. 支持分组管理和信息编辑

#### 5.3.2 收藏流程

1. 用户浏览剧院/剧目内容
2. 点击收藏按钮
3. 保存到个人收藏列表
4. 支持分类和管理

#### 5.3.3 成就流程

1. 用户完成特定行为
2. 系统检测成就条件
3. 解锁相应徽章/勋章
4. 更新用户等级和排名

## 6. 性能要求

### 6.1 响应时间

- **页面加载**：首屏加载时间 < 2 秒
- **接口响应**：API 接口响应时间 < 1 秒
- **图片加载**：支持懒加载，优化加载体验

### 6.2 兼容性

- **平台支持**：微信小程序为主要平台
- **设备适配**：支持主流手机屏幕尺寸
- **系统版本**：兼容微信最近 3 个版本

### 6.3 稳定性

- **错误处理**：完善的错误捕获和处理机制
- **数据备份**：重要数据本地缓存备份
- **降级方案**：网络异常时的降级处理

## 7. 安全要求

### 7.1 用户隐私

- **数据加密**：敏感数据传输加密
- **权限控制**：最小化权限申请
- **隐私政策**：明确的隐私政策和用户协议

### 7.2 数据安全

- **输入验证**：严格的前端输入验证
- **XSS 防护**：防止跨站脚本攻击
- **CSRF 防护**：防止跨站请求伪造

## 8. 运营策略

### 8.1 用户获取

- **社交分享**：支持微信分享传播
- **内容营销**：优质剧目内容推荐
- **活动运营**：定期举办主题活动

### 8.2 用户留存

- **成就系统**：通过徽章勋章提升用户粘性
- **社交功能**：用户互动增强社区氛围
- **个性化推荐**：基于用户行为的内容推荐

### 8.3 商业模式

- **票务服务**：提供票务预订和管理服务
- **会员服务**：高级会员功能和权益
- **广告收入**：精准的文化娱乐广告投放

## 9. 迭代规划

### 9.1 V1.0 版本（当前版本）

- ✅ 基础功能模块完成
- ✅ 用户系统搭建完成
- ✅ 票务管理功能
- ✅ 收藏和成就系统

### 9.2 V1.1 版本（优化版本）

- 🔄 性能优化和体验提升
- 🔄 新增推荐算法
- 🔄 社交功能增强
- 🔄 数据统计和分析

### 9.3 V2.0 版本（功能扩展）

- 📋 直播功能
- 📋 付费内容
- 📋 线下活动
- 📋 商城功能

## 10. 风险评估

### 10.1 技术风险

- **平台依赖**：依赖微信小程序平台政策
- **性能瓶颈**：大量数据处理的性能问题
- **兼容性问题**：不同设备和版本的兼容性

### 10.2 业务风险

- **内容版权**：剧目内容的版权问题
- **用户流失**：竞品竞争导致的用户流失
- **政策风险**：相关政策法规的变化

### 10.3 应对措施

- **技术备案**：多平台适配和备用方案
- **内容合规**：建立完善的内容审核机制
- **用户调研**：定期进行用户需求调研和满意度调查

---

**文档版本**：V1.0  
**创建日期**：2024 年  
**最后更新**：2024 年  
**负责人**：产品团队
