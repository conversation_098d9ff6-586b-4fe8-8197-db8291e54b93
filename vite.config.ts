/** @type {import('vite').UserConfig} */
import uni from '@dcloudio/vite-plugin-uni'
import autoprefixer from 'autoprefixer'
import { URL, fileURLToPath } from 'node:url'
import rem2px from 'postcss-rem-to-responsive-pixel'
import tailwindcss from 'tailwindcss'
// import h5ProdEffectPlugin from 'uni-vite-plugin-h5-prod-effect'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig, loadEnv } from 'vite'
// import eslintPlugin from 'vite-plugin-eslint'
import { UnifiedViteWeappTailwindcssPlugin as uvwt } from 'weapp-tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env

  return {
    plugins: [
      uni(),
      uvwt({
        jsMatcher: (file: any) => {
          if (file == 'components/Model.js') return false
          return /^components|pages/.test(file) && /\.js$/.test(file)
        }
      }),

      // h5ProdEffectPlugin(),
      AutoImport({
        imports: ['vue', 'pinia', 'uni-app'],
        dts: 'src/auto-import.d.ts',
        eslintrc: {
          enabled: true
        }
      })
      // 注释掉ESLint插件，避免开发时格式化干扰
      // eslintPlugin({
      //   include: ['./src/**/*.{vue,js,ts,jsx,tsx}'],
      // }),
    ],
    css: {
      postcss: {
        plugins: [
          tailwindcss(),
          autoprefixer(),
          rem2px({
            // 32 意味着 1rem = 32rpx
            rootValue: 32,
            // 默认所有属性都转化
            propList: ['*'],
            // 转化的单位,可以变成 px / rpx
            transformUnit: 'rpx'
          })
        ]
      }
    },
    resolve: {
      // 配置路径别名
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      sourcemap: false,
      brotliSize: false,
      reportCompressedSize: false
    }
  }
})
