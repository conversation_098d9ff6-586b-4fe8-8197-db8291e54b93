import { $getUserInfo } from '@/api/account'
import { $iconFormat, $picFormat } from '@/utils/methods'

export const useGlobalStore = defineStore('global', () => {
  /* 用户token */
  const token = ref(uni.getStorageSync('token') || '')
  /* 用户信息 */
  const userInfo = ref(uni.getStorageSync('userInfo') || '')
  /* 定位信息 */
  const addressInfo = ref(uni.getStorageSync('addressInfo') || { id: '', name: '' })

  /* 刷新用户信息 */
  const handleRefreshUserInfo = async () => {
    await $getUserInfo().then((res: any) => {
      if (res.data.avatar) res.data.avatarUrl = $picFormat(res.data.avatar)
      else {
        res.data.avatarUrl = $iconFormat('avatar.jpg')
      }

      uni.setStorageSync('userInfo', res.data)

      userInfo.value = res.data
    })
  }

  /* 扫描结果 */
  const scanResult = ref<any>(uni.getStorageSync('scanResult') || '')

  /* 下拉刷新配置1 */
  const downOpt1 = reactive({ textColor: '#ffffff' })
  /* 下拉刷新配置2 */
  const downOpt2 = reactive({ textColor: '#333333' })
  /* 上拉加载配置1 */
  const upOpt1 = reactive({ use: false })
  /* 上拉加载配置2 */
  const upOpt2 = reactive({
    textColor: '#ffffff',
    textNoMore: '- 暂无更多 -',
    page: {
      size: 10
    },
    noMoreSize: 1,
    onScroll: true,
    empty: {
      use: true,
      icon: 'http://***************:8004/files/images/common/no-data.png',
      tip: '暂无相关数据'
    }
  })
  /* 上拉加载配置3 */
  const upOpt3 = reactive({
    textColor: '#ffffff',
    textNoMore: '- 暂无更多 -',
    page: {
      size: 10
    },
    toTop: {
      bottom: 200,
      safearea: true
    },
    noMoreSize: 1,
    onScroll: true,
    empty: false
  })
  /* 上拉加载配置4 */
  const upOpt4 = reactive({
    textColor: '#ffffff',
    textNoMore: '- 暂无更多 -',
    page: {
      size: 20
    },
    toTop: {
      bottom: 200,
      safearea: true
    },
    noMoreSize: 1,
    onScroll: true,
    empty: false
  })
  /* 导航栏样式1（居中） */
  const titleStyle1 = reactive({
    color: '#ffffff',
    fontWeight: 500,
    fontSize: '36rpx',
    fontFamily: 'PingFangSC-Medium, PingFang SC'
  })
  /* 导航栏样式2（靠左） */
  const titleStyle2 = reactive({
    color: '#ffffff',
    fontWeight: 500,
    fontSize: '36rpx',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    textAlign: 'left'
  })

  return {
    token,
    userInfo,
    addressInfo,
    handleRefreshUserInfo,
    scanResult,
    downOpt1,
    downOpt2,
    upOpt1,
    upOpt2,
    upOpt3,
    upOpt4,
    titleStyle1,
    titleStyle2
  }
})
