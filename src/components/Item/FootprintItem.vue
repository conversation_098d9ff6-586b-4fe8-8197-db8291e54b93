<template>
  <view>
    <view class="footprintItem relative mb-[16rpx] overflow-hidden rounded-[20rpx] bg-w-5" :key="index" @tap="$push({ name: 'FootprintDetail', params: { id: i.id } })" v-for="(i, index) in list">
      <!-- 封面图片 -->
      <u-image class="postPic" :height="i.imageHeight || '320rpx'" :src="i.coverPicture ? $picFormat(i.coverPicture) : ''" bgColor="transparent" mode="aspectFill" width="100%"></u-image>

      <!-- 内容区域 -->
      <view class="content p-3">
        <!-- 剧目/剧场信息 -->
        <view class="mb-2 flex items-center justify-start">
          <text class="fas fa-theater-masks mr-1" style="font-size: 24rpx; color: #8b5cf6"></text>
          <text class="line-clamp-1 flex-1 font-Regular text-[22rpx] font-normal leading-[32rpx] text-lightPurple">
            {{ i.repertoireName || i.theaterName }}
          </text>
        </view>

        <!-- 场次信息 -->
        <!-- <view class="mb-2 font-Medium text-[26rpx] font-medium leading-[36rpx] text-w-100" v-if="i.showTime">
          {{ i.showTime }}
        </view> -->

        <!-- 用户内容 -->
        <view class="mb-3 line-clamp-3 break-all font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-80" v-if="i.content">
          {{ i.content }}
        </view>

        <!-- 底部信息 -->
        <view class="flex items-center justify-between">
          <!-- 用户信息 -->
          <view class="flex items-center">
            <u-avatar :defaultUrl="$iconFormat('avatar.jpg')" :src="i.userAvatar ? $picFormat(i.userAvatar) : ''" size="32rpx"></u-avatar>
            <text class="ml-1 font-Regular text-[20rpx] font-normal leading-[28rpx] text-w-60">{{ i.userName }}</text>
          </view>

          <!-- 互动数据 -->
          <view class="flex items-center space-x-3">
            <view class="flex items-center">
              <text class="fas fa-heart mr-1" style="font-size: 24rpx; color: rgba(255, 255, 255, 0.6)"></text>
              <text class="font-Regular text-[20rpx] font-normal leading-[28rpx] text-w-60">{{ i.likeCount || 0 }}</text>
            </view>
            <view class="flex items-center">
              <text class="fas fa-comment mr-1" style="font-size: 24rpx; color: rgba(255, 255, 255, 0.6)"></text>
              <text class="font-Regular text-[20rpx] font-normal leading-[28rpx] text-w-60">{{ i.commentCount || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
import { $iconFormat, $picFormat, $push } from '@/utils/methods'

const props = defineProps({
  list: { type: Array, default: () => [] }
})

const emit = defineEmits(['updateList'])

// 计算图片高度，实现瀑布流效果
const calculateImageHeight = item => {
  // 根据内容长度和图片数量动态计算高度
  const baseHeight = 240
  const contentHeight = item.content ? Math.min(item.content.length * 2, 100) : 0
  const imageHeight = item.images?.length > 0 ? 120 : 0

  return baseHeight + contentHeight + imageHeight + 'rpx'
}

onMounted(() => {
  // 为每个项目计算高度
  props.list.forEach(item => {
    if (!item.imageHeight) {
      item.imageHeight = calculateImageHeight(item)
    }
  })
})

watch(
  () => props.list,
  newList => {
    emit('updateList', newList)
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.footprintItem {
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  .content {
    .grid {
      display: grid;

      &.grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      &.grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      &.gap-1 {
        gap: 4rpx;
      }
    }
  }
}
</style>
