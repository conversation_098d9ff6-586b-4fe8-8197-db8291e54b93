<template>
  <template :key="i.id" v-for="(i, index) in list as any">
    <view class="evaluateItem mb-[30rpx] flex w-full items-start justify-start" v-show="!hideMore || index <= 1">
      <!-- 头像 -->
      <u-avatar
        class="shrink-0"
        :defaultUrl="$iconFormat('avatar.jpg')"
        :src="$picFormat(i.userAvatar)"
        size="60rpx"></u-avatar>
      <view class="ml-[10rpx] grow">
        <view class="mb-[10rpx] flex w-full items-center justify-start">
          <!-- 名字 -->
          <view class="w-ful font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ i.userName }}</view>
          <!-- 优质回答 -->
          <view
            class="ml-[10rpx] h-4 w-fit rounded-[8rpx] bg-purpleBg pl-1 pr-1 font-Regular text-[24rpx] font-normal leading-[32rpx] text-purple"
            v-if="i.top === 1"
            >优质评价</view
          >
        </view>

        <view
          class="mb-[10rpx] w-full font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60"
          v-if="i.startTime"
          >场次：{{ dayjs(i.startTime).format('YYYY-MM-DD') }} {{ dayjs(i.startTime).format('dddd') }}
          {{ dayjs(i.startTime).format('HH:mm') }}</view
        >

        <!-- 评价内容-未展开 -->
        <template v-if="!i.isOpen">
          <!-- 剧场点赞 -->
          <view
            class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
            v-if="[0, 1].includes(i.theaterKudos) && commentType === 1">
            <view class="flex w-full items-center justify-start">
              <template v-if="i.theaterKudos === 1">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp2.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >赞了 {{ i.theaterName || '-' }}</view
                >
              </template>
              <template v-else-if="i.theaterKudos === 0">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp4.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >踩了 {{ i.theaterName || '-' }}</view
                >
              </template>
            </view>
          </view>

          <!-- 剧目点赞 -->
          <view
            class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
            v-if="[0, 1].includes(i.repertoireKudos) && commentType === 2">
            <view class="flex w-full items-center justify-start">
              <template v-if="i.repertoireKudos === 1">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp2.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >赞了 {{ i.repertoireName || '-' }}</view
                >
              </template>
              <template v-else-if="i.repertoireKudos === 0">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp4.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >踩了 {{ i.repertoireName || '-' }}</view
                >
              </template>
            </view>
          </view>

          <view
            class="relative mb-2 line-clamp-2 w-fit break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
            >{{ i.text }}
          </view>
          <view
            class="mb-2.5 text-right font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-30"
            @tap="i.isOpen = true"
            >展开</view
          >
        </template>

        <!-- 评价内容-展开 -->
        <template v-else>
          <!-- 剧场点赞 -->
          <view
            class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
            v-if="[0, 1].includes(i.theaterKudos) && commentType === 1">
            <view class="flex w-full items-center justify-start">
              <template v-if="i.theaterKudos === 1">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp2.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >赞了 {{ i.theaterName || '-' }}</view
                >
              </template>
              <template v-else-if="i.theaterKudos === 0">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp4.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >踩了 {{ i.theaterName || '-' }}</view
                >
              </template>
            </view>
          </view>
          <!-- 剧目点赞 -->
          <view
            class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
            v-if="[0, 1].includes(i.repertoireKudos) && commentType === 2">
            <view class="flex w-full items-center justify-start">
              <template v-if="i.repertoireKudos === 1">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp2.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >赞了 {{ i.repertoireName || '-' }}</view
                >
              </template>
              <template v-else-if="i.repertoireKudos === 0">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp4.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >踩了 {{ i.repertoireName || '-' }}</view
                >
              </template>
            </view>
          </view>
          <u-parse
            class="break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
            :content="commentType === 1 ? i.theaterContent : i.content"></u-parse>

          <view class="m-auto mt-3 h-[2rpx] w-full bg-w-10"></view>

          <!-- 剧场点赞 -->
          <view
            class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
            v-if="[0, 1].includes(i.theaterKudos) && commentType === 2">
            <view class="flex w-full items-center justify-start">
              <template v-if="i.theaterKudos === 1">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp2.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >赞了 {{ i.theaterName || '-' }}</view
                >
              </template>
              <template v-else-if="i.theaterKudos === 0">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp4.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >踩了 {{ i.theaterName || '-' }}</view
                >
              </template>
            </view>
          </view>
          <!-- 剧目点赞 -->
          <view
            class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
            v-if="[0, 1].includes(i.repertoireKudos) && commentType === 1">
            <view class="flex w-full items-center justify-start">
              <template v-if="i.repertoireKudos === 1">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp2.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >赞了 {{ i.repertoireName || '-' }}</view
                >
              </template>
              <template v-else-if="i.repertoireKudos === 0">
                <image
                  class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                  :src="$iconFormat('icon/thumbsUp4.svg')"
                  mode="scaleToFill" />
                <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                  >踩了 {{ i.repertoireName || '-' }}</view
                >
              </template>
            </view>
          </view>
          <u-parse
            class="break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
            :content="commentType === 1 ? i.content : i.theaterContent"></u-parse>

          <view
            class="mb-2.5 text-right font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-30"
            @tap="i.isOpen = false"
            >收起</view
          >
        </template>

        <view class="mt-[10rpx] flex items-center justify-start">
          <text class="grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ i.createTime }}</text>

          <view class="flex items-center justify-end">
            <view
              class="mr-4 flex items-center justify-end"
              @tap="
                $push({
                  name: 'CommentDetail',
                  params: { id: i.id, commentType, theaterId, repertoireId },
                })
              ">
              <image
                class="mr-[6rpx] block h-[26rpx] w-[30rpx]"
                :src="$iconFormat('icon/reply.svg')"
                mode="scaleToFill" />
              <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ i.replyCount }}</text>
            </view>

            <view
              class="mr-4 flex items-center justify-end"
              @tap="handleKudos(i.id, i.kudosStatus == 1 ? undefined : 1)">
              <image
                class="mr-[6rpx] block h-[26rpx] w-[30rpx]"
                :src="$iconFormat('icon/thumbsUp2.svg')"
                mode="scaleToFill"
                v-if="i.kudosStatus == 1" />
              <image
                class="mr-[6rpx] block h-[26rpx] w-[30rpx]"
                :src="$iconFormat('icon/thumbsUp1.svg')"
                mode="scaleToFill"
                v-else />
              <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ i.likeCount }}</text>
            </view>

            <view
              class="mr-4 flex items-center justify-end"
              @tap="handleKudos(i.id, i.kudosStatus == 0 ? undefined : 0)">
              <image
                class="mr-[6rpx] block h-[26rpx] w-[30rpx]"
                :src="$iconFormat('icon/thumbsUp4.svg')"
                mode="scaleToFill"
                v-if="i.kudosStatus == 0" />
              <image
                class="mr-[6rpx] block h-[26rpx] w-[30rpx]"
                :src="$iconFormat('icon/thumbsUp3.svg')"
                mode="scaleToFill"
                v-else />
              <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ i.dislikeCount }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </template>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $commentInfoSave } from '@/api/base'
  import { useGlobalStore } from '@/stores/global'
  import { $checkKudos, $iconFormat, $picFormat, $push } from '@/utils/methods'
  import dayjs from 'dayjs'
  import _ from 'lodash'

  const emit = defineEmits(['updateCommentList'])
  const props = defineProps({
    theaterId: { type: Number, default: 0 }, // 剧场id
    repertoireId: { type: Number, default: 0 }, // 剧目id
    list: { type: Array, default: () => [] }, // 评论列表
    commentType: { type: Number, default: 1 }, // 评价类型 1：剧场  2：剧目
    hideMore: { type: Boolean, default: true }, // 影藏更多
  })

  const { userInfo } = storeToRefs(useGlobalStore())

  /* 赞踩 */
  const handleKudos = (commentId: number, type: any) => {
    uni.$u.throttle(() => {
      $commentInfoSave({ userId: userInfo.value.id, commentId, type }).then((res: any) => {
        let arr: any = _.cloneDeep(props.list)

        arr.map((i: any) => {
          if (i.id === commentId) {
            $checkKudos(i, type)
          }
        })
        emit('updateCommentList', arr)
      })
    }, 500)
  }
</script>

<style lang="scss" scoped>
  .evaluateItem {
    &:nth-last-child(2) {
      margin-bottom: 40rpx;
    }
  }
</style>
