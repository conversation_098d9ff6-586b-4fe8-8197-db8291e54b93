<template>
  <view class="flex h-[400rpx] w-fit">
    <view
      class="mr-2.5 h-[400rpx] w-[640rpx] shrink-0 rounded-[20rpx] bg-w-10 p-2.5 last:mr-0"
      :key="i.id"
      @tap="$push({ name: 'DynamicDetail', params: { id: i.id, type } })"
      v-for="i in list as any">
      <!-- 标题 -->
      <view class="mb-[10rpx] line-clamp-1 font-Medium text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
        i.title
      }}</view>
      <!-- 简介 -->
      <view
        class="mb-[10rpx] line-clamp-3 h-[120rpx] break-all font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
        >{{ i.body }}</view
      >
      <!-- 图片 -->
      <view class="mb-[10rpx] flex h-[140rpx] items-center justify-start">
        <u-album
          class="albums"
          :urls="i.images"
          @tap.stop.prevent
          maxCount="4"
          multipleMode="aspectFill"
          multipleSize="140rpx"
          rowCount="4"
          singleMode="aspectFill"
          singleSize="140rpx"
          space="14rpx"
          v-if="i.images.length > 1"></u-album>

        <u-image
          class="banner"
          :src="i.images[0]"
          @tap="$previewImage(i.images, 0)"
          bgColor="transparent"
          height="140rpx"
          mode="aspectFill"
          radius="10rpx"
          v-else-if="i.images.length"
          width="140rpx"></u-image>
      </view>
      <view class="flex items-center justify-between">
        <!-- 发布时间 -->
        <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ i.createTime }}</text>

        <!-- 点赞 -->
        <view class="flex items-center justify-end" @tap.stop="handleKudos(i)" v-if="i.kudosStatus === 1 && type === 2">
          <image class="block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" />
          <text class="ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#FF8A03]">{{
            i.likeCount
          }}</text>
        </view>
        <view class="flex items-center justify-end" @tap.stop="handleKudos(i)" v-else-if="type === 2">
          <image class="block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/thumbsUp1.svg')" mode="scaleToFill" />
          <text class="ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
            i.likeCount
          }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $dynamicKudos } from '@/api/dynamic'
  import { useGlobalStore } from '@/stores/global'
  import { $iconFormat, $picFormat, $previewImage, $push } from '@/utils/methods'

  const emit = defineEmits(['update'])

  const props = defineProps({
    list: { type: Array, default: () => [] },
    type: { type: Number, default: 1 },
  })

  const { userInfo } = storeToRefs(useGlobalStore())

  /* 点赞 */
  const handleKudos = (i: any) => {
    $dynamicKudos({
      dynamicId: i.id,
      type: i.kudosStatus === 1 ? undefined : 1,
      userId: userInfo.value.id,
    }).then((res: any) => {
      emit('update')
    })
  }
</script>

<style lang="scss" scoped>
  .albums {
    &:deep(.u-album__row__wrapper) {
      overflow: hidden;
      border-radius: 10rpx;

      image {
        height: 140rpx !important;
        object-fit: cover;
      }
    }
  }
</style>
