<template>
  <swiper
    class="swiper h-[600rpx] w-full"
    :current="swiperIndex"
    @change="handleSwiperChange"
    circular
    nextMargin="116rpx"
    previousMargin="116rpx"
    v-if="list && list.length">
    <swiper-item
      class="swiperItem flex items-center justify-center opacity-50"
      :class="swiperClass(index)"
      :key="index"
      v-for="(item, index) in list as any">
      <view
        class="swiperCard relative h-[600rpx] w-[480rpx] overflow-hidden rounded-[20rpx] bg-bg"
        @tap="$push({ name: 'TheaterDetail', params: { id: item.id } })">
        <!-- 海报 -->
        <u-image
          :fade="false"
          :src="$picFormat(item.coverPicture)"
          bgColor="transparent"
          height="480rpx"
          mode="aspectFill"
          webp
          width="480rpx"></u-image>

        <image
          class="absolute left-[20rpx] top-[20rpx] z-10 block h-[50rpx] w-[80rpx]"
          :src="$iconFormat('icon/recommend.svg')"
          mode="scaleToFill"
          v-if="item.recommend === 1" />

        <!-- 剧场信息 -->
        <view class="flex w-full items-center justify-start p-[20rpx]">
          <view class="ml-[10rpx] mr-[10rpx] grow">
            <!-- 名称 -->
            <text class="mb-[8rpx] line-clamp-1 font-Medium text-[32rpx] font-medium text-w-100">{{
              item.name || '-'
            }}</text>

            <!-- 关注人数 -->
            <view class="otherInfo flex items-center justify-start">
              <image
                class="block h-[32rpx] w-[32rpx] opacity-60"
                :src="$iconFormat('icon/favouriteOn.svg')"
                v-if="item.fansFlag === 1" />
              <image class="block h-[32rpx] w-[32rpx] opacity-60" :src="$iconFormat('icon/favouriteOff.svg')" v-else />
              <text class="ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
                >{{ item.focusNumber || 0 }}关注</text
              >
            </view>
          </view>

          <!-- 关注按钮 -->
          <!-- <followBtn :followStatus="item.fansFlag === 1 ? true : false" @changeFollowStatus="(status: boolean) => handleSwitchFollow(item, status)" /> -->
        </view>
      </view>
    </swiper-item>
  </swiper>

  <view class="h-[600rpx] w-full" v-else></view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $userTreasureSave } from '@/api/base'
  import { useGlobalStore } from '@/stores/global'
  import { $iconFormat, $picFormat, $push } from '@/utils/methods'
  import _ from 'lodash'

  const { userInfo } = storeToRefs(useGlobalStore())

  const props = defineProps({
    list: { type: Array, required: true },
  })

  const swiperIndex = ref(0) // 当前轮播index

  /* swiper 滚动 */
  const handleSwiperChange = (e: any) => {
    swiperIndex.value = e.detail.current
  }

  /* swiper 样式 */
  const swiperClass = (index: number) => {
    const prevIndex = swiperIndex.value - 1 >= 0 ? swiperIndex.value - 1 : props.list.length - 1
    const nextIndex = swiperIndex.value + 1 < props.list.length ? swiperIndex.value + 1 : 0
    return {
      activeSwiper: swiperIndex.value == index,
      prevSwiper: prevIndex == index,
      nextSwiper: nextIndex == index && props.list.length > 2,
    }
  }

  /* 关注切换 */
  const handleSwitchFollow = (item: any, status: boolean) => {
    $userTreasureSave({
      userId: userInfo.value.id,
      merchantId: item.merchantId,
      theaterId: item.id,
    }).then((res: any) => {
      let obj = _.cloneDeep(item)
      _.set(item, 'fansFlag', status ? 1 : 0)
      obj.focusNumber = obj.focusNumber - 0
      let focusNumber = status ? obj.focusNumber + 1 : obj.focusNumber - 1
      _.set(item, 'focusNumber', focusNumber > 0 ? focusNumber.toString() : 0)
    })
  }
</script>

<style lang="scss" scoped>
  .swiper {
    .swiperItem {
      .swiperCard {
        transition: transform 0.5s;
      }
    }

    .activeSwiper {
      opacity: 1;
    }

    .prevSwiper,
    .nextSwiper {
      .swiperCard {
        transform: scale(0.9);
      }
    }

    .prevSwiper {
      justify-content: flex-end;

      .swiperCard {
        transform-origin: right center;
      }
    }

    .nextSwiper {
      justify-content: flex-start;

      .swiperCard {
        transform-origin: left center;
      }
    }
  }

  .emptyWrap {
    height: 600rpx;
  }
</style>
