<template>
  <view>
    <view
      class="wrepertoireItem relative mb-[16rpx] h-[460rpx] overflow-hidden rounded-[20rpx]"
      :key="index"
      @tap="$push({ name: 'RepertoireDetail', params: { id: i.id } })"
      v-for="(i, index) in list as any">
      <!-- 海报 -->
      <u-image
        class="postPic"
        :src="$picFormat(i.coverPicture)"
        bgColor="transparent"
        height="460rpx"
        mode="aspectFill"
        ref="bannerRef"
        webp
        width="100%"></u-image>

      <!-- 顶部遮罩 -->
      <view class="absolute left-0 top-0 z-10 h-[116rpx] w-full bg-gradient-to-t from-b-0 to-b-100 opacity-60"></view>

      <!-- 推荐 -->
      <image
        class="absolute left-[10rpx] top-[10rpx] z-10 block h-[50rpx] w-[80rpx]"
        :src="$iconFormat('icon/recommend.svg')"
        mode="scaleToFill"
        v-if="i.recommend === 1 && type === 1" />

      <!-- 好评率 -->
      <view
        class="rate absolute left-[10rpx] top-[10rpx] z-20 flex h-5 min-w-[110rpx] items-center justify-start rounded-es-[10rpx] rounded-se-[20rpx] rounded-ss-[10rpx] pl-2.5 pr-1"
        v-if="type !== 1">
        <image class="mr-1 block h-3 w-3 shrink-0" :src="$iconFormat('icon/thumbsUp0.svg')" mode="scaleToFill" />
        <text class="text-[22rpx] font-normal leading-[28rpx] text-w-100">{{ i.likeRatio || 0 }}%</text>
      </view>

      <!-- 赞与关注人数 -->
      <view class="absolute right-[16rpx] top-[14rpx] z-20 flex items-center justify-start">
        <image
          class="relative z-10 block h-[32rpx] w-[32rpx] opacity-60"
          :src="$iconFormat('icon/favouriteOn.svg')"
          v-if="i.fansFlag === 1" />
        <image
          class="relative z-10 block h-[32rpx] w-[32rpx] opacity-60"
          :src="$iconFormat('icon/favouriteOff.svg')"
          v-else />
        <text class="relative z-10 ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
          >{{ i.focusNumber || 0 }}关注</text
        >
      </view>

      <view
        class="infoBox absolute bottom-0 left-0 h-[120rpx] w-full bg-w-se pb-[24rpx] pl-[10rpx] pr-[10rpx] pt-[12rpx] backdrop-blur-[3px]">
        <!-- 好评率 -->
        <view
          class="rate absolute left-[10rpx] top-[-50rpx] z-20 flex h-5 min-w-[110rpx] items-center justify-start rounded-es-[10rpx] rounded-se-[20rpx] rounded-ss-[10rpx] pl-2.5 pr-1"
          v-if="type === 1">
          <image class="mr-1 block h-3 w-3 shrink-0" :src="$iconFormat('icon/thumbsUp0.svg')" mode="scaleToFill" />
          <text class="text-[22rpx] font-normal leading-[28rpx] text-w-100">{{ i.likeRatio || 0 }}%</text>
        </view>

        <!-- 剧目名称 -->
        <view class="line-clamp-2 font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100">{{
          i.name || '-'
        }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat, $picFormat, $push } from '@/utils/methods'
  import followBtn from '@/components/Btn/FollowBtn.vue'
  import { $userTreasureSave } from '@/api/base'
  import { useGlobalStore } from '@/stores/global'
  import _ from 'lodash'

  const emit = defineEmits(['updateList'])
  const props = defineProps({
    list: { type: Array, required: true }, // 列表数据
    type: { type: Number, default: 0 },
  })

  const app: any = getCurrentInstance()?.proxy
  const { userInfo } = storeToRefs(useGlobalStore())

  /* 关注切换 */
  const handleSwitchFollow = (i: any, status: boolean) => {
    $userTreasureSave({
      userId: userInfo.value.id,
      merchantId: i.merchantId,
      repertoireId: i.id,
    }).then((res: any) => {
      let arr: any = _.cloneDeep(props.list)

      arr.map((j: any) => {
        if (i.id == j.id) {
          if (status) {
            j.fansFlag = 1
            j.focusNumber++
          } else {
            j.fansFlag = 0
            if (j.focusNumber > 0) j.focusNumber--
          }
        }
      })

      emit('updateList', arr)
    })
  }
</script>

<style lang="scss" scoped>
  .wrepertoireItem {
    .rate {
      background: linear-gradient(270deg, rgb(255 165 36 / 60%) 0%, rgb(255 130 39 / 60%) 100%);
    }
  }
</style>
