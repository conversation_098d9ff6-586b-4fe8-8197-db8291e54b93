<template>
  <view class="relative rounded-full" :class="shadowCol" :style="style">
    <!-- 同意协议按钮 -->
    <button
      class="flex items-center justify-center whitespace-nowrap rounded-full border-0 font-Medium text-[34rpx] font-medium leading-6 outline-none active:top-[10rpx]"
      :class="colClass"
      :style="style"
      @agreeprivacyauthorization="handleAgree"
      openType="agreePrivacyAuthorization"
      v-if="agree">
      {{ text }}
    </button>
    <!-- 普通按钮 -->
    <button
      class="flex items-center justify-center whitespace-nowrap rounded-full border-0 font-Medium text-[34rpx] font-medium leading-6 outline-none active:top-[10rpx]"
      :class="colClass"
      :style="style"
      @tap="handleClick"
      v-else>
      <image class="mr-2.5 block h-4 w-4" :src="$iconFormat(icon)" mode="scaleToFill" v-if="icon" />
      {{ text }}
    </button>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat } from '@/utils/methods'

  const props = defineProps({
    icon: { type: String, default: '' }, // 按钮图标
    text: { type: String, default: '' }, // 按钮文案
    width: { type: [Number, String], default: 532 }, // 按钮宽
    height: { type: [Number, String], default: 80 }, // 按钮搞
    type: { type: [Number, String], default: 1 }, // 按钮类型
    agree: { type: Boolean, default: false }, // 是否是同意协议
  })
  const emit = defineEmits(['click', 'handleAgree'])

  /* 按钮大小样式 */
  const style = reactive({ width: props.width + 'rpx', height: props.height + 'rpx' })

  /* 按钮颜色样式 */
  const shadowCol = ref('')
  const colClass = ref('')

  watch(
    () => props.type,
    (val: any) => {
      switch (val - 0) {
        case 1:
          shadowCol.value = 'shadow-btnCol1O'
          colClass.value =
            'bg-gradient-to-r from-[#C0A5EB] to-[#938FDE] text-deepPurple shadow-btnCol1N active:shadow-btnCol1A foucs:shadow-btnCol1A'
          break
        case 2:
          shadowCol.value = 'shadow-btnCol2O'
          colClass.value =
            'bg-gradient-to-r from-[#D5D5D5] to-[#BDBDBD] text-gray shadow-btnCol1N active:shadow-btnCol1A foucs:shadow-btnCol1A'
          break
        case 3:
          shadowCol.value = 'shadow-btnCol3O'
          colClass.value =
            'bg-gradient-to-r from-[#F4CC8B] to-[#F7AB77] text-glod shadow-btnCol1N active:shadow-btnCol1A foucs:shadow-btnCol1A'
          break
        case 4:
          shadowCol.value = 'shadow-btnCol4O'
          colClass.value =
            'bg-gradient-to-r from-[#262454] to-[#262454] text-w-60 shadow-btnCol2N active:shadow-btnCol2A foucs:shadow-btnCol2A'
          break
      }

      colClass.value += ' transition-all duration-300 ease-in-out'
    },
    { immediate: true }
  )

  /* 同意协议 */
  const handleAgree = () => {
    setTimeout(() => {
      uni.$u.throttle(() => {
        emit('handleAgree')
      }, 500)
    }, 300)
  }

  /* 按钮点击 */
  const handleClick = () => {
    setTimeout(() => {
      uni.$u.throttle(() => {
        emit('click')
      }, 500)
    }, 300)
  }
</script>
