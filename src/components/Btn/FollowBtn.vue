<template>
  <u-button
    class="followBtn"
    type="primary"
    :customStyle="{ width, height, ...btnStyle2 }"
    :hairline="false"
    @tap.stop.prevent="emit('changeFollowStatus', false)"
    color="linear-gradient(270deg, #D5D5D5 0%, #DFDFDF 100%)"
    shape="circle"
    v-if="followStatus"
    >已关注</u-button
  >
  <u-button
    class="followBtn"
    type="primary"
    :customStyle="{ width, height, ...btnStyle1 }"
    :hairline="false"
    @tap.stop.prevent="emit('changeFollowStatus', true)"
    color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
    icon="plus"
    iconColor="#282656"
    shape="circle"
    v-else
    >关注</u-button
  >
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  const props = defineProps({
    followStatus: { type: Boolean, defalut: false }, // 是否关注
    width: { type: String, default: '130rpx' }, // 按钮宽
    height: { type: String, default: '48rpx' }, // 按钮高
  })

  const emit = defineEmits(['changeFollowStatus'])

  /* 关注按钮样式 */
  const btnStyle1 = ref({
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '26rpx',
    fontWeight: 500,
    padding: 0,
  })

  /* 已关注按钮样式 */
  const btnStyle2 = ref({
    border: '2rpx solid #ffffff',
    color: '#A1A1A1',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '26rpx',
    fontWeight: 500,
    padding: 0,
  })
</script>

<style lang="scss" scoped>
  .followBtn {
    flex-shrink: 0;
    white-space: nowrap;

    &:deep(.u-icon) {
      .u-icon__icon {
        margin-right: 10rpx;
        font-size: 18rpx !important;
        font-weight: bold !important;
      }
    }
  }
</style>
