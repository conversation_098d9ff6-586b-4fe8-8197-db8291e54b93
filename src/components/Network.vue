<template>
  <u-no-network
    class="networkWrap bg-gradient-to-b from-bgS to-bgE"
    :image="networkPic"
    @connected="connected"
    @disconnected="disconnected"
    @retry="retry"
    tips="哔嘟哔嘟~信号飞到外太空了~"
    zIndex="10080"></u-no-network>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import networkPic from '../static/image/empty/network.png'

  /* 用户点击页面的"重试"按钮时触发 */
  const retry = () => {}

  /* "重试"后，有网络触发 */
  const connected = () => {}

  /* "重试"后，无网络触发 */
  const disconnected = () => {}
</script>

<style lang="scss" scoped>
  .networkWrap {
    width: 100%;

    &:deep(.u-no-network__error-icon) {
      .u-icon__img {
        width: 496rpx !important;
        height: 514rpx !important;
      }
    }

    &:deep(.u-no-network__tips) {
      margin-top: 78rpx;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 26rpx;
      font-weight: 400;
      line-height: 36rpx;
      color: #fff;
      opacity: 0.6;
    }

    &:deep(.u-no-network__retry) {
      width: 260rpx;
      height: 80rpx;
      margin-top: 80rpx;

      @apply rounded-full shadow-btnCol1O #{!important};

      .u-button {
        @apply flex h-full w-full items-center justify-center rounded-full border-0 bg-gradient-to-r from-[#C0A5EB] to-[#938FDE]  shadow-btnCol1N outline-none  #{!important};

        .u-button__text {
          @apply font-Medium text-[34rpx] font-medium leading-6 text-deepPurple  #{!important};
        }

        &:focus {
          @apply shadow-btnCol1A #{!important};
        }

        &:active {
          @apply active:top-[10rpx] active:shadow-btnCol1A #{!important};
        }
      }
    }
  }
</style>
