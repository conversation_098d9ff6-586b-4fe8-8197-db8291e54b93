<template>
  <view class="modelWrap" :style="{ width, height }">
    <canvas
      class="webgl block"
      id="gl"
      type="webgl"
      :style="{ width, height }"
      @touchend="onTX"
      @touchmove="onTX"
      @touchstart="onTX"></canvas>

    <l-painter
      :css="`width: 520rpx; height: ${backH}px; border-radius: 40rpx;`"
      :pixelRatio="2"
      @fail="handleGetBackPicFail"
      @success="handleGetBackPic"
      hidden
      isCanvasToTempFilePath
      pathType="url"
      v-if="backPic && backH">
      <l-painter-image :src="backPic" css="width: 100%; height: 100%; object-fit: cover; border-radius: 40rpx;" />
    </l-painter>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import {
    $cancelAnimationFrame,
    $requestAnimationFrame,
    $window,
    DirectionalLight,
    PerspectiveCamera,
    PLATFORM,
    Scene,
    sRGBEncoding,
    WebGL1Renderer,
    HemisphereLight,
  } from 'three-platformize'
  import * as THREE from 'three-platformize'
  import { WechatPlatform } from 'three-platformize/src/WechatPlatform'
  import { FBXLoader } from 'three-platformize/examples/jsm/loaders/FBXLoader'
  import { OrbitControls } from 'three-platformize/examples/jsm/controls/OrbitControls'
  import { $toast, $loading, $picFormat } from '@/utils/methods'
  import { $upload } from '@/api/base'

  const props = defineProps({
    width: { type: [Number, String], default: '100%' }, // 画布宽
    height: { type: [Number, String], default: '960rpx' }, // 画布高
    type: { type: String, default: 'model' }, // 模型类型 model: 模型 img: 图片
    url: { type: [String, Array], required: true }, // 模型地址
  })

  const app: any = getCurrentInstance()?.proxy

  const disposing = ref(false)
  const frameId = ref(-1)
  const rotateType = ref(0)

  let canvas: any // 画布 对象
  let platform = null as unknown as WechatPlatform

  let renderer: any // 渲染器
  let camera: any // 摄像机
  let scene: any // 场景
  let fbxLoader: any // fbx模型加载器
  let picLoader: any // 图片加载器
  let controls: any // 控制器
  let model: any // 模型

  const backPic = ref('')
  const backH = ref(0)
  const newBackPic = ref('')

  watch(
    () => props.url,
    (val: any) => {
      if (val.length === 2 && val[1]) {
        let pic = val[0] as any
        backPic.value = val[1] as any

        uni.getImageInfo({
          src: pic,
          success: (image: any) => {
            backH.value = (image.height / image.width) * uni.upx2px(520) || uni.upx2px(930)
          },
        })
      }
    },
    { immediate: true, deep: true }
  )

  onReady(() => {
    if (!backPic.value) {
      uni
        .createSelectorQuery()
        .in(app)
        .select('#gl')
        .node((res: any) => {
          canvas = res.node

          initCanva()
        })
        .exec()
    }
  })

  onUnload(() => {
    /* 使用完毕后释放资源 */
    disposing.value = false
    $cancelAnimationFrame(frameId.value)
    PLATFORM.dispose()
  })

  /* 3d 初始化 */
  const initCanva = async () => {
    $loading()

    platform = new WechatPlatform(canvas)
    PLATFORM.set(platform)

    /* 初始化 */
    renderer = new WebGL1Renderer({ canvas, antialias: true, alpha: true })
    camera = new PerspectiveCamera(75, canvas.width / canvas.height, 0.1, 1000)
    // camera = new PerspectiveCamera(45, canvas.width / canvas.height, 0.25, 100)
    scene = new Scene()
    fbxLoader = new FBXLoader()
    picLoader = new THREE.TextureLoader()
    controls = new OrbitControls(camera, canvas)

    /* render 渲染设置 */
    renderer.outputEncoding = sRGBEncoding
    renderer.gammaFactor = 2.2
    renderer.setSize(canvas.width, canvas.height)
    renderer.setPixelRatio($window.devicePixelRatio)

    /* 相机设置 */
    camera.position.set(0, 0, 10)
    camera.lookAt(new THREE.Vector3(0, 0, 0))

    /* 控制器设置 */
    controls.enableDamping = true
    controls.enablePan = false
    controls.enableZoom = false

    /* 光照设置 */
    scene.add(new DirectionalLight(0xffffff, 1.0))
    const hemiLight = new HemisphereLight(0xffffff, 0x444444)
    hemiLight.position.set(0, 200, 0)
    scene.add(hemiLight)
    const dirLight = new DirectionalLight(0xffffff)
    dirLight.position.set(0, 200, 100)
    dirLight.castShadow = true
    dirLight.shadow.camera.top = 180
    dirLight.shadow.camera.bottom = -100
    dirLight.shadow.camera.left = -120
    dirLight.shadow.camera.right = 120
    scene.add(dirLight)

    /* 加载模型 */
    switch (props.type) {
      /* 模型 */
      case 'model':
        // https://dtmall-tel.alicdn.com/edgeComputingConfig/upload_models/1591673169101/RobotExpressive.glb
        // props.url  https://techbrood.com/threejs/examples/models/fbx/Samba Dancing.fbx

        fbxLoader.load(
          props.url,
          /* 加载成功 */
          (fbx: any) => {
            model = fbx
            model.traverse(function (child: any) {
              // @ts-ignore
              if (child.isMesh) {
                child.castShadow = true
                child.receiveShadow = true
              }
            })

            setScaleToFitSize()
            calcMeshCenter()

            uni.hideLoading()
          },
          /* 加载中 */
          (params: any) => {},
          /* 加载失败 */
          (err: any) => {
            uni.hideLoading()
            $toast(app, '模型加载出错')
          }
        )
        break
      /* 电子票 */
      case 'ticket':
        try {
          // 创建几何模型 BoxGeometry('x轴', 'y轴', 'z轴')

          let w: any = (1 / canvas.height) * uni.upx2px(560)
          let h
          let pic: any = props.url[0]

          uni.getImageInfo({
            src: pic,
            success: (image: any) => {
              h = (1 / canvas.height) * ((image.height / image.width) * uni.upx2px(560) || uni.upx2px(1000))

              const geometry: any = new THREE.BoxGeometry(w, h, 1 / canvas.height)
              // 创建纹理贴图  前后
              const texture0: any = picLoader.load(pic)
              const texture1: any = newBackPic.value ? picLoader.load(newBackPic.value) : undefined
              // 添加材质
              const material: any = [
                new THREE.MeshBasicMaterial({ color: 0x220000, transparent: true, opacity: 0 }),
                new THREE.MeshBasicMaterial({ color: 0x220000, transparent: true, opacity: 0 }),
                new THREE.MeshBasicMaterial({ color: 0x220000, transparent: true, opacity: 0 }),
                new THREE.MeshBasicMaterial({ color: 0x220000, transparent: true, opacity: 0 }),
                new THREE.MeshBasicMaterial({ map: texture0, side: THREE.DoubleSide }),
                texture1
                  ? new THREE.MeshBasicMaterial({ map: texture1, side: THREE.DoubleSide })
                  : new THREE.MeshBasicMaterial({ color: 0x9c75d3 }),
              ]

              // 创建网格对象
              const cube: any = new THREE.Mesh(geometry, material)

              model = cube

              setScaleToFitSize(cube)
              calcMeshCenter(cube)

              uni.hideLoading()
            },
            fail: (err: any) => {
              uni.hideLoading()
              $toast(app, '电子票加载出错')
            },
          })
        } catch (error) {
          uni.hideLoading()
          $toast(app, '电子票加载出错')
        }
        break
    }

    /* 画布渲染 */
    render()
  }
  /* 画布渲染 */
  const render = () => {
    if (!disposing.value) frameId.value = $requestAnimationFrame(render)

    switch (rotateType.value) {
      case 1:
        model.rotation.y += 0.01
        break
      case 2:
        model.rotation.y -= 0.01
        break
    }

    controls.update()
    renderer.render(scene, camera)
  }

  /* 模型大小自适应 */
  const setScaleToFitSize = (pic?: string) => {
    let obj = pic || model
    const boxHelper: any = new THREE.BoxHelper(obj)
    boxHelper.geometry.computeBoundingBox()
    const box: any = boxHelper.geometry.boundingBox
    const maxDiameter: any = Math.max(box.max.x - box.min.x, box.max.y - box.min.y, box.max.z - box.min.z)
    const scaleValue: any = (camera.position.z / maxDiameter) * (props.type == 'ticket' ? 1.3 : 1)
    obj.position.set(0, -10, 0) //模型摆放的位置
    obj.scale.set(scaleValue, scaleValue, scaleValue)
    scene.add(obj)
  }

  /* 居中模型 */
  const calcMeshCenter = (pic?: string) => {
    let obj = pic || model
    const box3: any = new THREE.Box3()
    box3.expandByObject(obj)
    const center: any = new THREE.Vector3()
    box3.getCenter(center)
    obj.position.x = obj.position.x - center.x
    obj.position.y = obj.position.y - center.y
    obj.position.z = obj.position.z - center.z
  }

  /* 触摸模型 */
  const onTX = (e: any) => {
    platform.dispatchTouchEvent(e)
  }

  /* 旋转模型 */
  const rotateModel = (type: number) => {
    rotateType.value = type
  }
  /* 停止旋转 */
  const rotateStop = () => {
    rotateType.value = 0
  }

  /* 获取生成的背面图片 */
  const handleGetBackPic = (val: any) => {
    $upload({ name: 'file', filePath: val }).then((res: any) => {
      newBackPic.value = $picFormat(res.fileName)

      uni
        .createSelectorQuery()
        .in(app)
        .select('#gl')
        .node((res: any) => {
          canvas = res.node

          initCanva()
        })
        .exec()

      uni.hideLoading()
    })
  }

  /* 数字头像生成失败 */
  const handleGetBackPicFail = (err: any) => {
    $toast(app, '生成电子票失败')
    uni.hideLoading()
  }

  defineExpose({ rotateModel, rotateStop })
</script>
