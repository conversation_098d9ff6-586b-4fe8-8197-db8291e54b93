<template>
  <view
    class="flex w-fit shrink-0 items-center justify-start overflow-hidden rounded-[4rpx] bg-w-100"
    :style="{ height: height + 'rpx' }"
    v-if="text && level && color">
    <view
      class="flex items-center justify-center pl-[10rpx] pr-[10rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100"
      :style="{ height: height + 'rpx', background: color }"
      >{{ text }}</view
    >
    <veiw
      class="box-border flex shrink-0 items-center justify-center border-[2rpx] border-l-0 border-solid bg-w-100 pl-[10rpx] pr-[10rpx] font-Regular text-[26rpx] font-normal leading-[36rpx]"
      :style="{ minWidth: height + 'rpx', height: height + 'rpx', color, borderColor: color }"
      >{{ level }}</veiw
    >
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  const props = defineProps({
    height: { type: String, default: '40' },
    text: { type: String, default: '' },
    level: { type: String, default: '' },
    color: { type: String, default: '' },
  })
</script>

<style lang="scss" scoped></style>
