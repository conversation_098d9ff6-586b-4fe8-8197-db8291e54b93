<template>
  <u-popup class="askPopup" :show="popupContaoller" @close="handleClose" bgColor="#1F1933" round="30rpx">
    <view class="m-auto w-[702rpx] pt-[30rpx]">
      <view
        class="mb-[22rpx] w-full text-center font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100"
        v-if="props.way == 1"
        >{{ way == 1 ? '我要提问' : '我要回答' }}
      </view>

      <!-- 剧场信息 -->
      <view
        class="mb-[10rpx] flex w-full items-center justify-start rounded-[20rpx] p-1 pr-[12rpx]"
        @tap="$push({ name: 'TheaterDetail', params: { id: detail.id } })"
        v-if="askType === 1 && detail && props.way == 2">
        <u-icon class="mr-2.5 shrink-0" name="arrow-left" color="#ffffff"></u-icon>

        <u-image
          class="shrink-0"
          :src="$picFormat(detail.coverPicture)"
          bgColor="transparent"
          height="80rpx"
          mode="aspectFill"
          radius="10rpx"
          width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Medium text-[24rpx] font-medium leading-[34rpx] text-w-100">{{
            detail.name || '-'
          }}</view>
          <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
            >地址:{{ detail.address || '-' }}</view
          >
        </view>
      </view>

      <!-- 剧目信息 -->
      <view
        class="mb-[10rpx] flex w-full items-center justify-start rounded-[20rpx] p-1 pr-[12rpx]"
        @tap="$push({ name: 'RepertoireDetail', params: { id: detail.id } })"
        v-else-if="askType === 2 && detail && props.way == 2">
        <u-icon class="mr-2.5 shrink-0" name="arrow-left" color="#ffffff"></u-icon>

        <u-image
          class="shrink-0"
          :src="$picFormat(detail.coverPicture)"
          bgColor="transparent"
          height="80rpx"
          mode="aspectFill"
          radius="10rpx"
          width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Medium text-[24rpx] font-medium leading-[34rpx] text-w-100">{{
            detail.name
          }}</view>
          <!-- <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60">演出时间: {{ '-' }} | 演出场地: {{ '-' }}</view> -->
        </view>
      </view>

      <view class="mb-[18rpx] h-[1rpx] w-full bg-w-10"></view>

      <!-- 提问内容 -->
      <view class="mb-[24rpx] font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100">{{
        way == 1 ? '提问内容' : '回答内容'
      }}</view>

      <!-- 提问内容 -->
      <u-form class="formWrap w-full" :model="formData" :rules="$askRules" labelWidth="0" ref="formRef">
        <u-form-item class="formItem" prop="content">
          <u-textarea
            class="commentBox"
            :placeholder="way == 1 ? '对于这个剧目我想问...问题越详细回答越准确~~' : '您的回答很重要…回答越详细越好哦~'"
            border="none"
            cursorSpacing="10"
            height="216rpx"
            maxlength="1000"
            v-model="formData.content"></u-textarea>
        </u-form-item>
      </u-form>

      <customBtn class="m-auto mt-0" @tap="handleSave" icon="icon/send.svg" text="发布" />
    </view>
  </u-popup>

  <u-toast ref="uToast" />
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $issueAdd, $issueReply } from '@/api/ask'
  import { $repertoireDetails2 } from '@/api/repertoire'
  import { $theaterDetails } from '@/api/theater'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $picFormat, $push, $toast } from '@/utils/methods'
  import { $askRules } from '@/utils/rules'

  const emit = defineEmits(['close'])
  const props = defineProps({
    popupContaoller: { type: Boolean, default: false }, // 弹窗控制器
    askId: { type: [Number, String], default: undefined }, // 提问id
    askType: { type: [Number], default: 1 }, // 提问类型 1：剧场 2：剧目
    infoId: { type: [Number, String], default: undefined }, // 剧目/剧场id
    way: { type: [Number, String], default: 1 }, // 1: 提问 2：回答
    replyUserId: { type: [Number, String], default: undefined }, // 提问人id
    replyId: { type: [Number, String], default: undefined }, // 回复id
  })

  const app: any = getCurrentInstance()?.proxy
  const { userInfo } = storeToRefs(useGlobalStore())

  const detail = ref<any>('') // 剧目、剧场详情

  /* 表单 */
  const formRef = ref()
  const formData = reactive({
    content: '', // 问题内容
  })

  watch(
    () => props.popupContaoller,
    (val) => {
      if (val) formData.content = ''
    }
  )

  watch(
    () => props.infoId,
    (val: any) => {
      if (val) {
        switch (props.askType) {
          case 1:
            $theaterDetails({ id: val, userId: userInfo.value.id }).then((res: any) => {
              detail.value = res.data
            })
            break
          case 2:
            $repertoireDetails2({ id: val, userId: userInfo.value.id }).then((res: any) => {
              detail.value = res.data
            })
            break
        }
      }
    },
    { immediate: true }
  )

  /* 关闭弹窗 */
  const handleClose = () => {
    emit('close')
  }

  /* 提交评论 */
  const handleSave = () => {
    formRef.value.validate().then((res: any) => {
      if (props.way == 1) {
        $issueAdd({
          merchantId: detail.value.merchantId || undefined,
          theaterId: props.askType === 1 ? props.infoId : undefined,
          repertoireId: props.askType === 2 ? props.infoId : undefined,
          userId: userInfo.value.id,
          content: formData.content,
        }).then((res: any) => {
          formData.content = ''
          $toast(app, '发布成功')
          emit('close', true)
          uni.$emit('updateAsk')
        })
      } else if (props.way == 2) {
        $issueReply({
          id: props.replyId || undefined,
          issue: {
            merchantId: detail.value.merchantId || undefined,
            theaterId: props.askType === 1 ? props.infoId : undefined,
            repertoireId: props.askType === 2 ? props.infoId : undefined,
            userId: userInfo.value.id,
            content: formData.content,
            replyId: props.replyUserId || undefined,
            parentId: props.askId || undefined,
          },
        }).then((res: any) => {
          formData.content = ''
          $toast(app, '回答成功')
          emit('close', true)
          uni.$emit('updateAsk')
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .askPopup {
    flex-grow: 0 !important;
  }

  .formWrap {
    margin-bottom: 80rpx;

    .formItem {
      margin: 0;

      &:deep(.u-form-item__body) {
        padding: 0 !important;

        .commentBox {
          background: rgb(7 1 29 / 20%) !important;
        }
      }
    }
  }
</style>
