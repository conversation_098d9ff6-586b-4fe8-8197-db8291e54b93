<template>
  <u-popup class="askPopup" :show="popupContaoller" @close="handleClose" bgColor="#1F1933" round="30rpx">
    <view class="m-auto flex h-[75vh] w-[702rpx] flex-col items-start justify-start pt-[30rpx]">
      <view class="relative w-full">
        <view class="mb-[22rpx] w-full text-center font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100"
          >全部{{ askdetail.replyCount || 0 }}个回答</view
        >
        <image
          class="absolute bottom-0 right-0 top-0 m-auto block h-6 w-6"
          :src="$iconFormat('icon/close2.svg')"
          @tap="handleClose"
          mode="scaleToFill" />
      </view>

      <view class="mb-[18rpx] h-[1rpx] w-full shrink-0 bg-w-10"></view>

      <!-- 剧场信息 -->
      <view
        class="m-auto mb-2.5 flex w-[682rpx] shrink-0 items-start justify-start rounded-[20rpx] bg-deepbg pb-1 pl-[10rpx] pr-[10rpx] pt-1"
        @tap="$push({ name: 'TheaterDetail', params: { id: selAsk.theaterId } })"
        v-if="selAsk.theaterId">
        <u-image
          class="shrink-0"
          :src="detail.coverPicture"
          bgColor="transparent"
          height="80rpx"
          mode="aspectFill"
          radius="10rpx"
          width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{
            detail.name || '-'
          }}</view>
          <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
            >地址:{{ detail.address || '-' }}</view
          >
        </view>
      </view>
      <!-- 剧目信息 -->
      <view
        class="m-auto mb-2.5 flex w-[682rpx] shrink-0 items-center justify-start rounded-[20rpx] bg-deepbg pb-1 pl-[10rpx] pr-[10rpx] pt-1"
        @tap="$push({ name: 'RepertoireDetail', params: { id: selAsk.repertoireId } })"
        v-else-if="selAsk.repertoireId">
        <u-image
          class="shrink-0"
          :src="detail.coverPicture"
          bgColor="transparent"
          height="80rpx"
          mode="aspectFill"
          radius="10rpx"
          width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{
            detail.name || '-'
          }}</view>
          <!-- <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60">演出时间: {{ '-' }} | 演出场地:{{ '-' }}</view> -->
        </view>
      </view>

      <view class="w-full shrink-0 rounded-[20rpx] bg-deepbg2 pb-[18rpx] pl-2.5 pr-2.5 pt-[18rpx]">
        <!-- 提问 -->
        <view class="w-fuill flex items-start justify-start last:mb-0">
          <!-- 头像 -->
          <view class="relative mr-2.5 h-[60rpx] w-[60rpx] shrink-0">
            <u-avatar
              :defaultUrl="$iconFormat('avatar.jpg')"
              :src="$picFormat(selAsk.userMerchantId ? selAsk.merchantUserAvatar : selAsk.userAvatar)"
              size="60rpx"></u-avatar>
            <image
              class="absolute bottom-0 right-0 block h-4 w-4"
              :src="$iconFormat('icon/ask3.svg')"
              mode="scaleToFill" />
          </view>
          <!-- 问题信息 -->
          <view class="grow">
            <view class="mb-[4rpx] flex w-full items-center justify-start">
              <!-- 用户名 -->
              <text class="mr-1.5 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80">{{
                selAsk.userMerchantId ? selAsk.userName : selAsk.userName
              }}</text>
              <!-- 勋章 -->
              <medal :color="selAsk.rankMedalColor" :level="selAsk.rankMedalLevel" :text="selAsk.rankMedalName" />
            </view>
            <!-- 问题 -->
            <view class="mb-[4rpx] w-full break-all font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100">{{
              askdetail.content || '-'
            }}</view>
            <!-- 时间 -->
            <view class="w-full font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
              askdetail.createTime || '-'
            }}</view>
          </view>
        </view>
      </view>

      <!-- 回答列表 -->
      <mescroll-uni
        class="block h-0 w-full grow"
        :down="downOpt1"
        :fixed="false"
        :up="upOpt3"
        @down="downCallback"
        @init="mescrollInit"
        @topclick="$topclick"
        @up="upCallback">
        <view
          class="mb-2.5 w-full border-b-[1rpx] border-solid border-w-10 pb-2.5 pt-2.5 last:mb-0"
          :key="i.id"
          v-for="i in list">
          <!-- 回答 -->
          <view class="w-fuill mb-2.5 flex items-start justify-start last:mb-0">
            <!-- 头像 -->
            <view class="relative mr-2.5 h-[60rpx] w-[60rpx] shrink-0">
              <u-avatar
                :defaultUrl="$iconFormat('avatar.jpg')"
                :src="$picFormat(i.userMerchantId ? i.merchantUserAvatar : i.userAvatar)"
                size="60rpx"></u-avatar>
              <image
                class="absolute bottom-0 right-0 block h-4 w-4"
                :src="$iconFormat('icon/ask4.svg')"
                mode="scaleToFill" />
            </view>
            <!-- 答案信息 -->
            <view class="grow">
              <view class="mb-[4rpx] flex w-full items-center justify-start">
                <!-- 用户名 -->
                <text class="mr-1.5 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80">{{
                  i.userMerchantId ? i.userName : i.userName
                }}</text>
                <!-- 勋章 -->
                <medal :color="i.rankMedalColor" :level="i.rankMedalLevel" :text="i.rankMedalName" />
              </view>
              <!-- 回答 -->
              <view
                class="mb-[4rpx] w-full break-all font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
                >{{ i.content }}</view
              >
              <!-- 时间 -->
              <view class="w-full font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
                i.createTime
              }}</view>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </u-popup>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $issueDetails, $issueListByParentId } from '@/api/ask'
  import { $repertoireDetails2 } from '@/api/repertoire'
  import { $theaterDetails } from '@/api/theater'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $hideName, $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const emit = defineEmits(['close'])
  const props = defineProps({
    popupContaoller: { type: Boolean, default: false }, // 弹窗控制器
    selAsk: { type: Object, default: () => {} }, // 查看详情的问答
  })

  const app: any = getCurrentInstance()?.proxy
  const { downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const { userInfo } = storeToRefs(useGlobalStore())

  const mescrollObj = ref() // 滚动obj

  const detail = ref<any>('')
  const askdetail = ref<any>('')
  const list = ref<any>([]) // 回答列表

  watch(
    () => props.popupContaoller,
    (val) => {
      if (val) mescrollObj.value.resetUpScroll()
    }
  )

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll
    if (props.selAsk.theaterId) {
      /* 剧场详情 */
      $theaterDetails({ id: props.selAsk.theaterId, userId: userInfo.value.id }).then((res: any) => {
        if (res.data.coverPicture) res.data.coverPicture = $picFormat(res.data.coverPicture)

        detail.value = res.data
      })
    } else if (props.selAsk.repertoireId) {
      /* 剧目详情 */
      $repertoireDetails2({ id: props.selAsk.repertoireId, userId: userInfo.value.id }).then((res: any) => {
        if (res.data.coverPicture) res.data.coverPicture = $picFormat(res.data.coverPicture)

        detail.value = res.data
      })
    }

    /* 问题详情 */
    $issueDetails(props.selAsk.id).then((res: any) => {
      if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY/MM/DD')
      askdetail.value = res.data
    })

    /* 问题列表 */
    $issueListByParentId({
      parentId: props.selAsk.id,
      pageNum: mescroll.num,
      pageSize: mescroll.size,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.userName) i.userName = $hideName(i.userName)
          if (i.merchantUserName) i.merchantUserName = $hideName(i.merchantUserName)
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY/MM/DD')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 关闭弹窗 */
  const handleClose = () => {
    emit('close')
  }
</script>

<style lang="scss" scoped>
  .askPopup {
    flex-grow: 0 !important;
  }

  .formWrap {
    margin-bottom: 80rpx;

    .formItem {
      margin: 0;

      &:deep(.u-form-item__body) {
        padding: 0 !important;
      }
    }
  }
</style>
