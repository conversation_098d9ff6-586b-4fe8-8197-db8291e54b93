<template>
  <view class="w-full overflow-hidden bg-transparent" :style="borderH">
    <swiper class="h-[50vw] w-full" v-if="show">
      <swiper-item
        class="flex w-full flex-wrap justify-center"
        :class="[key == list.length - 1 ? 'lastbox' : '']"
        :key="key"
        v-for="(item, key) in list">
        <view
          class="flex h-[12.5vw] w-[12.5vw] items-center justify-center"
          :key="index"
          @click="selEmoji(i)"
          v-for="(i, index) in item">
          <text>{{ i }}</text>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import emoji from '@/utils/emoji.js'
  const emit = defineEmits(['emoji'])

  const props = defineProps({
    show: { type: Boolean, default: true },
    keyboardH: { type: Number, default: 0 },
  })

  const list = ref<any>([])
  const borderH = computed(() => {
    let styleTxt = ''

    switch (true) {
      case props.keyboardH > 0:
        styleTxt = 'height:' + props.keyboardH + 'px'
        break
      case props.show:
        styleTxt = 'height: 50vw;'
        break
      default:
        styleTxt = 'height: 0vw;'
        break
    }

    return styleTxt
  })

  onMounted(() => {
    var page = Math.ceil(emoji.length / 32)

    for (let i = 0; i < page; i++) {
      list.value[i] = []
      for (let k = 0; k < 32; k++) {
        emoji[i * 32 + k] ? list.value[i].push(emoji[i * 32 + k]) : ''
      }
    }
  })

  /* 选中表情 */
  const selEmoji = (e: any) => {
    emit('emoji', e)
  }
</script>

<style lang="scss" scoped>
  .lastbox {
    justify-content: flex-start;
  }
</style>
