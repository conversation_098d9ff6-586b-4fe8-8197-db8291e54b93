<template>
  <u-popup class="aiSearchPopup" :safeAreaInsetBottom="false" :show="props.show" @close="handleClose" bgColor="#1F1933"
    round="30rpx">
    <view class="m-auto w-[702rpx] pb-[60rpx] pt-[30rpx]">
      <!-- 标题 -->
      <view class="mb-[30rpx] flex items-center justify-between">
        <view class="flex items-center">
          <text class="fas fa-robot mr-2" style="font-size: 40rpx; color: #fff;"></text>
          <text class="font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">智能搜索</text>
        </view>
        <u-icon name="close" @click="handleClose" color="#ffffff" size="28rpx"></u-icon>
      </view>

      <!-- 问题输入区 -->
      <view class="mb-[30rpx]">
        <u-form class="formWrap w-full" :model="formData" ref="formRef">
          <u-form-item class="formItem" prop="question">
            <u-textarea class="questionBox" border="none" cursorSpacing="10" height="160rpx" maxlength="200"
              placeholder="请输入你想了解的剧目问题，比如：6月有哪些好看的音乐剧？" showWordLimit v-model="formData.question">
            </u-textarea>
          </u-form-item>
        </u-form>
      </view>

      <!-- 快速问题模板 -->
      <view class="mb-[30rpx]">
        <view class="mb-[16rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80">推荐问题：</view>
        <view class="flex flex-wrap items-center">
          <view class="mb-2 mr-2 rounded-full bg-w-10 px-3 py-2" :key="index" @tap="selectTemplate(template)"
            v-for="(template, index) in questionTemplates">
            <text class="font-Regular text-[24rpx] font-normal text-w-100">{{ template }}</text>
          </view>
        </view>
      </view>



      <!-- 搜索历史 -->
      <view class="mb-[30rpx]" v-if="searchHistory.length">
        <view class="mb-[16rpx] flex items-center justify-between">
          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80">搜索历史：</text>
          <text class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-50" @tap="clearHistory">清空</text>
        </view>
        <view class="max-h-[200rpx] overflow-y-auto">
          <view class="mb-2 flex items-center justify-between rounded-[12rpx] bg-w-5 px-3 py-2" :key="index"
            @tap="selectHistory(history)" v-for="(history, index) in searchHistory.slice(0, 5)">
            <text class="flex-1 font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-80">{{ history }}</text>
            <text class="fas fa-history" style="font-size: 24rpx; color: rgba(255,255,255,0.6);"></text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="flex space-x-4">
        <view class="flex-1">
          <u-button :customStyle="cancelButtonStyle" @click="handleClose" text="取消"> </u-button>
        </view>
        <view class="flex-1">
          <u-button :customStyle="confirmButtonStyle" :disabled="!formData.question.trim()" :loading="isAnalyzing"
            @click="handleSearch" text="开始搜索">
          </u-button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat } from '@/utils/methods'

  const props = defineProps({
    show: { type: Boolean, default: false },
  })

  const emit = defineEmits(['close', 'search'])

  // 表单数据
  const formRef = ref()
  const formData = reactive({
    question: '',
  })

  // 分析状态
  const isAnalyzing = ref(false)

  // 问题模板
  const questionTemplates = ref([
    '6月有哪些好看的演出？',
    '上海有什么推荐音乐会？',
    'SIX值得看吗？',
    '最近有什么话剧推荐？',
    '这个剧目口碑怎么样？',
    '周末有什么亲子剧？',
  ])

  // 搜索历史
  const searchHistory = ref([])

  // 按钮样式
  const cancelButtonStyle = reactive({
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    color: '#ffffff',
  })

  const confirmButtonStyle = reactive({
    backgroundColor: '#8B5CF6',
    borderColor: '#8B5CF6',
    color: '#ffffff',
  })

  onMounted(() => {
    // 加载搜索历史
    loadSearchHistory()
  })

  watch(
    () => props.show,
    (val) => {
      if (val) {
        // 弹窗打开时重置表单
        formData.question = ''
        isAnalyzing.value = false
      }
    }
  )

  // 加载搜索历史
  const loadSearchHistory = () => {
    const history = uni.getStorageSync('aiSearchHistory') || []
    searchHistory.value = history
  }

  // 保存搜索历史
  const saveSearchHistory = (question) => {
    let history = uni.getStorageSync('aiSearchHistory') || []

    // 移除重复项
    history = history.filter((item) => item !== question)

    // 添加到开头
    history.unshift(question)

    // 限制历史记录数量
    if (history.length > 10) {
      history = history.slice(0, 10)
    }

    uni.setStorageSync('aiSearchHistory', history)
    searchHistory.value = history
  }

  // 选择模板
  const selectTemplate = (template) => {
    formData.question = template
  }

  // 选择历史记录
  const selectHistory = (history) => {
    formData.question = history
  }

  // 清空历史
  const clearHistory = () => {
    uni.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          uni.removeStorageSync('aiSearchHistory')
          searchHistory.value = []
        }
      },
    })
  }

  // 关闭弹窗
  const handleClose = () => {
    emit('close')
  }

  // 开始搜索
  const handleSearch = () => {
    const question = formData.question.trim()
    if (!question) {
      uni.$u.toast('请输入问题')
      return
    }

    isAnalyzing.value = true

    // 保存搜索历史
    saveSearchHistory(question)

    // 模拟分析延迟
    setTimeout(() => {
      isAnalyzing.value = false
      emit('search', question)
      handleClose()
    }, 1000)
  }
</script>

<style lang="scss" scoped>
  .aiSearchPopup {
    flex-grow: 0 !important;
  }

  .formWrap {
    .formItem {
      margin: 0;

      &:deep(.u-form-item__body) {
        padding: 0 !important;

        .questionBox {
          background: rgba(7, 1, 29, 0.3) !important;
          border-radius: 16rpx;
          padding: 20rpx;
        }
      }
    }
  }
</style>
