<template>
  <mescroll-uni
    class="block h-0 w-screen grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt3"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="w-full pt-[20rpx]">
      <!-- 瀑布流 -->
      <uv-waterfall
        :addTime="10"
        @changeList="handleChangeList"
        columnCount="2"
        columnGap="14rpx"
        columnWidth="344rpx"
        leftGap="24rpx"
        ref="waterfall"
        rightGap="24rpx"
        v-if="list && list.length > 0"
        v-model="list">
        <!-- 第一列数据 -->
        <template #list1>
          <waterfallItem :list="list1" @updateList="(list) => (list1 = list)" />
        </template>

        <!-- 第二列数据 -->
        <template #list2>
          <waterfallItem :list="list2" @updateList="(list) => (list2 = list)" />
        </template>
      </uv-waterfall>

      <!-- 空状态 -->
      <view class="emptyWrap2 w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/repertoire.svg')"
          height="426rpx"
          mode="data"
          text="哔嘟哔嘟~暂无剧目信息~"
          width="516rpx"></u-empty>
      </view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $topclick } from '@/utils/methods'
  import waterfallItem from '@/components/Item/WaterfallItem.vue'
  import { $repertoireListByPage } from '@/api/repertoire'
  import dayjs from 'dayjs'

  const props = defineProps({
    orderByColumn: { type: String, default: undefined }, // 排序字段
    keyword: { type: String, default: undefined }, // 关键字搜索
  })

  const app: any = getCurrentInstance()?.proxy
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)
  const { userInfo, addressInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())

  const mescrollObj = ref() // 滚动obj

  const list = ref([]) // 数据表格
  const list1 = ref<any>([]) // 瀑布流左
  const list2 = ref<any>([]) // 瀑布流右

  watch(
    () => props.orderByColumn,
    () => {
      if (mescrollObj.value) mescrollObj.value.resetUpScroll()
    }
  )

  /* watch(
  () => props.keyword,
  () => {
    mescrollObj.value.resetUpScroll()
  }
) */

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    if (mescroll.num == 1 && app.$refs.waterfall) app.$refs.waterfall.clear()

    mescrollObj.value = mescroll

    $repertoireListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
      city: addressInfo.value.id,
      keyword: props.keyword,
      // orderByColumn: props.orderByColumn ? props.orderByColumn + ' desc,id' : undefined,
      orderByColumn: props.orderByColumn
        ? 'cityCount desc,' + props.orderByColumn + ' desc,lastTime desc,id'
        : 'createTime desc,id',
      // isAsc: 'desc',
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.createTime) i.createTime = dayjs(i.createTime).format('M月D日')
        })

        // 第一页需手动制空列表
        if (mescroll.num == 1) {
          list.value = []
          list1.value = []
          list2.value = []
        }

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 瀑布流接收列表数据 */
  const handleChangeList = (e: any) => {
    // 为列表数据赋值
    switch (e.name) {
      case 'list1':
        list1.value.push(e.value)
        break
      case 'list2':
        list2.value.push(e.value)
        break
    }
  }

  defineExpose({ mescrollObj })
</script>

<style lang="scss">
  :deep(.mescroll-wxs-content) {
    padding-bottom: 100rpx;
  }
</style>
