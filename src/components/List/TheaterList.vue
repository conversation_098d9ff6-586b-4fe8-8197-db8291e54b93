<template>
  <mescroll-uni
    class="block h-0 w-screen grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt3"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="listWrap w-full pl-3 pr-3 pt-2.5" v-if="list && list.length > 0">
      <view
        class="listItem mb-2.5 flex min-h-[280rpx] w-full items-start justify-start bg-fullAuto bg-no-repeat p-2.5 last:mb-0"
        :key="index"
        @tap="$push({ name: 'TheaterDetail', params: { id: item.id } })"
        v-for="(item, index) in list">
        <view class="relative mr-2.5 h-[240rpx] w-[240rpx] shrink-0 overflow-hidden rounded-[16rpx]">
          <!-- 海报 -->
          <u-image
            :src="$picFormat(item.coverPicture)"
            bgColor="transparent"
            height="240rpx"
            mode="aspectFill"
            width="240rpx"></u-image>

          <!-- 关注人数 -->
          <view class="absolute bottom-0 left-0 h-[64rpx] w-full bg-gradient-to-b from-b-0 to-b-100 opacity-60"></view>
          <view class="absolute bottom-[10rpx] left-[10rpx] flex items-center justify-start">
            <image
              class="mr-[4rpx] block h-4 w-4"
              :src="$iconFormat('icon/favouriteOn.svg')"
              mode="scaleToFill"
              v-if="item.fansFlag === 1" />
            <image
              class="mr-[4rpx] block h-4 w-4"
              :src="$iconFormat('icon/favouriteOff.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
              >{{ item.focusNumber }}人关注</text
            >
          </view>
        </view>

        <view class="grow pb-2.5 pt-2.5">
          <view class="mb-[12rpx] flex items-center justify-start">
            <!-- 名称 -->
            <text class="mr-2.5 line-clamp-1 grow font-Medium text-[32rpx] font-medium leading-[40rpx] text-w-100">{{
              item.name || '-'
            }}</text>

            <!-- 关注按钮 -->
            <!-- <followBtn
              :followStatus="item.fansFlag === 1 ? true : false"
              @changeFollowStatus="(status: boolean) => handleSwitchFollow(item, status)"
              height="48rpx"
              width="150rpx" /> -->
          </view>

          <!-- 地址 -->
          <view class="mb-[28rpx] flex items-center justify-start">
            <image class="block h-4 w-4 shrink-0" :src="$iconFormat('icon/address.svg')" mode="scaleToFill" />
            <text class="line-clamp-1 grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-80">{{
              item.address || '-'
            }}</text>
          </view>

          <!-- 简介 -->
          <view class="rounded-[16rpx] bg-b-20 pb-1 pl-[12rpx] pr-[12rpx] pt-1">
            <view class="line-clamp-2 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
              >“{{ item.remark || '-' }}”</view
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="emptyWrap2 w-full pt-[200rpx]" v-if="!list || !list.length">
      <u-empty
        :icon="$iconFormat('empty/repertoire.svg')"
        height="426rpx"
        mode="data"
        text="哔嘟哔嘟~暂无剧场信息~"
        width="516rpx"></u-empty>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $userTreasureSave } from '@/api/base'
  import { $theaterListByPage } from '@/api/theater'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'
  import _ from 'lodash'

  const props = defineProps({
    keyword: { type: String, default: undefined }, // 关键字搜索
  })

  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)
  const { userInfo, addressInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())

  const mescrollObj = ref()
  const list = ref<any>([]) // 数据表格

  /* watch(
  () => props.keyword,
  () => {
    if (mescrollObj.value) mescrollObj.value.resetUpScroll()
  }
) */

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $theaterListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
      keyword: props.keyword,
      orderByColumn: `(city_id = '${addressInfo.value.id}') desc,id`,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 关注切换 */
  const handleSwitchFollow = (item: any, status: boolean) => {
    $userTreasureSave({
      userId: userInfo.value.id,
      merchantId: item.merchantId,
      theaterId: item.id,
    }).then((res: any) => {
      let arr: any = _.cloneDeep(list.value)

      arr.map((j: any) => {
        if (item.id == j.id) {
          if (status) {
            j.fansFlag = 1
            j.focusNumber++
          } else {
            j.fansFlag = 0
            if (j.focusNumber > 0) j.focusNumber--
          }
        }
      })

      list.value = _.cloneDeep(arr)
    })
  }

  defineExpose({ mescrollObj })
</script>

<style lang="scss" scoped>
  .listWrap {
    .listItem {
      background-image: url($icon + 'background/listbg1.webp');
    }
  }

  :deep(.mescroll-wxs-content) {
    padding-bottom: 100rpx;
  }
</style>
