<template>
  <view class="countdownDisplay w-full px-6 mb-6">
    <!-- 主容器：包含开演倒计时和四个项目 -->
    <view class="mainCountdown rounded-[24rpx] bg-gradient-to-br from-purple-600 to-blue-600 p-4 relative overflow-hidden" @tap="handleSettingsClick">
      <!-- 背景装饰 -->
      <view class="absolute top-[-20rpx] right-[-20rpx] w-[120rpx] h-[120rpx] bg-white opacity-10 rounded-full"></view>
      <view class="absolute bottom-[-30rpx] left-[-30rpx] w-[80rpx] h-[80rpx] bg-white opacity-5 rounded-full"></view>

      <!-- 第一行：开演倒计时 -->
      <view v-if="props.mainCountdownData" class="relative z-10 flex items-center justify-between mb-4">
        <!-- 左侧：标题 -->
        <text class="font-Medium text-[26rpx] font-medium" style="color: #ffffff">开演倒计时</text>

        <!-- 中间：天数显示 -->
        <view class="flex items-baseline">
          <text class="font-Bold text-[48rpx] font-bold" style="color: #ffffff">{{ props.mainCountdownData.days }}</text>
          <text class="font-Regular text-[24rpx] font-normal ml-2" style="color: rgba(255, 255, 255, 0.9)">天</text>
        </view>

        <!-- 右侧：设置按钮 -->
        <text class="fas fa-cog" style="font-size: 24rpx; color: rgba(255, 255, 255, 0.7)"></text>
      </view>

      <!-- 空状态 -->
      <view v-else class="relative z-10 flex items-center justify-between mb-4">
        <text class="font-Medium text-[26rpx] font-medium" style="color: #ffffff">开演倒计时</text>
        <view class="flex items-center">
          <text class="fas fa-clock mr-2" style="font-size: 32rpx; color: rgba(255, 255, 255, 0.6)"></text>
          <text class="font-Regular text-[22rpx] font-normal" style="color: rgba(255, 255, 255, 0.7)">点击设置</text>
        </view>
        <text class="fas fa-cog" style="font-size: 24rpx; color: rgba(255, 255, 255, 0.7)"></text>
      </view>

      <!-- 第二行：四个项目并排显示（在同一个容器内） -->
      <view class="relative z-10 grid grid-cols-4 gap-2">
        <!-- 发薪项目 -->
        <view class="projectItem rounded-[12rpx] bg-black bg-opacity-30 p-2 text-center">
          <text class="font-Regular text-[18rpx] font-normal mb-1 block" style="color: #ffffff">发薪</text>
          <text class="font-Medium text-[22rpx] font-medium" style="color: #ffffff">{{ getProjectValue('salary') }}</text>
        </view>

        <!-- 开票项目 -->
        <view class="projectItem rounded-[12rpx] bg-black bg-opacity-30 p-2 text-center">
          <text class="font-Regular text-[18rpx] font-normal mb-1 block" style="color: #ffffff">开票</text>
          <text class="font-Medium text-[22rpx] font-medium" style="color: #ffffff">{{ getProjectValue('ticketTime') }}</text>
        </view>

        <!-- 下场项目 -->
        <view class="projectItem rounded-[12rpx] bg-black bg-opacity-30 p-2 text-center">
          <text class="font-Regular text-[18rpx] font-normal mb-1 block" style="color: #ffffff">下场</text>
          <text class="font-Medium text-[22rpx] font-medium" style="color: #ffffff">{{ getProjectValue('nextShow') }}</text>
        </view>

        <!-- 看剧基金项目 -->
        <view class="projectItem rounded-[12rpx] bg-black bg-opacity-30 p-2 text-center">
          <text class="font-Regular text-[18rpx] font-normal mb-1 block" style="color: #ffffff">基金</text>
          <text class="font-Medium text-[22rpx] font-medium" style="color: #ffffff">{{ getProjectValue('fund') }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
export default {
  name: 'CountdownDisplay'
}
</script>

<script lang="ts" setup>
interface CountdownData {
  title: string
  days: number
  description: string
  iconClass: string
}

interface ProjectData {
  [key: string]: {
    value: number | string
    unit: string
    enabled: boolean
  }
}

// Props 使用默认值
const props = withDefaults(
  defineProps<{
    mainCountdownData?: CountdownData | null
    projectsData?: ProjectData
  }>(),
  {
    mainCountdownData: null,
    projectsData: () => ({})
  }
)

// 获取项目显示值
const getProjectValue = (projectKey: string) => {
  const project = props.projectsData?.[projectKey]
  if (!project || !project.enabled) {
    return '-'
  }

  if (projectKey === 'fund') {
    return `${project.value}${project.unit}`
  }

  return `${project.value}${project.unit}`
}

// 点击设置按钮
const handleSettingsClick = () => {
  uni.navigateTo({
    url: '/pages/sub/account/settings'
  })
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.countdownDisplay {
  .mainCountdown {
    min-height: 140rpx;
    box-shadow: 0 8rpx 32rpx rgba(124, 58, 237, 0.3);

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }

    .projectItem {
      min-height: 60rpx;

      &:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
      }
    }
  }
}
</style>
