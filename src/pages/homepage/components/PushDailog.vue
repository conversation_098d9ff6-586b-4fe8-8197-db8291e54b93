<template>
  <u-popup :show="popupController" @close="handleClose" bgColor="#302A45" mode="center" round="40rpx">
    <view class="w-[690rpx] pb-[70rpx] pt-[94rpx]" v-if="pushList.length">
      <!-- 等级徽章 -->
      <template v-if="pushList[oIndex].rankMedalResponse">
        <medal
          class="m-auto"
          :color="pushList[oIndex].rankMedalResponse.color"
          :level="pushList[oIndex].rankMedalResponse.rankMedalLevel"
          :text="pushList[oIndex].rankMedalResponse.rankMedalName" />
      </template>
      <!-- 纪念勋章 -->
      <template v-else-if="pushList[oIndex].souvenirBadge">
        <u-image
          class="m-auto w-fit"
          :src="$picFormat(pushList[oIndex].souvenirBadge.coverPicture)"
          height="360rpx"
          mode="aspectFill"
          width="360rpx"></u-image>
      </template>

      <view
        class="mb-[10rpx] mt-[60rpx] w-full text-center font-Medium text-[40rpx] font-medium leading-[56rpx] text-w-100"
        >恭喜你</view
      >
      <view
        class="mb-[80rpx] w-full text-center font-Regular text-[36rpx] font-normal leading-[50rpx] text-w-100"
        v-if="pushList[oIndex].rankMedalResponse"
        >获得{{
          pushList[oIndex].rankMedalResponse.repertoireName || pushList[oIndex].rankMedalResponse.theaterName
        }}的等级勋章</view
      >
      <view
        class="mb-[80rpx] w-full text-center font-Regular text-[36rpx] font-normal leading-[50rpx] text-w-100"
        v-else-if="pushList[oIndex].souvenirBadge"
        >获得{{
          pushList[oIndex].souvenirBadge.repertoireName || pushList[oIndex].souvenirBadge.theaterName
        }}的纪念徽章</view
      >

      <customBtn class="m-auto" @tap="handleGetMedal" text="立即领取" />
    </view>
  </u-popup>

  <u-toast ref="uToast" />
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $collectionSave, $userPushCollectionListByPage } from '@/api/scan'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import medal from '@/components/Medal.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $loading, $picFormat, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()

  const popupController = ref(false)
  const pushList = ref<any>('') // 推送列表
  const oIndex = ref(0) // 当前index

  onShow(() => {
    // handleGetPushList()
  })

  /* 获取推送列表 */
  const handleGetPushList = () => {
    if (globalStore.token) {
      $userPushCollectionListByPage({ pageNum: 1, pageSize: 9999999 }).then((res: any) => {
        pushList.value = res.data.rows

        if (pushList.value.length) popupController.value = true
        else popupController.value = false
      })
    }
  }

  /* 关闭弹窗 */
  const handleClose = () => {
    popupController.value = false
  }

  /* 领取纪念勋章 */
  const handleGetMedal = () => {
    $loading()

    let data = pushList.value[oIndex.value]
    let type = 0
    let relationId = 0
    let issuerName = undefined

    if (data.rankMedalResponse) {
      type = 4
      relationId = data.rankMedalResponse.id
    } else if (data.souvenirBadge) {
      type = 3
      relationId = data.souvenirBadge.id
      issuerName = data.souvenirBadge.issuerName
    }

    $collectionSave({
      id: data.id,
      theaterId: data.theaterId || undefined,
      repertoireId: data.repertoireId || undefined,
      badgeType: type,
      relationId: relationId,
      issuerName: issuerName,
    }).then((res: any) => {
      uni.hideLoading()

      if (oIndex.value < pushList.value.length - 1) oIndex.value++
      else popupController.value = false

      $toast(app, '领取成功')
    })
  }

  defineExpose({ handleGetPushList })
</script>

<style lang="scss" scoped></style>
