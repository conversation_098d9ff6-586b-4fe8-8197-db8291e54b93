<template>
  <u-popup :show="popupController" @close="handleClose" bgColor="#FFFFFF" mode="center" round="20rpx">
    <view class="h-[740rpx] w-[640rpx] pt-[40rpx]">
      <view class="mb-[60rpx] text-center font-Medium text-[34rpx] font-medium leading-[48rpx] text-b-100"
        >用户隐私保护提示</view
      >

      <view
        class="mb-[32rpx] w-full pl-[48rpx] pr-[48rpx] font-Regular text-[34rpx] font-normal leading-[48rpx] text-[#666666]">
        <text>感谢您使用本应用，您使用本应用的服务之前请仔细阅读并同意</text>
        <text class="text-blue" @tap.stop="handleLinkAgreement(1)">《用户协议》</text>
        <text class="text-blue" @tap.stop="handleLinkAgreement(2)">《隐私协议》</text>
        <text class="text-blue" @tap.stop="handleLinkAgreement(4)">《充值及虚拟产品协议》</text>
        <text
          >。当您点击同意并开始使用产品服务时，即表示你已理解并同意该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用相应服务。</text
        >
      </view>

      <customBtn class="m-auto" @handleAgree="handleAgree" agree text="同意并继续" />

      <view
        class="mt-[40rpx] h-[80rpx] w-full text-center text-[34rpx] leading-[80rpx] text-[#666666]"
        @click="handleClose"
        >不同意</view
      >
    </view>
  </u-popup>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import { $push } from '@/utils/methods'

  const emit = defineEmits(['handleAgree'])

  const popupController = ref(false)

  onMounted(() => {
    if (wx.canIUse('wx.getPrivacySetting')) {
      wx.getPrivacySetting({
        success: (res: any) => {
          if (res.needAuthorization) {
            // 需要弹出隐私协议
            popupController.value = true
          } else {
            // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
            emit('handleAgree')
          }
        },
      })
    } else {
      emit('handleAgree')
    }
  })

  /* 打开协议弹窗 */
  const handleLinkAgreement = (type: number) => {
    $push({ name: 'Agreement', params: { type } })
  }

  /* 关闭弹窗 */
  const handleClose = () => {
    popupController.value = false
  }

  /* 同意协议 */
  const handleAgree = () => {
    handleClose()
    emit('handleAgree')
  }
</script>

<style lang="scss" scoped></style>
