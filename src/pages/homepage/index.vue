<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <mescroll-uni
      class="relative block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @scroll="handleScroll"
      @topclick="$topclick"
      @up="upCallback">
      <!-- 遮罩层 -->
      <view class="absolute left-0 top-0 z-10 h-[220rpx] w-full bg-gradient-to-t from-b-0 to-b-100 opacity-50"></view>

      <!-- 导航栏 -->
      <u-navbar class="z-20 w-full shrink-0" :bgColor="leavelTop1 ? '#1B1629' : 'transparent'" leftIcon=" ">
        <template #left>
          <view class="flex items-center justify-start">
            <!-- 所在地址 -->
            <image
              class="block h-[48rpx] w-[48rpx]"
              :src="$iconFormat('icon/address.svg')"
              @tap="$push({ name: 'CitySelect' })" />
            <view
              class="line-clamp-1 w-[156rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100"
              @tap="$push({ name: 'CitySelect' })"
              >{{ addressInfo.name || '-' }}</view
            >

            <!-- 搜索 -->
            <view
              class="ml-2.5 flex h-[64rpx] w-[296rpx] items-center justify-start rounded-full bg-w-20 pl-2.5 pr-2.5"
              @tap="$push({ name: 'Search' })">
              <image class="mr-[6rpx] h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
              <view class="line-clamp-1 grow font-Regular text-[26rpx] font-normal leading-[34rpx] text-w-60"
                >输入剧目/剧场名搜索</view
              >
            </view>
          </view>
        </template>
      </u-navbar>

      <!-- 头部轮播图 -->
      <u-swiper
        class="swiperBg w-full"
        :list="advertisingPicture"
        @click="hanleLinkFn"
        autoplay
        circulars
        height="560rpx"
        indicator
        indicatorActiveColor="rgba(255, 255, 255, 0.8)"
        indicatorInactiveColor="rgba(255, 255, 255, 0.3)"
        indicatorStyle="bottom: 70rpx; right: 64rpx; top: initial; left: initial"
        v-if="advertisingPicture && advertisingPicture.length"></u-swiper>
      <view class="h-[560rpx] w-full" v-else></view>

      <!-- 推荐列表 -->
      <view class="relative z-10 mt-[-40rpx] w-full">
        <view class="innerWrap w-full rounded-[30rpx] bg-[#0c0815] bg-fullAuto bg-no-repeat pt-5">
          <!-- 推荐剧场 -->
          <view class="blockBox mb-[26rpx] w-full">
            <!-- 标题 -->
            <view class="mb-[50rpx] flex w-full items-center justify-between pl-3 pr-3">
              <image class="block h-5 w-20" :src="$iconFormat('text/title1.webp')" mode="scaleToFill" webp />

              <view class="flex items-center justify-end" @tap="$tab({ name: 'Tabulation', routeParams: { type: 1 } })">
                <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-lightPurple">查看更多</text>
                <image class="block h-4 w-4" :src="$iconFormat('arrow/right1.svg')" />
              </view>
            </view>

            <!-- 轮播 -->
            <theaterSwiper :list="theaterList" />
          </view>

          <!-- 推荐剧目 -->
          <view class="blockBox w-full">
            <!-- 标题 -->
            <view
              class="sticky left-0 top-0 z-30 flex w-full items-center justify-between pb-[44rpx] pl-3 pr-3 pt-[44rpx]"
              id="stickyTitle"
              :style="{ top: styickTop, background: leavelTop2 ? '#1B1629' : 'transparent' }">
              <image class="block h-5 w-20" :src="$iconFormat('text/title2.webp')" mode="scaleToFill" webp />

              <view class="typeTab flex items-center justify-end">
                <text
                  class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-50"
                  :class="{ active: orderByColumn == 'likeRatio' }"
                  @tap="handleSwitchOrderType('likeRatio')"
                  >好评率</text
                >
                <view class="line ml-[20rpx] mr-[20rpx] h-[22rpx] w-[2rpx] bg-w-50"></view>
                <text
                  class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-50"
                  :class="{ active: orderByColumn == 'focusNumber' }"
                  @tap="handleSwitchOrderType('focusNumber')"
                  >关注人数</text
                >
              </view>
            </view>

            <!-- 瀑布流 -->
            <uv-waterfall
              :addTime="10"
              :style="{ minHeight: listH }"
              @changeList="handleChangeList"
              columnCount="2"
              columnGap="14rpx"
              columnWidth="344rpx"
              leftGap="24rpx"
              ref="waterfall"
              rightGap="24rpx"
              v-if="repertoireList && repertoireList.length > 0"
              v-model="repertoireList">
              <!-- 第一列数据 -->
              <template #list1>
                <waterfallItem :list="repertoireList1" :type="1" @updateList="(list) => (repertoireList1 = list)" />
              </template>

              <!-- 第二列数据 -->
              <template #list2>
                <waterfallItem :list="repertoireList2" :type="1" @updateList="(list) => (repertoireList2 = list)" />
              </template>
            </uv-waterfall>

            <view class="min-h-screen w-full" v-else>
              <!-- 空状态 -->
              <view class="emptyWrap2 w-full pt-[200rpx]">
                <u-empty
                  :icon="$iconFormat('empty/repertoire.svg')"
                  height="426rpx"
                  mode="data"
                  text="哔嘟哔嘟~暂无剧目信息~"
                  width="516rpx"></u-empty>
              </view>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="Homepage" />
  </view>

  <!-- 用户隐私协议弹窗 -->
  <agreeDailog @handleAgree="autoLocation" />
  <!-- 领取纪念勋章/等级徽章弹窗 -->
  <pushDailog ref="pushDailogRef" />

  <!-- toast提示 -->
  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import agreeDailog from './components/AgreeDailog.vue'
  import pushDailog from './components/PushDailog.vue'
  import { $areaTree } from '@/api/area'
  import { $advertisingPicture } from '@/api/base'
  import { $repertoireListByIndex } from '@/api/repertoire'
  import { $theaterListByIndex } from '@/api/theater'
  import theaterSwiper from '@/components/Item/TheaterSwiper.vue'
  import waterfallItem from '@/components/Item/WaterfallItem.vue'
  import network from '@/components/Network.vue'
  import tabbar from '@/components/Tabbar.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $picFormat, $push, $tab, $topclick } from '@/utils/methods'
  import QQMapWX from '@jonny1994/qqmap-wx-jssdk'
  import dayjs from 'dayjs'

  let qqmapsdk: any

  const app: any = getCurrentInstance()?.proxy
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)
  const globalStore = useGlobalStore()
  const { userInfo, addressInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())

  const mescrollObj = ref() // 滚动obj
  const leavelTop1 = ref(false) // 是否离开顶部1
  const leavelTop2 = ref(false) // 是否离开顶部2
  const stickyTitleRef = ref()
  const scrollTop = ref(0) // 滚动高度

  const advertisingPicture = ref([]) // 广告轮播图

  const theaterList = ref([]) // 推荐剧场列表

  const orderByColumn = ref('likeRatio') // 剧目推荐类型
  const orderFlag = ref(false) // 是否是切换剧目列表排序
  const repertoireList = ref([]) // 推荐剧目列表
  const repertoireList1 = ref<any>([]) // 瀑布流左
  const repertoireList2 = ref<any>([]) // 瀑布流右

  /* 粘性定位top */
  const styickTop = computed(() => {
    return uni.$u.addUnit(uni.$u.sys().statusBarHeight + 44, 'px')
  })

  const listH = computed(() => {
    return uni.$u.addUnit(uni.$u.sys().safeArea.height - 94 - uni.upx2px(328), 'px')
  })

  onLoad(() => {
    // autoLocation()
  })

  onShow(() => {
    const query = uni.createSelectorQuery().in(app)
    stickyTitleRef.value = query.select('#stickyTitle')
  })

  /* 是否滚动 */
  const handleScroll = (mescroll: any) => {
    if (mescroll.scrollTop - scrollTop.value > 50 || mescroll.scrollTop - scrollTop.value < -50) {
      scrollTop.value = mescroll.scrollTop

      leavelTop1.value = mescroll.scrollTop >= uni.upx2px(432) - uni.$u.sys().statusBarHeight

      stickyTitleRef.value
        .boundingClientRect((data: any) => {
          leavelTop2.value = mescroll.scrollTop >= data.top + (uni.$u.sys().statusBarHeight + uni.upx2px(608))
        })
        .exec()
    }
  }

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    if (app.$refs.pushDailogRef) app.$refs.pushDailogRef.handleGetPushList()

    if (!orderFlag.value) {
      /* 获取广告轮播图 */
      advertisingPicture.value = []
      $advertisingPicture().then((res: any) => {
        res.data.map((i: any) => {
          if (i.url) i.link = i.url
          if (i.image) i.url = $picFormat(i.image)
        })
        advertisingPicture.value = res.data
      })

      /* 获取推荐剧场列表 */
      theaterList.value = []
      $theaterListByIndex({
        userId: userInfo.value.id,
        pageNum: 1,
        pageSize: 6,
        orderByColumn: 'recommend',
        isAsc: 'desc',
      }).then((res: any) => {
        theaterList.value = res.data.rows || []
      })
    }

    /* 获取推荐剧目列表 */
    if (mescroll.num == 1 && app.$refs.waterfall) app.$refs.waterfall.clear()
    $repertoireListByIndex({
      userId: userInfo.value.id,
      city: addressInfo.value.id,
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      orderByColumn: orderByColumn.value
        ? 'recommend desc,cityCount desc,' + orderByColumn.value + ' desc,lastTime desc,id'
        : 'recommend desc,createTime desc,id',
      // isAsc: 'desc',
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.createTime) i.createTime = dayjs(i.createTime).format('M月D日')
        })

        // 第一页需手动制空列表
        if (mescroll.num == 1) {
          repertoireList.value = []
          repertoireList1.value = []
          repertoireList2.value = []
        }
        repertoireList.value = repertoireList.value.concat(curPageData) //追加新数据

        orderFlag.value = false

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* banner 跳转 */
  const hanleLinkFn = (index: any) => {
    let item: any = advertisingPicture.value[index]

    if (item.link) {
      let testReg1 = new RegExp('/pages/sub/detail/theater')
      let testReg2 = new RegExp('/pages/sub/detail/repertoire')

      if (testReg1.test(item.link) || testReg2.test(item.link)) {
        uni.navigateTo({ url: item.link })
      }
    }
  }

  /* 推荐剧目切换排序类型 */
  const handleSwitchOrderType = (orderType: string) => {
    if (orderByColumn.value != orderType) orderByColumn.value = orderType
    else orderByColumn.value = ''

    orderFlag.value = true

    mescrollObj.value.resetUpScroll()
  }

  /* 瀑布流接收列表数据 */
  const handleChangeList = (e: any) => {
    // 为列表数据赋值
    switch (e.name) {
      case 'list1':
        repertoireList1.value.push(e.value)
        break
      case 'list2':
        repertoireList2.value.push(e.value)
        break
    }
  }

  /* 自动定位 */
  const autoLocation = () => {
    if (!globalStore.addressInfo || !globalStore.addressInfo.id) {
      uni.showLoading({ title: '定位中' })

      qqmapsdk = new QQMapWX({ key: 'N5ZBZ-W53KI-WSWGE-5X22E-Z46WH-OIFA5' })

      qqmapsdk.reverseGeocoder({
        success: (res: any) => {
          const adcode: number = res.result.ad_info.city_code?.replace(res.result.ad_info.nation_code, '')

          let isFoundInfo = false

          $areaTree()
            .then((res: any) => {
              res.data.map((i: any) => {
                if (i.id == adcode) {
                  isFoundInfo = true
                  globalStore.addressInfo = { id: i.id, name: i.name }
                  uni.setStorageSync('addressInfo', { id: i.id, name: i.name })
                }

                if (!isFoundInfo) {
                  i.children.map((j: any) => {
                    if (j.id == adcode) {
                      isFoundInfo = true
                      globalStore.addressInfo = { id: j.id, name: j.name }
                      uni.setStorageSync('addressInfo', { id: j.id, name: j.name })
                    }
                  })
                }
              })

              uni.hideLoading()

              if (!isFoundInfo) {
                uni.$u.toast('自动定位失败，请手动选择所在城市')
                uni.$u.sleep(2000).then(() => {
                  $push({ name: 'CitySelect' })
                })
              }
            })
            .catch(() => {
              uni.hideLoading()

              uni.$u.toast('自动定位失败，请手动选择所在城市')
              uni.$u.sleep(2000).then(() => {
                $push({ name: 'CitySelect' })
              })
            })
        },
        fail: (error: any) => {
          uni.hideLoading()

          uni.$u.toast('自动定位失败，请手动选择所在城市')
          uni.$u.sleep(2000).then(() => {
            $push({ name: 'CitySelect' })
          })
        },
      })
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

  .pageWrap {
    & > .u-navbar {
      &:deep(.u-navbar__content) {
        .u-navbar__content__left {
          padding: 0 0 0 24rpx;
        }
      }
    }

    &:deep(.mescroll-wxs-content) {
      padding-bottom: 100rpx;
    }

    .innerWrap {
      background-image: url($icon + 'background/homepage.webp');

      .blockBox {
        .typeTab {
          .active {
            font-family: PingFangSC-Medium, 'PingFang SC';
            font-size: 26rpx;
            font-weight: 500;
            line-height: 36rpx;
            color: #fff;
          }
        }
      }
    }
  }
</style>
