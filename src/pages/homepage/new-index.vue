<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start" :style="pageBackgroundStyle">
    <!-- 状态栏占位 -->
    <view class="statusBar w-full" :style="{ height: statusBarHeight + 'px' }"></view>

    <mescroll-uni
      class="relative block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @scroll="handleScroll"
      @topclick="$topclick"
      @up="upCallback">
      <view class="relative z-10 w-full" hover-class="none" hover-stop-propagation="false">
        <!-- 顶部一行：城市选择+AI搜索框 -->
        <view class="flex items-center justify-between w-full px-6 pt-4 mb-4">
          <!-- 左侧：城市选择 -->
          <view class="flex items-center" @tap="$push({ name: 'CitySelect' })">
            <text class="fas fa-map-marker-alt mr-2" :style="iconStyle"></text>
            <view class="line-clamp-1 w-[100rpx] font-Regular text-[26rpx] font-normal leading-[36rpx]" :style="primaryTextStyle">
              {{ addressInfo.name || currentCity }}
            </view>
          </view>
          <!-- 右侧：AI搜索框 -->
          <view class="flex h-[56rpx] items-center pl-4 pr-4 flex-1 ml-4" :style="searchBoxStyle" style="max-width: 70%" @tap="handleOpenAISearch">
            <text class="fas fa-search mr-[6rpx]" :style="searchIconStyle"></text>
            <view class="line-clamp-1 grow font-Regular text-[24rpx] font-normal leading-[34rpx]" :style="searchTextStyle">{{ searchPlaceholder }}</view>
          </view>
        </view>
      </view>

      <!-- 倒计时显示区域 -->
      <CountdownDisplay :mainCountdownData="mainCountdownData" :projectsData="projectsData" />

      <!-- 足迹广场 -->
      <view class="footprintSection relative z-10 w-full">
        <!-- 标题区域 -->
        <view :style="footprintHeaderStyle">
          <view class="flex items-center justify-between">
            <view class="flex items-center">
              <text class="fas fa-shoe-prints mr-2" :style="iconStyle"></text>
              <text class="font-Medium text-[32rpx] font-medium leading-[44rpx]" :style="primaryTextStyle">足迹广场</text>
            </view>
          </view>
        </view>

        <!-- 瀑布流布局 -->
        <uv-waterfall
          :addTime="10"
          :style="{ minHeight: listH }"
          @changeList="handleChangeFootprintList"
          columnCount="2"
          columnGap="14rpx"
          columnWidth="344rpx"
          leftGap="24rpx"
          ref="footprintWaterfall"
          rightGap="24rpx"
          v-if="footprintList && footprintList.length > 0"
          v-model="footprintList">
          <!-- 第一列数据 -->
          <template #list1>
            <footprintItem :list="footprintList1" @updateList="list => (footprintList1 = list)" />
          </template>

          <!-- 第二列数据 -->
          <template #list2>
            <footprintItem :list="footprintList2" @updateList="list => (footprintList2 = list)" />
          </template>
        </uv-waterfall>

        <view class="min-h-[400rpx] w-full" v-else>
          <!-- 空状态 -->
          <view class="emptyWrap2 w-full pt-[100rpx]">
            <view class="flex flex-col items-center justify-center">
              <text class="fas fa-shoe-prints mb-4" :style="emptyIconStyle"></text>
              <text class="font-Regular text-[28rpx] font-normal mb-2" :style="primaryTextStyle">暂无足迹内容</text>
              <text class="font-Regular text-[24rpx] font-normal" :style="secondaryTextStyle">快去看剧分享吧~</text>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="Homepage" />
  </view>

  <!-- AI搜索弹窗 -->
  <aiSearchDialog :show="aiSearchShow" @close="aiSearchShow = false" @search="handleAISearch" />

  <!-- toast提示 -->
  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
import CountdownDisplay from '../../components/CountdownDisplay.vue'
import aiSearchDialog from '../../components/Dialog/AISearchDialog.vue'
import footprintItem from '../../components/Item/FootprintItem.vue'
import network from '../../components/Network.vue'
import tabbar from '../../components/Tabbar.vue'
import { useGlobalStore } from '@/stores/global'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { $picFormat, $push, $topclick } from '@/utils/methods'

const app: any = getCurrentInstance()?.proxy
const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)
const globalStore = useGlobalStore()
const { addressInfo, downOpt1, upOpt3 } = storeToRefs(globalStore)

const mescrollObj = ref() // 滚动obj
const leavelTop1 = ref(false) // 是否离开顶部1
const scrollTop = ref(0) // 滚动高度
const statusBarHeight = ref(0) // 状态栏高度
const currentCity = ref('上海') // 当前城市

// AI搜索相关
const aiSearchShow = ref(false)
const searchPlaceholder = ref('AI为你分析推荐最适合的演出...')

// 推荐话题数组和循环索引
const recommendedTopics = ref(['AI为你分析推荐最适合的演出...', '6月有哪些好看的音乐剧？', '上海有什么推荐的话剧？', 'SIX音乐剧值得看吗？', '这个月有哪些亲子演出？', '周末有什么好看的演出？'])
const topicIndex = ref(0)
const topicTimer = ref()

// 倒计时相关数据
const mainCountdownData = ref<any>(null)
const projectsData = ref<any>({})

// 足迹广场模拟数据
const mockFootprintData = [
  {
    id: 1,
    coverPicture: 'https://picsum.photos/344/240?random=11',
    theaterName: '上海大剧院',
    repertoireName: '狮子王',
    showTime: '2024年6月15日 19:30',
    content: '今晚的《狮子王》真的太震撼了！舞台设计和服装都太精美了，孩子们看得目不转睛。',
    images: [], // 简化：移除详情图片
    createTime: '6月15日',
    imageHeight: '240rpx',
    likeCount: 128,
    commentCount: 23,
    userName: '戏剧爱好者',
    userAvatar: 'https://picsum.photos/100/100?random=101'
  },
  {
    id: 2,
    coverPicture: 'https://picsum.photos/344/320?random=12',
    theaterName: '北京天桥艺术中心',
    repertoireName: '芝加哥',
    showTime: '2024年6月14日 20:00',
    content: '《芝加哥》的爵士乐太棒了！演员们的表演也很到位，特别是女主角的演唱。',
    images: [], // 简化：移除详情图片
    createTime: '6月14日',
    imageHeight: '320rpx',
    likeCount: 95,
    commentCount: 18,
    userName: '音乐剧迷',
    userAvatar: 'https://picsum.photos/100/100?random=102'
  },
  {
    id: 3,
    coverPicture: 'https://picsum.photos/344/280?random=13',
    theaterName: '广州大剧院',
    repertoireName: '猫',
    showTime: '2024年6月13日 19:30',
    content: '经典音乐剧《猫》永远不会让人失望，每一首歌都是经典。',
    images: [],
    createTime: '6月13日',
    imageHeight: '280rpx',
    likeCount: 76,
    commentCount: 12,
    userName: '小猫咪',
    userAvatar: 'https://picsum.photos/100/100?random=103'
  },
  {
    id: 4,
    coverPicture: 'https://picsum.photos/344/260?random=14',
    theaterName: '深圳音乐厅',
    repertoireName: '歌剧魅影',
    showTime: '2024年6月12日 20:00',
    content: '《歌剧魅影》的舞台效果太震撼了！特别是那个大吊灯掉下来的场面。',
    images: [], // 简化：移除详情图片
    createTime: '6月12日',
    imageHeight: '260rpx',
    likeCount: 156,
    commentCount: 34,
    userName: '歌剧院幽灵',
    userAvatar: 'https://picsum.photos/100/100?random=104'
  },
  {
    id: 5,
    coverPicture: 'https://picsum.photos/344/300?random=15',
    theaterName: '杭州大剧院',
    repertoireName: '妈妈咪呀',
    showTime: '2024年6月11日 19:30',
    content: '轻松愉快的音乐剧，ABBA的歌曲太经典了，看完心情特别好！',
    images: [],
    createTime: '6月11日',
    imageHeight: '300rpx',
    likeCount: 89,
    commentCount: 16,
    userName: 'ABBA粉丝',
    userAvatar: 'https://picsum.photos/100/100?random=105'
  },
  {
    id: 6,
    coverPicture: 'https://picsum.photos/344/220?random=16',
    theaterName: '南京保利大剧院',
    repertoireName: '悲惨世界',
    showTime: '2024年6月10日 19:30',
    content: '《悲惨世界》真的是史诗级的音乐剧，每个角色都很饱满，故事也很感人。',
    images: [],
    createTime: '6月10日',
    imageHeight: '220rpx',
    likeCount: 123,
    commentCount: 28,
    userName: '音乐剧收藏家',
    userAvatar: 'https://picsum.photos/100/100?random=106'
  }
]

// 足迹广场
const footprintList = ref<any[]>([])
const footprintList1 = ref<any[]>([])
const footprintList2 = ref<any[]>([])

const listH = computed(() => {
  return uni.$u.addUnit(uni.$u.sys().safeArea.height - 300, 'px')
})

// 主题相关的计算属性 - 统一使用暗色模式样式
const pageBackgroundStyle = computed(() => {
  return {
    background: 'linear-gradient(180deg, #1E1733 0%, #0A0713 100%)'
  }
})

const primaryTextStyle = computed(() => {
  return {
    color: '#ffffff'
  }
})

const secondaryTextStyle = computed(() => {
  return {
    color: 'rgba(255, 255, 255, 0.6)'
  }
})

const iconStyle = computed(() => {
  return {
    fontSize: '40rpx',
    color: '#ffffff'
  }
})

const searchBoxStyle = computed(() => {
  return {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    backdropFilter: 'blur(10px)'
  }
})

const searchIconStyle = computed(() => {
  return {
    fontSize: '28rpx',
    color: 'rgba(255, 255, 255, 0.6)'
  }
})

const searchTextStyle = computed(() => {
  return {
    color: 'rgba(255, 255, 255, 0.6)'
  }
})

const emptyIconStyle = computed(() => {
  return {
    fontSize: '80rpx',
    color: 'rgba(255, 255, 255, 0.3)'
  }
})

// 新增：足迹广场标题区域样式
const footprintHeaderStyle = computed(() => {
  return {
    padding: '0 24rpx 16rpx 24rpx'
  }
})

onLoad(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0

  // 设置模拟地址信息
  if (!addressInfo.value.name) {
    addressInfo.value = { id: '310000', name: '上海' }
    currentCity.value = '上海'
  } else {
    currentCity.value = addressInfo.value.name
  }

  // 启动推荐话题循环
  startTopicRotation()

  // 加载倒计时数据
  loadCountdownData()
})

onShow(() => {
  // 页面显示时刷新倒计时数据（用户可能修改了设置）
  loadCountdownData()
})

onUnload(() => {
  // 清理定时器
  if (topicTimer.value) {
    clearInterval(topicTimer.value)
  }
})

// 启动推荐话题循环
const startTopicRotation = () => {
  topicTimer.value = setInterval(() => {
    topicIndex.value = (topicIndex.value + 1) % recommendedTopics.value.length
    searchPlaceholder.value = recommendedTopics.value[topicIndex.value]
  }, 3000) // 每3秒切换一次
}

/* 是否滚动 */
const handleScroll = (mescroll: any) => {
  if (mescroll.scrollTop - scrollTop.value > 50 || mescroll.scrollTop - scrollTop.value < -50) {
    scrollTop.value = mescroll.scrollTop
    leavelTop1.value = mescroll.scrollTop >= uni.upx2px(200)
  }
}

/* 数据加载 */
const upCallback = (mescroll: any) => {
  mescrollObj.value = mescroll

  Promise.all([loadFootprintData(mescroll)])
    .then(() => {
      // 数据加载完成
    })
    .catch(() => {
      mescroll.endErr()
    })
}

/* 加载足迹数据 */
const loadFootprintData = (mescroll: any) => {
  return new Promise(resolve => {
    if (mescroll.num == 1 && app.$refs.footprintWaterfall) {
      app.$refs.footprintWaterfall.clear()
    }

    // 模拟API延迟
    setTimeout(() => {
      const pageSize = mescroll.size || 10
      const startIndex = (mescroll.num - 1) * pageSize
      const endIndex = startIndex + pageSize
      const curPageData = mockFootprintData.slice(startIndex, endIndex)

      // 处理数据格式
      curPageData.forEach(item => {
        // 处理封面图片
        if (item.coverPicture) {
          item.coverPicture = $picFormat(item.coverPicture)
        }

        // 处理用户头像
        if (item.userAvatar) {
          item.userAvatar = $picFormat(item.userAvatar)
        }
      })

      if (mescroll.num == 1) {
        footprintList.value = []
        footprintList1.value = []
        footprintList2.value = []
      }
      footprintList.value = footprintList.value.concat(curPageData)

      mescroll.endBySize(curPageData.length, mockFootprintData.length)
      resolve(true)
    }, 500)
  })
}

/* 打开AI搜索 */
const handleOpenAISearch = () => {
  aiSearchShow.value = true
}

/* AI搜索处理 */
const handleAISearch = (question: string) => {
  // TODO: 调用AI分析API
  // 跳转到搜索结果页面
  $push({
    name: 'AISearchResult',
    params: { question: encodeURIComponent(question) }
  })
}

/* 瀑布流接收列表数据 */
const handleChangeFootprintList = (e: any) => {
  switch (e.name) {
    case 'list1':
      footprintList1.value.push(e.value)
      break
    case 'list2':
      footprintList2.value.push(e.value)
      break
  }
}

/* 加载倒计时数据 */
const loadCountdownData = () => {
  const settings = uni.getStorageSync('countdownSettings')
  if (!settings) {
    mainCountdownData.value = null
    projectsData.value = {}
    return
  }

  const showTimeCountdowns: any[] = []
  const now = new Date()

  // 倒计时配置映射
  const countdownConfigs = [
    {
      key: 'salary',
      title: '发薪日',
      iconClass: 'fa-dollar-sign'
    },
    {
      key: 'showTime',
      title: '距开演',
      iconClass: 'fa-clock'
    },
    {
      key: 'ticketTime',
      title: '开票提醒',
      iconClass: 'fa-ticket-alt'
    },
    {
      key: 'nextShow',
      title: '下场演出',
      iconClass: 'fa-calendar-alt'
    },
    {
      key: 'fund',
      title: '看剧基金',
      iconClass: 'fa-piggy-bank'
    }
  ]

  // 初始化项目数据
  const projects: any = {}

  // 处理每个启用的倒计时
  countdownConfigs.forEach(config => {
    const setting = settings[config.key]

    if (!setting || !setting.enabled) {
      projects[config.key] = { value: '-', unit: '', enabled: false }
      return
    }

    let countdownItem: any = {
      title: config.title,
      iconClass: config.iconClass,
      priority: 0
    }

    switch (config.key) {
      case 'salary':
        const dayOfMonth = setting.dayOfMonth || 25
        const currentYear = now.getFullYear()
        const currentMonth = now.getMonth()
        let salaryDate = new Date(currentYear, currentMonth, dayOfMonth)

        if (salaryDate <= now) {
          salaryDate = new Date(currentYear, currentMonth + 1, dayOfMonth)
        }

        const salaryDays = Math.ceil((salaryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        projects[config.key] = { value: salaryDays, unit: '天', enabled: true }
        break

      case 'showTime':
      case 'ticketTime':
      case 'nextShow':
        if (setting.datetime) {
          const targetDate = new Date(setting.datetime)
          if (targetDate > now) {
            const days = Math.floor((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

            // 将开演相关的倒计时作为主倒计时候选
            if (config.key === 'showTime') {
              countdownItem.days = days
              countdownItem.description = days === 0 ? '今天就是了！' : `还有${days}天`
              countdownItem.priority = days <= 1 ? 100 : days <= 7 ? 90 : 70
              showTimeCountdowns.push(countdownItem)
            }

            projects[config.key] = { value: days, unit: '天', enabled: true }
          } else {
            projects[config.key] = { value: '-', unit: '', enabled: false }
          }
        } else {
          projects[config.key] = { value: '-', unit: '', enabled: false }
        }
        break

      case 'fund':
        const current = Number(setting.current) || 0
        projects[config.key] = { value: current, unit: '元', enabled: true }
        break
    }
  })

  // 设置项目数据
  projectsData.value = projects

  // 按优先级排序，找到最紧急的开演倒计时作为主倒计时
  showTimeCountdowns.sort((a, b) => b.priority - a.priority)

  if (showTimeCountdowns.length > 0) {
    mainCountdownData.value = showTimeCountdowns[0]
  } else {
    mainCountdownData.value = null
  }
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.pageWrap {
  position: relative;

  &:deep(.mescroll-wxs-content) {
    padding-bottom: 100rpx;
  }

  .footprintSection {
    position: relative;
    z-index: 2;
  }
}
</style>
