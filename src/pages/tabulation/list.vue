<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar class="navbar w-full shrink-0" bgColor="transparent" leftIcon=" " placeholder>
      <template #left>
        <u-tabs
          class="tabBar"
          :activeStyle="activeStyle"
          :current="listType"
          :inactiveStyle="inactiveStyle"
          :list="tabList"
          :scrollable="false"
          @change="handleTabChange"
          lineColor="linear-gradient(90deg, #844FD2 0%, #4230C2 100%)"
          lineHeight="10rpx"
          lineWidth="80rpx" />
      </template>
    </u-navbar>

    <!-- 剧目搜索 -->
    <view
      class="searchWrap m-auto mt-[10rpx] flex h-8 w-[702rpx] shrink-0 items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5"
      v-if="listType === 0">
      <image class="mr-1 h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @clear="handleSearch1(true)"
        @confirm="handleSearch1"
        border="none"
        clearable
        placeholder="请输入剧目名称搜索"
        v-model="keyword1" />
      <view class="ml-2.5 shrink-0 font-Regular text-base font-normal leading-[44rpx] text-purple" @tap="handleSearch1"
        >搜索</view
      >
    </view>
    <!-- 剧场搜索 -->
    <view
      class="searchWrap m-auto mt-[10rpx] flex h-8 w-[702rpx] shrink-0 items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5"
      v-else-if="listType === 1">
      <image class="mr-1 h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @clear="handleSearch2(true)"
        @confirm="handleSearch2"
        border="none"
        clearable
        placeholder="请输入剧场名称搜索"
        v-model="keyword2" />
      <view class="ml-2.5 shrink-0 font-Regular text-base font-normal leading-[44rpx] text-purple" @tap="handleSearch2"
        >搜索</view
      >
    </view>

    <!-- 剧目 -->
    <template v-if="listType === 0">
      <!-- 排序切换 -->
      <view class="typeTab mb-[10rpx] mt-[28rpx] flex w-full shrink-0 items-center justify-start pl-3 pr-3">
        <text
          class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-50"
          :class="{ active: orderByColumn === 'likeRatio' }"
          @tap="handleSwitchOrderType('likeRatio')"
          >好评率</text
        >
        <view class="line ml-[20rpx] mr-[20rpx] h-[22rpx] w-[2rpx] bg-w-50"></view>
        <text
          class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-50"
          :class="{ active: orderByColumn === 'focusNumber' }"
          @tap="handleSwitchOrderType('focusNumber')"
          >关注人数</text
        >
      </view>
      <!-- 剧目瀑布流 -->
      <repertoireList :keyword="keyword1" :orderByColumn="orderByColumn" ref="repertoireListRef" />
    </template>

    <!-- 剧场 -->
    <theaterList :keyword="keyword2" ref="theaterListRef" v-else-if="listType === 1" />

    <!-- 底部导航 -->
    <tabbar name="Tabulation" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import repertoireList from '@/components/List/RepertoireList.vue'
  import theaterList from '@/components/List/TheaterList.vue'
  import network from '@/components/Network.vue'
  import tabbar from '@/components/Tabbar.vue'
  import { $iconFormat } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy

  const tabList = ref([{ name: '剧目' }, { name: '剧场' }]) // tab
  const listType = ref(0) // 列表类型

  /* tab未激活样式 */
  const inactiveStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '28rpx',
    fontFamily: 'PingFangSC-Regular, PingFang SC',
    fontWeight: 400,
    color: 'rgb(255 255 255 / 60%)',
    lineHeight: '40rpx',
  })
  /* tab激活样式 */
  const activeStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '40rpx',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontWeight: 500,
    color: '#ffffff',
    lineHeight: '56rpx',
  })

  const keyword1 = ref(undefined) // 剧目关键字搜索
  const keyword2 = ref(undefined) // 剧场关键字搜索

  const orderByColumn = ref('likeRatio') // 剧目推荐类型

  const repertoireListRef = ref() // 剧目列表ref
  const theaterListRef = ref() // 剧场列表ref

  onLoad((params: any) => {
    if (params.type) listType.value = params.type
  })

  onShow(() => {
    if (app.$routeParams && app.$routeParams.type) listType.value = app.$routeParams.type
  })

  /* tab栏切换 */
  const handleTabChange = (e: any) => {
    listType.value = e.index
  }

  /* 推荐剧目切换排序类型 */
  const handleSwitchOrderType = (orderType: string) => {
    // orderByColumn.value = orderType
    if (orderByColumn.value != orderType) orderByColumn.value = orderType
    else orderByColumn.value = ''
  }

  /* 剧目搜索 */
  const handleSearch1 = (flag?: boolean) => {
    if (!keyword1.value && !flag) return
    nextTick(() => {
      repertoireListRef.value.mescrollObj.resetUpScroll()
    })
  }

  /* 剧场搜索 */
  const handleSearch2 = (flag?: boolean) => {
    if (!keyword2.value && !flag) return
    nextTick(() => {
      theaterListRef.value.mescrollObj.resetUpScroll()
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .navbar {
      &:deep(.u-navbar__content__left) {
        padding-left: 10rpx;
      }
    }

    .tabBar {
      &:deep(.u-tabs__wrapper) {
        .u-tabs__wrapper__nav {
          &__item {
            position: relative;
            z-index: 2;
            width: 120rpx;
            padding: 0;
          }

          &__line {
            bottom: 14rpx !important;
            z-index: 1;
          }
        }
      }
    }

    .typeTab {
      .active {
        font-family: PingFangSC-Medium, 'PingFang SC';
        font-size: 26rpx;
        font-weight: 500;
        line-height: 36rpx;
        color: #fff;
      }
    }
  }
</style>
