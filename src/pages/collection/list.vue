<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar class="navbar w-full shrink-0" bgColor="transparent" leftIcon=" " placeholder>
      <template #left>
        <u-tabs
          class="tabBar"
          :activeStyle="activeStyle"
          :current="listType"
          :inactiveStyle="inactiveStyle"
          :list="tabList"
          :scrollable="false"
          @change="handleSwitchTab"
          lineColor="linear-gradient(90deg, #844FD2 0%, #4230C2 100%)"
          lineHeight="10rpx"
          lineWidth="110rpx" />
      </template>
    </u-navbar>

    <!-- 搜索 -->
    <view
      class="searchWrap m-auto mb-5 mt-[10rpx] flex h-8 w-[702rpx] shrink-0 items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
      <image class="mr-1 h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        :placeholder="listType === 2 ? '请输入剧场名称搜索' : '请输入剧目名称搜索'"
        @clear="handleSearch"
        @confirm="handleSearch"
        border="none"
        clearable
        v-model="keyword" />
      <view class="ml-2.5 shrink-0 font-Regular text-base font-normal leading-[44rpx] text-purple" @tap="handleSearch"
        >搜索</view
      >
    </view>

    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="m-auto flex w-[702rpx] flex-wrap items-start justify-between" v-if="list && list.length">
        <collectionItem :list="list" />
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap mtPatch2 w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/collection.webp')"
          :text="`哔嘟哔嘟~暂无${tabList[listType].name}~`"
          height="385rpx"
          mode="data"
          width="487rpx"></u-empty>
      </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="CollectionList" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import collectionItem from './components/CollectionItem.vue'
  import { $avatarList, $souvenirBadgeList, $ticketList } from '@/api/collention'
  import network from '@/components/Network.vue'
  import tabbar from '@/components/Tabbar.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'
  import isBetween from 'dayjs/plugin/isBetween'

  dayjs.extend(isBetween)

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const tabList = ref([{ name: '电子票' }, { name: '数字头像' }, { name: '纪念徽章' }]) // tab
  const listType = ref(0) // 列表类型

  /* tab未激活样式 */
  const inactiveStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '28rpx',
    fontFamily: 'PingFangSC-Regular, PingFang SC',
    fontWeight: 400,
    color: 'rgb(255 255 255 / 60%)',
    lineHeight: '40rpx',
  })
  /* tab激活样式 */
  const activeStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '40rpx',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontWeight: 500,
    color: '#ffffff',
    lineHeight: '56rpx',
  })

  const keyword = ref('') // 搜索关键字

  const mescrollObj = ref() // 滚动obj
  const list = ref([]) // 藏品列表

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    switch (listType.value) {
      /* 电子票 */
      case 0:
        $ticketList({
          pageNum: mescroll.num,
          pageSize: mescroll.size,
          keyword: keyword.value || undefined,
        })
          .then((res: any) => {
            const curPageData = res.data.rows || [] // 当前页数据

            if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

            curPageData.map((i: any) => {
              i.badgeType = listType.value + 1
              i.soldOut = dayjs().isBefore(dayjs(i.startTime)) ? -1 : i.soldOut
              i.soldOut = dayjs().isAfter(dayjs(i.endTime)) ? 1 : i.soldOut
            })

            list.value = list.value.concat(curPageData) //追加新数据

            mescroll.endBySize(curPageData.length, res.data.total)
          })
          .catch((err: any) => {
            mescroll.endErr() // 请求失败, 结束加载
          })
        break
      /* 数字头像 */
      case 1:
        $avatarList({
          pageNum: mescroll.num,
          pageSize: mescroll.size,
          keyword: keyword.value || undefined,
        })
          .then((res: any) => {
            const curPageData = res.data.rows || [] // 当前页数据

            if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

            curPageData.map((i: any) => {
              i.badgeType = listType.value + 1
              i.soldOut = dayjs().isBefore(dayjs(i.startTime)) ? -1 : i.soldOut
              i.soldOut = dayjs().isAfter(dayjs(i.endTime)) ? 1 : i.soldOut
            })

            list.value = list.value.concat(curPageData) //追加新数据
            mescroll.endBySize(curPageData.length, res.data.total)
          })
          .catch((err: any) => {
            mescroll.endErr() // 请求失败, 结束加载
          })
        break
      /* 纪念勋章 */
      case 2:
        $souvenirBadgeList({
          pageNum: mescroll.num,
          pageSize: mescroll.size,
          keyword: keyword.value || undefined,
        })
          .then((res: any) => {
            const curPageData = res.data.rows || [] // 当前页数据

            if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

            curPageData.map((i: any) => {
              i.badgeType = listType.value + 1
              i.soldOut = dayjs().isBefore(dayjs(i.startTime)) ? -1 : i.soldOut
              i.soldOut = dayjs().isAfter(dayjs(i.endTime)) ? 1 : i.soldOut
            })

            list.value = list.value.concat(curPageData) //追加新数据

            mescroll.endBySize(curPageData.length, res.data.total)
          })
          .catch((err: any) => {
            mescroll.endErr() // 请求失败, 结束加载
          })
        break
    }
  }

  /* 切换tab */
  const handleSwitchTab = (e: any) => {
    listType.value = e.index

    if (mescrollObj.value) mescrollObj.value.resetUpScroll()
  }

  /* 搜索 */
  const handleSearch = () => {
    if (mescrollObj.value) mescrollObj.value.resetUpScroll()
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .navbar {
      &:deep(.u-navbar__content__left) {
        padding-left: 10rpx;
      }
    }

    .tabBar {
      &:deep(.u-tabs__wrapper) {
        .u-tabs__wrapper__nav {
          &__item {
            position: relative;
            z-index: 2;
            width: 160rpx;
            padding: 0;
          }

          &__line {
            bottom: 14rpx !important;
            z-index: 1;
          }
        }
      }
    }

    .typeTab {
      .active {
        font-family: PingFangSC-Medium, 'PingFang SC';
        font-size: 26rpx;
        font-weight: 500;
        line-height: 36rpx;
        color: #fff;
      }
    }

    :deep(.mescroll-wxs-content) {
      padding-bottom: 100rpx;
    }
  }
</style>
