<template>
  <view
    class="mb-2.5 h-[520rpx] w-[344rpx] overflow-hidden rounded-[20rpx] bg-w-10"
    :key="index"
    @tap="
      $push({
        name: 'CollectionDetail',
        params: {
          id: i.id,
          relationId: i.relationId,
          portfolioId: i.portfolioId,
          badgeType: i.badgeType,
        },
      })
    "
    v-for="(i, index) in list as any">
    <view class="relative">
      <u-image
        :src="$picFormat(i.coverFront || i.commonImage)"
        bgColor="transparent"
        height="344rpx"
        mode="aspectFill"
        v-if="i.badgeType === 1"
        width="344rpx"></u-image>
      <u-image
        :src="$picFormat(i.overlayImage || i.commonImage)"
        bgColor="transparent"
        height="344rpx"
        mode="aspectFill"
        v-else-if="i.badgeType === 2"
        width="344rpx"></u-image>
      <u-image
        :src="$picFormat(i.coverPicture)"
        bgColor="transparent"
        height="344rpx"
        mode="aspectFill"
        v-else-if="i.badgeType === 3"
        width="344rpx"></u-image>

      <!-- 未发售 -->
      <view
        class="absolute left-2.5 top-2.5 z-10 flex h-[48rpx] items-center justify-center rounded-full bg-gradient-to-r from-[#FFC680] to-[#EB8929] pl-[10rpx] pr-[10rpx]"
        v-if="i.soldOut == -1">
        <image class="mt-[4rpx] block h-[26rpx] w-[28rpx]" :src="$iconFormat('icon/over.svg')" mode="scaleToFill" />
        <view class="ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100">未发售</view>
      </view>
      <!-- 发放中 -->
      <view
        class="absolute left-2.5 top-2.5 z-10 flex h-[48rpx] items-center justify-center rounded-full bg-gradient-to-r from-[#A37BDD] to-[#4641A4] pl-[10rpx] pr-[10rpx]"
        v-if="i.soldOut == 0">
        <image class="mt-[4rpx] block h-[26rpx] w-[28rpx]" :src="$iconFormat('icon/doing.svg')" mode="scaleToFill" />
        <view class="ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100">发放中</view>
      </view>
      <!-- 已结束 -->
      <view
        class="absolute left-2.5 top-2.5 z-10 flex h-[48rpx] items-center justify-center rounded-full bg-b-80 pl-[10rpx] pr-[10rpx]"
        v-else-if="i.soldOut == 1">
        <image class="mt-[4rpx] block h-[26rpx] w-[28rpx]" :src="$iconFormat('icon/over.svg')" mode="scaleToFill" />
        <view class="ml-[4rpx] font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100">已结束</view>
      </view>

      <!-- 类型与限量 -->
      <view class="absolute bottom-[10rpx] left-[20rpx] flex items-center justify-start">
        <view
          class="mr-1 rounded-[4rpx] bg-b-70 pl-1 pr-1 font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100"
          v-if="i.badgeType === 1"
          >升级票·限量{{ i.issuedQuantity }}份</view
        >
        <view
          class="mr-1 rounded-[4rpx] bg-b-70 pl-1 pr-1 font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100"
          v-else-if="i.badgeType === 2"
          >升级数字头像·限量{{ i.issuedQuantity }}份</view
        >
        <view
          class="mr-1 rounded-[4rpx] bg-b-70 pl-1 pr-1 font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100"
          v-else-if="i.badgeType === 3"
          >纪念勋章·限量{{ i.issuedQuantity }}份</view
        >
      </view>
    </view>

    <view class="w-full pb-[10rpx] pt-[10rpx]">
      <view
        class="mb-[4rpx] line-clamp-1 w-full pl-[22rpx] pr-[22rpx] font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100"
        v-if="[1, 2].includes(i.badgeType)"
        >{{ i.portfolioName || '-' }}</view
      >
      <view
        class="mb-[4rpx] line-clamp-1 w-full pl-[22rpx] pr-[22rpx] font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100"
        v-else-if="i.badgeType === 3"
        >{{ i.name || '-' }}</view
      >

      <view
        class="mb-[10rpx] w-full pl-[22rpx] pr-[22rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        >发行时间：{{ dayjs(i.startTime).format('YYYY.MM.DD') }}</view
      >

      <view class="flex items-center justify-start pl-[22rpx] pr-[16rpx]" v-if="[1, 2].includes(i.badgeType)">
        <u-image
          class="shrink-0"
          :src="$picFormat(i.repertoireCoverPicture)"
          bgColor="transparent"
          height="32rpx"
          mode="aspectFill"
          radius="32rpx"
          width="32rpx"></u-image>
        <view class="ml-[10rpx] line-clamp-2 font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100">{{
          i.repertoireName || '-'
        }}</view>
      </view>

      <view class="itemsCenter flex h-[64rpx] justify-start pl-[22rpx] pr-[16rpx]" v-else-if="i.badgeType === 3">
        <u-image
          class="shrink-0"
          :src="$picFormat(i.theaterCoverPicture)"
          bgColor="transparent"
          height="32rpx"
          mode="aspectFill"
          radius="32rpx"
          width="32rpx"></u-image>
        <view class="ml-[10rpx] line-clamp-2 font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100">{{
          i.theaterName || '-'
        }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat, $picFormat, $push } from '@/utils/methods'
  import dayjs from 'dayjs'

  const props = defineProps({
    list: { type: Array, default: () => [] },
  })
</script>

<style lang="scss" scoped></style>
