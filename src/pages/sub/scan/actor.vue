<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="演职人员"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#282142"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="w-full border-b border-solid border-w-10"></view>

    <!-- 添加演员信息 -->
    <view class="box-border w-full pb-[150rpx] pl-3 pr-3 pt-[18rpx]">
      <view
        class="flex h-10 w-full flex-wrap items-center justify-start"
        :class="index === actorInformationList.length - 1 ? 'mb-5' : 'mb-[28rpx]'"
        :key="index"
        v-for="(i, index) in actorInformationList">
        <u-input
          class="actorName h-full grow"
          border="none"
          placeholder="请输入演职人员姓名"
          v-model="i.actorInformation"></u-input>

        <image
          class="ml-2.5 block h-5 w-5 shrink-0"
          :src="$iconFormat('icon/del1.svg')"
          @click="handleDel(i.id, index)"
          mode="scaleToFill" />

        <u-line color="rgba(255, 255, 255, 0.2)" />
      </view>

      <view
        class="flex h-8 w-[240rpx] items-center justify-center rounded-[12rpx] bg-[#9C75D3]"
        @click="handleAddActor">
        <u-icon name="plus" color="#ffffff" size="26rpx"></u-icon>
        <text class="ml-[6rpx] font-Medium text-[26rpx] font-medium leading-[36rpx] text-w-100">添加演员信息</text>
      </view>
    </view>

    <!-- 保存 -->
    <customBtn class="!fixed bottom-10 left-0 right-0 m-auto" @tap="handleSave" text="保存" />
  </view>

  <!-- 提示 -->
  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userReceivingRecordsDelete, $userReceivingRecordsText, $userReceivingRecordsUpdate } from '@/api/ticket'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy

  const { titleStyle1 } = storeToRefs(useGlobalStore())

  const id = ref(0)

  const delIds = ref<any>([]) // 删除id

  const actorInformationList = ref<any>([
    {
      actorInformation: '',
    },
  ]) // 演员列表

  onLoad((params: any) => {
    if (params.actorInfo) actorInformationList.value = JSON.parse(params.actorInfo)
    if (params.id) {
      id.value = params.id
      handleGetDetail()
    }
  })

  const handleGetDetail = () => {
    $userReceivingRecordsText({ userReceivingRecordsId: id.value }).then((res: any) => {
      actorInformationList.value = res.data
    })
  }

  /* 添加演员 */
  const handleAddActor = () => {
    actorInformationList.value.push({
      actorInformation: '',
    })
  }

  /* 删除演员 */
  const handleDel = (id: any, index: number) => {
    if (id) delIds.value.push(id)
    actorInformationList.value.splice(index, 1)
  }

  /* 保存 */
  const handleSave = () => {
    let arr: any = []

    actorInformationList.value.map((i: any) => {
      if (i.actorInformation) {
        if (id.value) i.userReceivingRecordsId = id.value
        arr.push(i)
      }
    })

    if (delIds.value && delIds.value.length) {
      $userReceivingRecordsDelete(delIds.value.join(',')).then((res: any) => {
        // console.log('🚀 ~ $userReceivingRecordsDelete ~ res🚀', res)
      })
    }

    if (id.value) {
      $userReceivingRecordsUpdate(arr).then((res: any) => {
        $toast(app, '保存成功', () => {
          $back()
        })
      })
    } else {
      uni.$emit('updateActorInfo', [...arr])

      $back()
    }
  }
</script>

<style lang="scss" scoped>
  .actorName {
    &:deep(.u-input__content) {
      .u-input__content__field-wrapper {
        .input-placeholder {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 30rpx !important;
          font-weight: 400 !important;
          line-height: 42rpx !important;
          color: rgb(255 255 255 / 40%) !important;
        }

        .u-input__content__field-wrapper__field {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 32rpx !important;
          font-weight: 400 !important;
          line-height: 44rpx !important;
          color: #fff !important;
        }
      }
    }
  }
</style>
