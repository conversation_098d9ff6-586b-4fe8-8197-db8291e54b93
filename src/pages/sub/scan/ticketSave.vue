<template>
  <view class="pageWrap min-h-screen w-screen bg-deepbg bg-fullAuto bg-no-repeat pb-[180rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder />

    <view class="m-auto w-[560rpx]">
      <u-image :src="postImage" height="1186rpx" mode="aspectFill" width="560rpx"></u-image>
    </view>
  </view>

  <u-popup :closeOnClickOverlay="false" :overlay="false" :show="showMenu" round="30rpx">
    <view class="flex items-start justify-start pb-2.5 pl-[42rpx] pr-[42rpx] pt-2.5">
      <button class="btnDef mr-[80rpx] h-fit w-fit p-0" open-type="share">
        <view class="flex w-[104rpx] flex-col items-center justify-start">
          <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/wechat.svg')" mode="scaleToFill" />
          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">微信好友</text>
        </view>
      </button>

      <!-- <view class="mr-[80rpx] flex w-[104rpx] flex-col items-center justify-start">
        <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/friend.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">朋友圈</text>
      </view> -->

      <view class="flex w-[104rpx] flex-col items-center justify-start" @click="handleSavePost">
        <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/save.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">保存图片</text>
      </view>
    </view>
  </u-popup>

  <l-painter
    :pixelRatio="2"
    @fail="handleGetFail"
    @success="handleGetPost"
    css="width: 560rpx; height: 1186rpx"
    hidden
    isCanvasToTempFilePath
    pathType="url"
    v-if="ticketDetail.modelUrl">
    <!-- 电子票 -->
    <l-painter-image
      :css="`width: ${postW};height: 1000rpx; object-fit: contain; margin: 0 auto;`"
      :src="ticketDetail.modelUrl" />

    <!-- 小程序二维码 -->
    <l-painter-image
      :src="$iconFormat('qrcode.png')"
      css="width: 120rpx; height: 120rpx; object-fit: cover; margin: 20rpx auto 12rpx;" />

    <!-- 提示 -->
    <l-painter-text
      :text="`长按识别二维码，来${wechatInfo.wechatName}看演出`"
      css="width: 100%; text-align: center; color: #FFFFFF; line-height: 34rpx; font-weight: normal; font-size: 24rpx;" />
  </l-painter>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $getWechatSetting } from '@/api/base'
  import { $collectionDetails } from '@/api/scan'
  import network from '@/components/Network.vue'
  import { $back, $iconFormat, $loading, $picFormat, $savePic, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy

  const showMenu = ref(true)

  const id = ref() // 电子票id
  const upgradeStatus = ref() // 是否是升级票
  const ticketDetail = ref<any>('') // 电子票详情
  const wechatInfo = ref<any>('') // 二维码信息

  const postW = ref('560rpx') // 海报宽度
  const postImage = ref('') // 海报地址

  onLoad(async (params: any) => {
    id.value = params.id
    upgradeStatus.value = Number(params.isUpdate)
    $loading('生成海报中')

    await $getWechatSetting().then((res: any) => {
      wechatInfo.value = res.data
    })

    /* 获取电子票详情 */
    $collectionDetails(id.value).then((res: any) => {
      if (res.data.image && upgradeStatus.value === 0) res.data.modelUrl = $picFormat(res.data.image)
      if (res.data.upgradeImage && upgradeStatus.value === 1) res.data.modelUrl = $picFormat(res.data.upgradeImage)

      uni.getImageInfo({
        src: res.data.modelUrl,
        success: (image: any) => {
          postW.value = (1000 / image.height) * 560 + 'rpx'

          ticketDetail.value = res.data
        },
      })
    })
  })

  /* 获取生成的海报 */
  const handleGetPost = (val: any) => {
    postImage.value = val

    uni.$u.mpShare.imageUrl = val

    uni.hideLoading()
  }

  /* 生成失败 */
  const handleGetFail = (err: any) => {
    $toast(app, '生成海报失败')
    uni.hideLoading()
  }

  /* 保存海报 */
  const handleSavePost = () => {
    $savePic(app, postImage.value)
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/ticketShare.webp');
  }
</style>
