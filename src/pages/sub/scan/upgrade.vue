<template>
  <view
    class="pageWrap flex min-h-screen w-screen flex-col items-start justify-start bg-[#160947] bg-fullAuto bg-no-repeat pb-[200rpx]"
    v-if="!payLoading">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" placeholder>
      <template #left>
        <image class="block h-[72rpx] w-[72rpx]" :src="$iconFormat('/arrow/left1.svg')" mode="scaleToFill" />
      </template>
    </u-navbar>

    <view class="relative z-10 w-full">
      <template v-if="avatarInfo">
        <image
          class="absolute bottom-0 left-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperL1.svg')"
          @click.stop.prevent="showType = 0"
          mode="scaleToFill"
          v-if="showType === 1" />
        <image
          class="absolute bottom-0 left-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperL2.svg')"
          mode="scaleToFill"
          v-else-if="showType === 0" />
      </template>

      <!-- 电子票 -->
      <u-transition :show="collectionShow1" mode="fade-left">
        <view class="relative m-auto h-[1000rpx] w-screen" @click.stop.prevent @touchstart.stop.prevent>
          <model
            class="modelBox topPatch absolute bottom-0 left-0 right-0 top-0 m-auto"
            type="ticket"
            :url="[upgradeTicketInfo.imageUrl, ticketInfo.coverReverseUrl]"
            height="1100rpx"
            ref="modelRef"
            v-if="upgradeTicketInfo.imageUrl && ticketInfo.coverReverseUrl" />
        </view>

        <view class="absolute bottom-0 left-0 right-0 m-auto flex h-8 w-[582rpx] items-center justify-between">
          <image
            class="block h-8 w-8"
            :src="$iconFormat('icon/rotateR.svg')"
            @longtap="rotateModel(1)"
            @touchend="rotateStop"
            mode="scaleToFill" />
          <image
            class="block h-8 w-8"
            :src="$iconFormat('icon/rotateL.svg')"
            @longtap="rotateModel(2)"
            @touchend="rotateStop"
            mode="scaleToFill" />
        </view>
      </u-transition>
      <!-- 数字头像 -->
      <u-transition :show="collectionShow2" mode="fade-right">
        <view class="relative m-auto flex h-[1000rpx] w-[560rpx] items-center justify-center overflow-hidden">
          <image
            class="avatar m-auto block h-[560rpx] w-[560rpx]"
            :src="$picFormat(avatarInfo.overlayImage)"
            mode="scaleToFill"
            v-if="avatarInfo.overlayImage" />
        </view>
      </u-transition>

      <template v-if="avatarInfo">
        <image
          class="absolute bottom-0 right-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperR1.svg')"
          @click.stop.prevent="showType = 1"
          mode="scaleToFill"
          v-if="showType === 0" />
        <image
          class="absolute bottom-0 right-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperR2.svg')"
          mode="scaleToFill"
          v-else-if="showType === 1"
      /></template>
    </view>

    <view
      class="relative z-20 mb-[10rpx] w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100"
      :class="collectionShow1 ? '' : 'opacity-0'"
      >按住画面左右翻动可查看票的背面</view
    >

    <view
      class="mb-[20rpx] mt-[136rpx] w-full pl-[26rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
      >升级信息</view
    >

    <view class="mb-[20rpx] w-full pl-[24rpx] pr-[24rpx]">
      <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">剧目名称</text>
      <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
        ticketInfo.repertoireName
      }}</text>
    </view>
    <view class="mb-[20rpx] w-full pl-[24rpx] pr-[24rpx]">
      <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">发放时间</text>
      <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
        >{{ ticketInfo.startTime }}-{{ ticketInfo.endTime }}</text
      >
    </view>
    <view class="mb-[20rpx] w-full pl-[24rpx] pr-[24rpx]">
      <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">组合名称</text>
      <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
        ticketInfo.portfolioName
      }}</text>
    </view>
    <view class="w-full pl-[24rpx] pr-[24rpx]">
      <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">升级价格</text>
      <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-[#FF7F1A]">￥{{ ticketInfo.price }}</text>
    </view>

    <view
      class="m-auto mb-[30rpx] mt-[30rpx] w-[701rpx] border-b-[2rpx] border-dashed border-[#E9C49D] opacity-30"></view>

    <view class="mb-[10rpx] w-full pl-[26rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
      >升级介绍</view
    >

    <view class="pl-[24rpx] pr-[24rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">{{
      ticketInfo.introduction
    }}</view>

    <view
      class="m-auto mb-[30rpx] mt-[30rpx] w-[701rpx] border-b-[2rpx] border-dashed border-[#E9C49D] opacity-30"></view>

    <view class="mb-[10rpx] w-full pl-[26rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
      >免责声明</view
    >

    <view class="pl-[24rpx] pr-[24rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">{{
      ticketInfo.statement
    }}</view>

    <customBtn
      class="upgradeBtn"
      :type="ticketInfo.overTime ? 2 : 3"
      @click="handleShowPayInfo"
      text="确定升级"
      v-if="ticketInfo.upgradeCount != '-1'" />
    <customBtn class="upgradeBtn" type="1" text="已售罄" v-else />
  </view>

  <view class="flex h-screen w-screen flex-col items-center justify-center bg-gradient-to-b from-bgS to-bg" v-else>
    <image class="block w-full" :src="$iconFormat('/loading.gif')" mode="widthFix" />

    <view
      class="mb-[96rpx] mt-[46rpx] w-full text-center font-Regular text-[40rpx] font-normal leading-[56rpx] text-w-60"
      >支付成功</view
    >

    <customBtn class="m-auto mb-0 mt-0" @tap="$replace({ name: 'Receive' })" text="返回查看" width="260" />
  </view>

  <!-- 确认支付弹窗 -->
  <u-popup
    :closeOnClickOverlay="false"
    :show="paySureController"
    @close="paySureController = false"
    bgColor="#1F1933"
    round="30rpx">
    <view
      class="mb-[18rpx] flex h-[112rpx] w-full items-center justify-center border-b-[1rpx] border-solid border-w-10 pl-[24rpx] pr-[24rpx]">
      <view class="h-[48rpx] w-[48rpx] shrink-0"></view>

      <text class="grow text-center font-Medium text-[36rpx] font-medium leading-[50rpx] text-w-100">确认订单</text>

      <image
        class="block h-[48rpx] w-[48rpx] shrink-0"
        :src="$iconFormat('/icon/close1.svg')"
        @click="paySureController = false"
        mode="scaleToFill" />
    </view>

    <view class="flex items-center justify-start pl-[20rpx] pr-[20rpx]">
      <view class="col mr-[20rpx] grow">
        <view class="mb-[40rpx] flex w-full items-center justify-start">
          <image
            class="mr-[20rpx] block h-[150rpx] w-[150rpx] shrink-0 rounded-[10rpx]"
            :src="$picFormat(upgradeTicketInfo.image)"
            mode="aspectFit" />

          <view class="mr-[20rpx] grow">
            <view class="mb-[20rpx] font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
              >{{ ticketInfo.repertoireName || '-' }}升级收藏票</view
            >

            <view class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">x1</view>
          </view>
        </view>

        <view class="mb-[40rpx] flex w-full items-center justify-start" v-if="avatarInfo">
          <image
            class="mr-[20rpx] block h-[150rpx] w-[150rpx] shrink-0 rounded-[10rpx]"
            :src="$picFormat(avatarInfo.overlayImage)"
            mode="scaleToFill" />

          <view class="mr-[20rpx] grow">
            <view class="mb-[20rpx] font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
              >{{ avatarInfo.repertoireName || '-' }}升级数字头像</view
            >

            <view class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">x1</view>
          </view>
        </view>
      </view>

      <view class="shrink-0">
        <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#FF7F1A]">￥</text>
        <text class="font-Regular text-[40rpx] font-normal leading-[34rpx] text-[#FF7F1A]">{{
          ticketInfo.price || '0.00'
        }}</text>
      </view>
    </view>

    <customBtn class="m-auto" type="3" @click="hanldePay" text="确定支付" />

    <view class="mt-[40rpx] w-full text-center">
      <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60">确认支付即代表同意</text>
      <text
        class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#9C75D3]"
        @click="$push({ name: 'Agreement', params: { type: 4 } })"
        >《虚拟商品交易协议》</text
      >
    </view>
  </u-popup>

  <!-- 升级票合成 -->
  <createTicket :app="app" :ticketInfo="upgradeTicketInfo" @getUrl="handleGetTicketUrl" v-if="upgradeTicketInfo" />

  <!-- toast提示 -->
  <u-toast ref="uToast"></u-toast>
  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import createTicket from './components/createTicket.vue'
  import { $upgradeInfo } from '@/api/collention'
  import { $getPayInfo } from '@/api/pay'
  import { $collectionUpdate } from '@/api/scan'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import model from '@/components/Model.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $picFormat, $push, $replace, $toast } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()

  const showType = ref(0) // 显示类型 0：电子票 1：数字头像
  const collectionShow1 = ref(true) // 电子票显示
  const collectionShow2 = ref(false) // 数字头像显示

  const portfolioNo = ref(0) // 组合包id
  const ticketInfo = ref<any>('') // 电子票详情
  const avatarInfo = ref<any>('') // 数字头像

  const upgradeTicketInfo = ref<any>({}) // 升级电子票信息

  const paySureController = ref(false) // 支付确认弹窗
  const payLoading = ref(false) // 支付中

  watch(showType, (val: any) => {
    if (val === 0) {
      uni.$u.sleep(300).then(() => {
        collectionShow1.value = true
      })
      collectionShow2.value = false
    } else if (val === 1) {
      collectionShow1.value = false
      uni.$u.sleep(300).then(() => {
        collectionShow2.value = true
      })
    }
  })

  onLoad((params: any) => {
    portfolioNo.value = params.portfolioNo

    getInfo()
  })

  /* 数据加载 */
  const getInfo = () => {
    $upgradeInfo({ portfolioNo: portfolioNo.value }).then((res: any) => {
      res.data.map((i: any) => {
        if (i.badgeType === 1) {
          if (i.coverFront) i.coverFrontUrl = $picFormat(i.coverFront)
          if (i.coverReverse) i.coverReverseUrl = $picFormat(i.coverReverse)
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY/MM/DD HH:mm')
          if (i.endTime) {
            i.overTime = dayjs().isAfter(i.endTime)
            i.endTime = dayjs(i.endTime).format('YYYY/MM/DD HH:mm')
          }

          let upgradeTicketH: string = '0px'

          /* 升级票 */
          if (i.coverFrontUrl) {
            uni.getImageInfo({
              src: i.coverFrontUrl,
              success: (image: any) => {
                upgradeTicketH = ((image.height / image.width) * uni.upx2px(520) || uni.upx2px(930)) + 'px'

                upgradeTicketInfo.value = {
                  ticketH: upgradeTicketH,
                  coverFrontUrl: i.coverFrontUrl,
                  theater: i.theaterName,
                  repertoire: i.repertoireName,
                  repertoireCoverPictureUrl: i.repertoireCoverPicture
                    ? $picFormat(i.repertoireCoverPicture)
                    : undefined,
                  serialNumber: i.no,
                  dateTime: dayjs(i.time).format('YYYY.MM.DD HH:mm'),
                  price: i.amount,
                  seat: i.seatNumber,
                }

                ticketInfo.value = i
              },
            })
          }
        }

        if (i.badgeType === 2) avatarInfo.value = i
      })
    })
  }

  /* 获取生成的电子票 */
  const handleGetTicketUrl = (url: string) => {
    upgradeTicketInfo.value.image = url
    upgradeTicketInfo.value.imageUrl = $picFormat(url)
  }

  /* 旋转模型 */
  const rotateModel = (type: number) => {
    app.$refs.modelRef.rotateModel(type)
  }
  /* 停止旋转模型 */
  const rotateStop = () => {
    app.$refs.modelRef.rotateStop()
  }

  /* 显示确认升级弹窗 */
  const handleShowPayInfo = () => {
    if (ticketInfo.value.overTime) $toast(app, '已过商品发放截止时间，请期待下一批次。')
    else paySureController.value = true
  }

  /* 确定支付 */
  const hanldePay = () => {
    $getPayInfo(ticketInfo.value.id).then((res: any) => {
      if (res.data.timeStamp && res.data.nonceStr && res.data.packageStr && res.data.sign && res.data.prepayId) {
        /* 付费购买 */
        uni.requestPayment({
          provider: 'wxpay',
          orderInfo: '',
          timeStamp: res.data.timeStamp,
          nonceStr: res.data.nonceStr,
          package: res.data.packageStr,
          signType: 'RSA',
          paySign: res.data.sign,
          success: (res: any) => {
            if (res.errMsg === 'requestPayment:ok') {
              paySureController.value = false
              payLoading.value = true

              if (ticketInfo.value) ticketInfo.value.upgradeStatus = 1
              if (avatarInfo.value) avatarInfo.value.upgradeStatus = 1

              handleUpdateTicket()
            } else {
              $toast(app, '支付失败')
            }
          },
          fail: (err: any) => {},
        })
      } else if (
        !res.data.timeStamp &&
        !res.data.nonceStr &&
        !res.data.packageStr &&
        !res.data.sign &&
        !res.data.prepayId
      ) {
        /* 免费购买 */
        paySureController.value = false
        payLoading.value = true

        if (ticketInfo.value) ticketInfo.value.upgradeStatus = 1
        if (avatarInfo.value) avatarInfo.value.upgradeStatus = 1

        handleUpdateTicket()
      }
    })
  }

  /* 修改升级票样式 */
  const handleUpdateTicket = () => {
    $collectionUpdate({
      id: ticketInfo.value.id,
      portfolioInfoId: ticketInfo.value.portfolioInfoId,
      upgradeImage: upgradeTicketInfo.value.image,
    }).then((res: any) => {
      let scanResult: any = []
      if (ticketInfo.value) scanResult.push({ ...ticketInfo.value })
      if (avatarInfo.value) scanResult.push({ ...avatarInfo.value })

      uni.setStorageSync('scanResult', scanResult)
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/scanBg.webp');

    .upgradeBtn {
      position: fixed;
      right: 0;
      bottom: 100rpx;
      left: 0;
      margin: auto;
    }
  }
</style>
