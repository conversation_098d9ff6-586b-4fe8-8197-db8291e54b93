<template>
  <view class="pageWrap min-h-screen w-screen bg-deepbg bg-fullAuto bg-no-repeat pb-[180rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder />

    <view class="m-auto w-[680rpx]">
      <u-image :src="postImage" height="1186rpx" mode="aspectFill" width="680rpx"></u-image>
    </view>
  </view>

  <u-popup :closeOnClickOverlay="false" :overlay="false" :show="showMenu" round="30rpx">
    <view class="flex items-start justify-start pb-2.5 pl-[42rpx] pr-[42rpx] pt-2.5">
      <button class="btnDef mr-[80rpx] h-fit w-fit p-0" open-type="share">
        <view class="flex w-[104rpx] flex-col items-center justify-start">
          <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/wechat.svg')" mode="scaleToFill" />
          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">微信好友</text>
        </view>
      </button>

      <view class="flex w-[104rpx] flex-col items-center justify-start" @click="handleSavePost">
        <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/save.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">保存图片</text>
      </view>
    </view>
  </u-popup>

  <l-painter
    :pixelRatio="2"
    @fail="handleGetFail"
    @success="handleGetPost"
    css="width: 680rpx; height: 1186rpx"
    hidden
    isCanvasToTempFilePath
    pathType="url"
    v-if="avatarDetail.modelUrl">
    <l-painter-view css="width: 680rpx; height: 1000rpx; background: #422E6B; border-radius: 20rpx;">
      <!-- 基本信息 -->
      <l-painter-view css="width: 680rpx; padding: 30rpx 40rpx 132rpx;">
        <!-- 用户头像 -->
        <l-painter-image
          :src="userInfo.avatarUrl"
          css="width: 80rpx; height: 80rpx; object-fit: cover; margin: 0 16rpx 0 0; border-radius: 50%;" />

        <!-- 用户昵称 -->
        <l-painter-view css="display: inline-block; width: 406rpx; margin: 0 16rpx 0 0;">
          <l-painter-text
            :text="userInfo.name"
            css="color: #FFFFFF; line-height: 44rpx; font-weight: normal; font-size: 32rpx; margin: 0 0 2rpx 0; display: block;" />
          <l-painter-text
            css="color: rgba(255, 255, 255, 0.6); line-height: 34rpx; font-weight: normal; font-size: 24rpx; display: block;"
            text="我又获得了1枚数字头像～" />
        </l-painter-view>

        <!-- 剧目二维码 -->
        <l-painter-image :src="avatarDetail.qrCode" css="width: 80rpx; height: 80rpx; object-fit: cover;" />
      </l-painter-view>
      <!-- 头像 -->
      <l-painter-image
        :src="avatarDetail.modelUrl"
        css="width: 560rpx; height: 560rpx; object-fit: cover; margin: 0 auto 60rpx;" />
      <!-- 剧目信息 -->
      <l-painter-text
        :text="`《${avatarDetail.repertoireName || '-'}》`"
        css="width: 100%; text-align: center; color: #FFFFFF; line-height: 50rpx; font-weight: normal; font-size: 36rpx; margin: 0 0 10rpx 0;" />
      <l-painter-text
        :text="`获得时间：${avatarDetail.createTime || '未知'}`"
        css="width: 100%; text-align: center; color: rgba(255, 255, 255, 0.7); line-height: 34rpx; font-weight: normal; font-size: 24rpx;" />
    </l-painter-view>

    <!-- 小程序二维码 -->
    <l-painter-image
      :src="$iconFormat('qrcode.png')"
      css="width: 120rpx; height: 120rpx; object-fit: cover; margin: 20rpx auto 12rpx;" />

    <!-- 提示 -->
    <l-painter-text
      :text="`长按识别二维码，来${wechatInfo.wechatName}看演出`"
      css="width: 100%; text-align: center; color: #FFFFFF; line-height: 34rpx; font-weight: normal; font-size: 24rpx;" />
  </l-painter>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $detailsByDigitalAvatar } from '@/api/avatar'
  import { $getWechatSetting } from '@/api/base'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $savePic, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo } = storeToRefs(useGlobalStore())

  const id = ref() // 数字头像id
  const isUpdate = ref(false) // 是否是升级头像
  const avatarDetail = ref<any>('') // 数字头像详情
  const postImage = ref('') // 海报地址
  const wechatInfo = ref<any>('')
  const showMenu = ref(true)

  onLoad(async (params: any) => {
    id.value = params.id
    isUpdate.value = params.isUpdate === '0' ? false : true

    $loading('生成海报中')

    await $getWechatSetting().then((res: any) => {
      wechatInfo.value = res.data
    })

    /* 获取数字头像详情 */
    $detailsByDigitalAvatar(id.value).then((res: any) => {
      if (res.data.image && !isUpdate.value) res.data.modelUrl = $picFormat(res.data.image)
      else if (res.data.upgradeImage && isUpdate.value) res.data.modelUrl = $picFormat(res.data.upgradeImage)
      if (res.data.qrCode) res.data.qrCode = $picFormat(res.data.qrCode)

      avatarDetail.value = res.data
    })
  })

  /* 获取生成的海报 */
  const handleGetPost = (val: any) => {
    postImage.value = val

    uni.$u.mpShare.imageUrl = val

    uni.hideLoading()
  }

  /* 生成失败 */
  const handleGetFail = (err: any) => {
    $toast(app, '生成海报失败')
    uni.hideLoading()
  }

  /* 保存海报 */
  const handleSavePost = () => {
    $savePic(app, postImage.value)
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/avatarShare.webp');
  }
</style>
