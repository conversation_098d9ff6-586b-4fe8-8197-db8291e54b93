<template>
  <view class="pageWrap min-h-screen w-screen bg-deepbg pb-[50rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      @leftClick="backRoute"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />

    <!-- 领取电子票 -->
    <template v-if="!failModalController">
      <!-- 扫描成功 -->
      <view class="m-auto mb-[28rpx] mt-[40rpx] w-[498rpx]">
        <view
          class="mb-[4rpx] w-full text-center font-Medium text-[36rpx] font-medium leading-[50rpx] text-lightGlod-100"
          >识别成功！</view
        >
        <view class="m-auto w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-lightGlod-70"
          >请点击领取您的电子纪念品，领取之后将为您自动关注剧目和剧场。
        </view>
      </view>

      <!-- 预览 -->
      <view class="flex items-center" :class="scanResult?.digitalAvatarId ? 'justify-between' : 'justify-center'">
        <template v-if="scanResult?.digitalAvatarId">
          <!-- 左按钮 -->
          <image
            class="block h-[98rpx] w-[96rpx]"
            :src="$iconFormat('/arrow/swiperL1.svg')"
            @click.stop.prevent="showType = 0"
            mode="scaleToFill"
            v-if="showType === 1" />
          <image
            class="block h-[98rpx] w-[96rpx]"
            :src="$iconFormat('/arrow/swiperL2.svg')"
            mode="scaleToFill"
            v-else-if="showType === 0" />
        </template>

        <!-- 电子票 -->
        <u-transition :show="collectionShow1" mode="fade-left" timingFunction="ease">
          <view class="relative m-auto min-h-[930rpx] w-[520rpx] overflow-hidden">
            <image
              class="block w-[520rpx]"
              :src="$picFormat(commenTicketInfo.image)"
              mode="widthFix"
              v-if="commenTicketInfo.image" />

            <u-button
              class="editBtn"
              @click="handleShowEditSeatDailog"
              color="linear-gradient(180deg, #CBADEB 0%, #9C75D3 100%)"
              shape="circle"
              text="修改"
              v-if="commenTicketInfo.image"></u-button>
          </view>
        </u-transition>

        <!-- 数字头像 -->
        <u-transition :show="collectionShow2 && scanResult?.digitalAvatarId" mode="fade-right" timingFunction="ease">
          <view class="relative m-auto flex h-[930rpx] w-[560rpx] items-center justify-center overflow-hidden">
            <image
              class="avatar m-auto block h-[560rpx] w-[560rpx]"
              :src="$picFormat(scanResult?.digitalAvatarCommonImage)"
              mode="scaleToFill"
              v-if="scanResult?.digitalAvatarId" />
          </view>
        </u-transition>

        <template v-if="scanResult?.digitalAvatarId">
          <!-- 右按钮 -->
          <image
            class="block h-[98rpx] w-[96rpx]"
            :src="$iconFormat('/arrow/swiperR1.svg')"
            @click.stop.prevent="showType = 1"
            mode="scaleToFill"
            v-if="showType === 0" />
          <image
            class="block h-[98rpx] w-[96rpx]"
            :src="$iconFormat('/arrow/swiperR2.svg')"
            mode="scaleToFill"
            v-else-if="showType === 1" />
        </template>
      </view>

      <view
        class="mt-[18rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        v-if="showType === 0 && scanResult?.digitalAvatarId"
        >点击右侧按钮查看数字头像（普通）</view
      >
      <view
        class="mt-[18rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        v-else-if="showType === 1 && scanResult?.digitalAvatarId"
        >点击左侧按钮查看电子票（普通）</view
      >

      <view
        class="ml-auto mr-auto mt-[28rpx] box-border w-[520rpx] pl-2.5"
        v-if="actorInformationList && actorInformationList.length">
        <view class="mb-[6rpx] w-full font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">演职信息</view>
        <view
          class="mb-[12rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
          :key="index"
          v-for="(i, index) in actorInformationList"
          >{{ i.actorInformation }}</view
        >
      </view>

      <view class="mtPatch flex items-center justify-center">
        <customBtn class="mr-5" @tap="handleAddActor" text="编辑演员信息" v-if="scanResult?.commonImage" width="260" />

        <!-- 可以领取 -->
        <customBtn type="3" @tap="handleReceiveTicket" text="立即领取" v-if="scanResult?.commonImage" width="260" />
        <customBtn class="m-auto" type="2" text="立即领取" v-else width="260" />
      </view>
    </template>

    <!-- 扫描失败 -->
    <u-modal
      class="failModelWrap"
      title="扫描失败"
      :content="globalStore.scanResult?.msg"
      :show="failModalController"
      @confirm="handleConfirm"
      confirmColor="#9C75D3"
      confirmText="我知道了"
      width="640rpx"></u-modal>
  </view>

  <u-popup
    :closeOnClickOverlay="false"
    :safeAreaInsetBottom="false"
    :show="editDailogController"
    @close="editDailogController = false"
    bgColor="#493D70"
    mode="center"
    round="30rpx"
    zIndex="10069">
    <view class="h-[400rpx] w-[560rpx]">
      <view class="relative flex h-[98rpx] w-full items-center justify-center">
        <text class="font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100">修改座位识别信息</text>

        <image
          class="absolute bottom-0 right-[14rpx] top-0 m-auto block h-[48rpx] w-[48rpx]"
          :src="$iconFormat('icon/close2.svg')"
          @click="editDailogController = false" />
      </view>

      <view class="h-[2rpx] w-full bg-w-100 opacity-10"></view>

      <u-input
        class="seatInput"
        border="surround"
        maxlength="25"
        placeholder="请输入座位"
        shape="circle"
        v-model="editSeat"></u-input>

      <customBtn class="mtPatch m-auto" @tap="handleEdit" text="确认修改" width="260" />
    </view>
  </u-popup>

  <!-- 普通票合成 -->
  <createTicket :app="app" :ticketInfo="commenTicketInfo" @getUrl="(url: string) => handleGetTicketUrl(0, url)" />
  <!-- 升级票合成 -->
  <createTicket :app="app" :ticketInfo="upgradeTicketInfo" @getUrl="(url: string) => handleGetTicketUrl(1, url)" />

  <!-- 数字头像合成 -->
  <l-painter
    :pixelRatio="2"
    @fail="handleGetAvatarFail"
    @success="handleGetAvatar"
    css="width: 800rpx; height: 800rpx;"
    hidden
    isCanvasToTempFilePath
    pathType="url"
    v-if="scanResult?.digitalAvatarUrlList">
    <l-painter-image
      :key="index"
      :src="$picFormat(i)"
      css="width: 800rpx; height: 800rpx; object-fit: cover; position: absolute; top: 0; left: 0;"
      v-for="(i, index) in scanResult?.digitalAvatarUrlList" />
  </l-painter>

  <!-- 提示 -->
  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import createTicket from './components/createTicket.vue'
  import { $upload } from '@/api/base'
  import { $collectionAdd, $userDigitalAvatarAdd } from '@/api/scan'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $push, $replace, $replaceAll, $toast } from '@/utils/methods'
  import dayjs from 'dayjs'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy

  const globalStore = useGlobalStore()

  const showType = ref(0) // 显示类型 0：电子票 1：数字头像
  const collectionShow1 = ref(true) // 电子票显示
  const collectionShow2 = ref(false) // 数字头像显示

  const failModalController = ref(false) // 扫描失败弹窗控制器
  const scanResult = ref<any>('') // 扫描结果

  const commenTicketInfo = ref<any>({}) // 普通电子票信息
  const upgradeTicketInfo = ref<any>({}) // 升级电子票信息

  const editDailogController = ref(false) // 修改
  const editSeat = ref()

  const actorInformationList = ref<any>([]) // 演员列表

  watch(showType, (val: any) => {
    if (val === 0) {
      uni.$u.sleep(250).then(() => {
        collectionShow1.value = true
      })
      collectionShow2.value = false
    } else if (val === 1) {
      collectionShow1.value = false
      uni.$u.sleep(250).then(() => {
        collectionShow2.value = true
      })
    }
  })

  onLoad(() => {
    switch (globalStore.scanResult.code) {
      case 200:
        /* 扫描成功 */
        $loading('合成预览中')

        failModalController.value = false

        /* 扫描结果 */
        let scanResultObj: any = globalStore.scanResult.data

        /* 格式化升级票排序 */
        if (scanResultObj?.digitalAvatarUrlList)
          scanResultObj.digitalAvatarUrlList = _.reverse(scanResultObj.digitalAvatarUrlList)

        /* 演出时间 */
        if (scanResultObj?.dateTime) scanResultObj.dateTime = dayjs(scanResultObj.dateTime).format('YYYY.MM.DD HH:mm')

        /* 电子票 */
        let commonTicketH: string = '0px',
          upgradeTicketH: string = '0px'

        /* 普通票 */
        if (scanResultObj?.commonImage) {
          scanResultObj.commonImage = $picFormat(scanResultObj.commonImage)

          uni.getImageInfo({
            src: scanResultObj.commonImage,
            success: (image: any) => {
              commonTicketH = ((image.height / image.width) * uni.upx2px(520) || uni.upx2px(930)) + 'px'

              commenTicketInfo.value = {
                ticketH: commonTicketH,
                coverFrontUrl: scanResultObj.commonImage,
                theater: scanResultObj.theater,
                repertoire: scanResultObj.repertoire,
                repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
                serialNumber: scanResultObj.serialNumber,
                dateTime: scanResultObj.dateTime,
                price: scanResultObj.price,
                seat: scanResultObj.seat,
              }

              /**
               * 776	Elsa在看剧			星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00001	2023-10-02 14:30:00	299.00	2排2排10号
               * 1044	图小喵			    星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00002	2024-01-13 14:30:00	399.00	1排6号
               * 1045	玫贝儿			    星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00003	2024-01-24 19:30:00	249.00	4排10号
               * 1046	知微			      星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00004	2024-01-13 14:30:00	299.00	2排11号
               * 1047	知微			      星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00005	2023-10-28 14:30:00	299.00	2排11号
               * 1059	小云			      星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00006	2024-01-13 14:30:00	399.00	1排5号
               * 1060	一一			      星空间332号·法国悬疑剧场	【上海】心理惊悚剧《她的审判》	NO.00007	2024-01-24 19:30:00	349.00	1排4号
               *  */

              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00001',
              dateTime: '2023-10-02 14:30',
              price: '299.00',
              seat: '2排2排10号',
            } */
              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00002',
              dateTime: '2024-01-13 14:30',
              price: '399.00',
              seat: '1排6号',
            } */
              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00003',
              dateTime: '2024-01-24 19:30',
              price: '249.00',
              seat: '4排10号',
            } */
              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00004',
              dateTime: '2024-01-13 14:30',
              price: '299.00',
              seat: '2排11号',
            } */
              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00005',
              dateTime: '2023-10-28 14:30',
              price: '299.00',
              seat: '2排11号',
            } */
              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00006',
              dateTime: '2024-01-13 14:30',
              price: '399.00',
              seat: '1排5号',
            } */
              /* commenTicketInfo.value = {
              ticketH: commonTicketH,
              coverFrontUrl: scanResultObj.commonImage,
              theater: '星空间332号·法国悬疑剧场',
              repertoire: '【上海】心理惊悚剧《她的审判》',
              repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
              serialNumber: 'NO.00007',
              dateTime: '2024-01-24 19:30',
              price: '349.00',
              seat: '1排4号',
            } */
            },
          })
        }

        /* 升级票 */
        if (scanResultObj?.coverFront) {
          scanResultObj.coverFront = $picFormat(scanResultObj.coverFront)

          uni.getImageInfo({
            src: scanResultObj.coverFront,
            success: (image: any) => {
              upgradeTicketH = ((image.height / image.width) * uni.upx2px(520) || uni.upx2px(930)) + 'px'

              upgradeTicketInfo.value = {
                ticketH: upgradeTicketH,
                coverFrontUrl: scanResultObj.coverFront,
                theater: scanResultObj.theater,
                repertoire: scanResultObj.repertoire,
                repertoireCoverPictureUrl: $picFormat(scanResultObj.repertoireCoverPicture),
                serialNumber: scanResultObj.serialNumber,
                dateTime: scanResultObj.dateTime,
                price: scanResultObj.price,
                seat: scanResultObj.seat,
              }
            },
          })
        }

        scanResult.value = globalStore.scanResult.data
        break
      case 500:
        /* 扫描失败 */
        failModalController.value = true
        break
      default:
        /* 接口其他报错 */
        uni.$u.toast(
          globalStore.scanResult.msg && globalStore.scanResult.msg.length < 200
            ? globalStore.scanResult.msg
            : '扫描识别失败'
        )
        break
    }

    uni.$on('updateActorInfo', (data: any) => {
      actorInformationList.value = [...data]
    })
  })

  /* 我知道了按钮 */
  const handleConfirm = () => {
    failModalController.value = false
    $back()
  }

  /* 获取生成的电子票 */
  const handleGetTicketUrl = (type: number, url: string) => {
    switch (type) {
      case 0:
        commenTicketInfo.value.image = url
        break
      case 1:
        upgradeTicketInfo.value.image = url
        break
    }
  }

  /* 获取生成的数字头像 */
  const handleGetAvatar = (val: any) => {
    $upload({
      name: 'file',
      filePath: val,
    }).then((res: any) => {
      $userDigitalAvatarAdd({
        portfolioId: scanResult.value.portfolioId,
        digitalAvatarId: scanResult.value.digitalAvatarId,
        image: res.fileName,
      })
        .then((res: any) => {
          scanResult.value.digitalAvatarUpdateImage = res.fileName
          uni.hideLoading()
        })
        .catch((err: any) => {
          $toast(app, '生成数字头像失败')
          uni.hideLoading()
        })
    })
  }

  /* 数字头像生成失败 */
  const handleGetAvatarFail = (err: any) => {
    $toast(app, '生成数字头像失败')
    uni.hideLoading()
  }

  /* 领取电子票 */
  const handleReceiveTicket = () => {
    if (commenTicketInfo.value.seat.length > 25) {
      $toast(app, '座位字数过长', handleShowEditSeatDailog)

      return
    }

    $loading()

    // 电子票
    let repertoireTicketList: any = [
      {
        seatNumber: commenTicketInfo.value.seat,
        amount: scanResult.value.price,
        image: commenTicketInfo.value.image,
        upgradeImage: upgradeTicketInfo.value.image,
        theaterId: scanResult.value.theaterId,
        time: scanResult.value.dateTime?.split('.').join('-') + ':00',
        repertoireId: scanResult.value.repertoireId,
        relationId: scanResult.value.repertoireTicketId,
        badgeType: 1,
        repertoireInfoDetailId: scanResult.value.repertoireInfoDetailId,
        repertoireInfoId: scanResult.value.repertoireInfoId,
      },
    ]

    // 数字头像
    if (scanResult.value.digitalAvatarId) {
      repertoireTicketList.push({
        seatNumber: commenTicketInfo.value.seat,
        amount: scanResult.value.price,
        image: scanResult.value.digitalAvatarCommonImage,
        upgradeImage: scanResult.value.digitalAvatarUpdateImage,
        theaterId: scanResult.value.theaterId,
        time: scanResult.value.dateTime?.split('.').join('-') + ':00',
        repertoireId: scanResult.value.repertoireId,
        relationId: scanResult.value.digitalAvatarId,
        badgeType: 2,
        repertoireInfoDetailId: scanResult.value.repertoireInfoDetailId,
        repertoireInfoId: scanResult.value.repertoireInfoId,
      })
    }

    $collectionAdd({
      portfolioId: scanResult.value.portfolioId,
      portfolioInfoId: scanResult.value.portfolioInfoId,
      no: scanResult.value.serialNumber,
      fileUrl: scanResult.value.fileUrl,
      actorInformationList: actorInformationList.value,
      repertoireTicketList: repertoireTicketList,
    }).then((res: any) => {
      globalStore.scanResult = res.data
      uni.setStorageSync('scanResult', res.data)

      $replace({ name: 'Receive' })

      uni.hideLoading()
    })
  }

  /* 后退页面 */
  const backRoute = () => {
    $replaceAll({ name: 'Personal' })
  }

  /* 显示修改弹窗 */
  const handleShowEditSeatDailog = () => {
    editDailogController.value = true
    editSeat.value = commenTicketInfo.value.seat
  }

  /* 修改座位 */
  const handleEdit = () => {
    if (editSeat.value?.length > 25) {
      $toast(app, '座位过长，字数不得超过25个字')
      return
    }

    commenTicketInfo.value.seat = editSeat.value
    upgradeTicketInfo.value.seat = editSeat.value
    scanResult.value.seat = editSeat.value
    editDailogController.value = false

    $loading('合成预览中')
  }

  /* 添加演员信息 */
  const handleAddActor = () => {
    $push({ name: 'AddActor', params: { actorInfo: JSON.stringify(actorInformationList.value) } })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .mtPatch {
      margin-top: 60rpx;
    }

    .failModelWrap {
      & > :deep(view:first-child) {
        display: none !important;
      }

      &:deep(.u-popup__content) {
        background: #1e1e1e;
      }

      &:deep(.u-modal) {
        .u-modal__title {
          box-sizing: border-box;
          width: 100%;
          padding: 0 48rpx;
          padding-top: 64rpx;
          margin-bottom: 32rpx;
          font-family: PingFangSC-Medium, 'PingFang SC';
          font-size: 34rpx;
          font-weight: 500;
          line-height: 48rpx;
          color: #fff;
        }

        .u-modal__content {
          box-sizing: border-box;
          width: 100%;
          min-height: 144rpx;
          padding: 0 48rpx;
          margin-bottom: 4rpx;

          &__text {
            display: block;
            max-width: 100%;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 34rpx;
            font-weight: 400;
            line-height: 48rpx;
            color: #fff;
            text-align: center;
            word-break: break-all;
          }
        }

        .u-line {
          border-color: rgb(255 255 255 / 5%) !important;
        }

        .u-modal__button-group {
          width: 100%;

          &__wrapper {
            width: 100%;
            height: 106rpx;

            &__text {
              font-family: PingFangSC-Medium, 'PingFang SC';
              font-size: 34rpx;
              font-weight: 500;
              line-height: 48rpx;
              letter-spacing: 1px;
            }
          }
        }
      }
    }

    .editBtn {
      position: absolute;
      right: 186rpx;
      bottom: 86rpx;
      width: 66rpx;
      height: 40rpx;
      padding: 0;
      box-shadow: inset 0rpx 2rpx 6rpx 0rpx rgb(255 255 255 / 50%);

      &:deep(.u-button__text) {
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 22rpx !important;
        font-weight: 400 !important;
        color: #fff !important;
      }
    }
  }
</style>

<style lang="scss">
  .seatInput {
    width: 462rpx;
    height: 88rpx;
    padding: 0 20rpx;
    margin: 40rpx auto 48rpx;

    .u-input__content__field-wrapper__field {
      font-family: PingFangSC-Regular, 'PingFang SC' !important;
      font-size: 28rpx !important;
      font-weight: 400 !important;
      color: #fff !important;
    }
  }
</style>
