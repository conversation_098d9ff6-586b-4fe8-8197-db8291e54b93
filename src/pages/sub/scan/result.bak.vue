<template>
  <image class="m-auto mt-40 block w-[520rpx]" :src="$picFormat(urlPIc)" mode="widthFix" v-if="urlPIc" />

  <!-- 升级票合成 -->
  <createTicket
    :app="app"
    :ticketInfo="upgradeTicketInfo"
    @getUrl="(url: string) => handleGetTicketUrl(1, url)"
    v-if="upgradeTicketInfo" />
</template>

<script lang="ts" setup>
  import createTicket from './components/createTicket.vue'
  import { $iconFormat, $picFormat } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy

  const urlPIc = ref('')
  const upgradeTicketInfo = ref<any>() // 升级电子票信息

  onShow(() => {
    let coverFront = $picFormat('/profile/upload/2024/03/01/维罗妮卡的房间电子票 202402版本_20240301170047A362.png')

    uni.getImageInfo({
      src: coverFront,
      success: (image: any) => {
        let upgradeTicketH = ((image.height / image.width) * uni.upx2px(520) || uni.upx2px(930)) + 'px'
        let repertoireCoverPictureUrl = $picFormat(
          '/profile/upload/2024/03/01/微信图片_20240301143038_20240301144128A340.png'
        )

        upgradeTicketInfo.value = {
          ticketH: upgradeTicketH,
          coverFrontUrl: coverFront,
          theater: '上海共舞台',
          repertoire: '【上海】话剧《维罗妮卡的房间》百老汇经典悬疑惊悚话剧-上海共舞台',
          repertoireCoverPictureUrl: repertoireCoverPictureUrl,
          serialNumber: 'NO.00028',
          price: '0',
          seat: '一楼15排16号',
          dateTime: '2024.3.15 19:30',
          name: 'reallllyx',
          avatarUrl: $iconFormat('avatar.jpg'),
        }
      },
    })
  })

  /* 获取生成的电子票 */
  const handleGetTicketUrl = (type: number, url: string) => {
    console.log('🚀 ~ handleGetTicketUrl ~ url:', $picFormat(url))
    urlPIc.value = url
  }
</script>

<style lang="scss" scoped>
  .pic {
    display: block;
    width: 90%;
    margin: 100rpx auto 0;
  }
</style>
