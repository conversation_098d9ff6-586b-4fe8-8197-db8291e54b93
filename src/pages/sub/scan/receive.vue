<template>
  <!-- 普通 -->
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-[#1E1733] to-[#0A0713] pb-[50rpx]" v-if="!isUpdate">
    <!-- 导航栏 -->
    <u-navbar
      class="z-50 w-full shrink-0"
      @leftClick="backRoute"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />

    <view class="relative mt-5 flex items-center" :class="avatarInfo ? 'justify-between' : 'justify-center'">
      <template v-if="avatarInfo">
        <image
          class="block h-[98rpx] w-[96rpx] shrink-0"
          :src="$iconFormat('/arrow/swiperL1.svg')"
          @click.stop.prevent="showType = 0"
          mode="scaleToFill"
          v-if="showType === 1" />
        <image
          class="block h-[98rpx] w-[96rpx] shrink-0"
          :src="$iconFormat('/arrow/swiperL2.svg')"
          mode="scaleToFill"
          v-else-if="showType === 0" />
      </template>

      <!-- 电子票 -->
      <u-transition :show="collectionShow1" mode="fade-left">
        <view class="relative m-auto min-h-[1000rpx] w-[560rpx] overflow-hidden">
          <image class="block w-[560rpx]" :src="ticketInfo.imageUrl" mode="widthFix" v-if="ticketInfo.image" />
        </view>
      </u-transition>
      <!-- 数字头像 -->
      <u-transition :show="collectionShow2" mode="fade-right">
        <view class="relative m-auto flex h-[1000rpx] w-[560rpx] items-center justify-center overflow-hidden">
          <image
            class="avatar m-auto block h-[560rpx] w-[560rpx]"
            :src="$picFormat(avatarInfo.image)"
            mode="scaleToFill"
            v-if="avatarInfo.image" />
        </view>
      </u-transition>

      <template v-if="avatarInfo">
        <image
          class="block h-[98rpx] w-[96rpx] shrink-0"
          :src="$iconFormat('/arrow/swiperR1.svg')"
          @click.stop.prevent="showType = 1"
          mode="scaleToFill"
          v-if="showType === 0" />
        <image
          class="block h-[98rpx] w-[96rpx] shrink-0"
          :src="$iconFormat('/arrow/swiperR2.svg')"
          mode="scaleToFill"
          v-else-if="showType === 1" />
      </template>

      <image
        class="absolute bottom-[52rpx] right-[24rpx] block h-[140rpx] w-[72rpx]"
        :src="$iconFormat('share.webp')"
        @tap="
          $push({
            name: showType === 0 ? 'TicketSave' : 'AvatarSave',
            params: { id: showType === 0 ? ticketInfo.id : avatarInfo.id, isUpdate: 0 },
          })
        "
        mode="scaleToFill" />
    </view>

    <view
      class="mb-[50rpx] mt-[18rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
      v-if="showType === 0 && avatarInfo"
      >点击右侧按钮查看数字头像（普通）</view
    >
    <view
      class="mt-[18rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
      v-else-if="showType === 1 && avatarInfo"
      >点击左侧按钮查看电子票（普通）</view
    >

    <view class="m-auto mb-[94rpx] mt-[50rpx] w-[470rpx]">
      <view class="mb-[20rpx] w-full">
        <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">剧目名称</text>
        <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
          ticketInfo.repertoireName
        }}</text>
      </view>
      <view class="w-full">
        <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">获得时间</text>
        <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
          ticketInfo.createTime
        }}</text>
      </view>
      <view
        class="flex w-full flex-col items-start justify-start"
        v-if="actorInformationList && actorInformationList.length">
        <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">演职信息</text>
        <text
          class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
          :key="index"
          v-for="(i, index) in actorInformationList"
          >{{ i.actorInformation }}</text
        >
      </view>
    </view>

    <view class="m-auto flex w-[582rpx] items-center justify-between">
      <customBtn class="m-auto" @tap="$push({ name: 'HistoryList', params: { skip: 1 } })" text="去评价" width="260" />
      <customBtn
        class="m-auto"
        type="3"
        @tap="$push({ name: 'Upgrade', params: { portfolioNo: ticketInfo.portfolioNo } })"
        text="升级收藏"
        v-if="ticketInfo.coverFront"
        width="260" />
    </view>
  </view>

  <!-- 升级 -->
  <view
    class="pageWrap2 flex min-h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat pb-[200rpx]"
    v-else>
    <!-- 导航栏 -->
    <u-navbar
      class="z-50 w-full shrink-0"
      @leftClick="backRoute"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />

    <view class="relative mb-[100rpx] h-[1000rpx] w-full">
      <template v-if="avatarInfo">
        <image
          class="absolute bottom-0 left-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperL1.svg')"
          @click.stop.prevent="showType = 0"
          mode="scaleToFill"
          v-if="showType === 1" />
        <image
          class="absolute bottom-0 left-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperL2.svg')"
          mode="scaleToFill"
          v-else-if="showType === 0" />
      </template>

      <!-- 电子票 -->
      <u-transition :show="collectionShow1" mode="fade-left">
        <view class="relative m-auto h-[1000rpx] w-screen overflow-hidden" @click.stop.prevent @touchstart.stop.prevent>
          <model
            class="modelBox topPatch absolute bottom-0 left-0 right-0 top-0 m-auto"
            type="ticket"
            :url="[ticketInfo?.upgradeImageUrl, ticketInfo?.coverReverseUrl]"
            height="1150rpx"
            ref="modelRef"
            v-if="ticketInfo?.upgradeImageUrl && ticketInfo?.coverReverseUrl" />
        </view>

        <view class="absolute bottom-[-64rpx] left-0 right-0 m-auto flex h-8 w-[622rpx] items-center justify-between">
          <image
            class="block h-8 w-8"
            :src="$iconFormat('icon/rotateR.svg')"
            @longtap="rotateModel(1)"
            @touchend="rotateStop"
            mode="scaleToFill" />
          <view
            class="relative z-20 w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100"
            >按住画面左右翻动可查看票的背面</view
          >
          <image
            class="block h-8 w-8"
            :src="$iconFormat('icon/rotateL.svg')"
            @longtap="rotateModel(2)"
            @touchend="rotateStop"
            mode="scaleToFill" />
        </view>
      </u-transition>
      <!-- 数字头像 -->
      <u-transition :show="collectionShow2" mode="fade-right">
        <view class="relative m-auto flex h-[1000rpx] w-[560rpx] items-center justify-center overflow-hidden">
          <image
            class="avatar m-auto block h-[560rpx] w-[560rpx]"
            :src="$picFormat(avatarInfo.upgradeImage)"
            mode="scaleToFill"
            v-if="avatarInfo.upgradeImage" />
        </view>
      </u-transition>

      <template v-if="avatarInfo">
        <image
          class="absolute bottom-0 right-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperR1.svg')"
          @click.stop.prevent="showType = 1"
          mode="scaleToFill"
          v-if="showType === 0" />
        <image
          class="absolute bottom-0 right-0 top-0 z-10 m-auto block h-[98rpx] w-[96rpx]"
          :src="$iconFormat('/arrow/swiperR2.svg')"
          mode="scaleToFill"
          v-else-if="showType === 1" />
      </template>
    </view>

    <!-- 电子票信息 -->
    <view class="relative m-auto mb-[40rpx] h-fit w-[706rpx]">
      <image class="block w-full" :src="$iconFormat('background/infoDailogT.png')" mode="widthFix" />

      <view
        class="relative z-10 mb-[-2rpx] mt-[-4rpx] h-fit w-full border-l-[4rpx] border-r-[4rpx] border-[#BC93FF] bg-[#100636] pb-[2rpx] pl-[38rpx] pr-[38rpx] pt-[2rpx]">
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >剧目名称</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            ticketInfo.repertoireName || '-'
          }}</text>
        </view>
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >剧场名称</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            ticketInfo.theaterName || '-'
          }}</text>
        </view>
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >演出时间</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            ticketInfo.time || '-'
          }}</text>
        </view>
        <view class="mb-[20rpx] flex flex-col items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >演职信息</text
          >
          <text
            class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
            :key="index"
            v-for="(i, index) in actorInformationList"
            >{{ i.actorInformation || '-' }}</text
          >
        </view>
        <view class="flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >获得时间</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            ticketInfo.createTime || '-'
          }}</text>
        </view>
      </view>

      <image class="block w-full" :src="$iconFormat('background/infoDailogB.png')" mode="widthFix" />
    </view>

    <view class="m-auto mb-0 mt-0 flex w-[582rpx] items-center justify-between">
      <customBtn class="m-auto" @tap="handleShare(showType)" text="分享一下" width="260" />
      <customBtn
        class="m-auto"
        type="3"
        @tap="$push({ name: 'HistoryList', params: { skip: 1 } })"
        text="去评价"
        width="260" />
    </view>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $collectionDetails } from '@/api/scan'
  import { $userReceivingRecordsText } from '@/api/ticket'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import model from '@/components/Model.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $iconFormat, $picFormat, $push, $replaceAll } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()

  const showType = ref(0) // 显示类型 0：电子票 1：数字头像
  const collectionShow1 = ref(true) // 电子票显示
  const collectionShow2 = ref(false) // 数字头像显示

  const isUpdate = ref(false) // 是否升级

  const ticketInfo = ref<any>('') // 电子票详情

  const avatarInfo = ref<any>('') // 数字头像

  const actorInformationList = ref<any>([])

  watch(showType, (val: any) => {
    if (val === 0) {
      uni.$u.sleep(300).then(() => {
        collectionShow1.value = true
      })
      collectionShow2.value = false
    } else if (val === 1) {
      collectionShow1.value = false
      uni.$u.sleep(300).then(() => {
        collectionShow2.value = true
      })
    }
  })

  onLoad(async () => {
    if (globalStore.scanResult?.length) {
      let ticketId: any

      await globalStore.scanResult.map((i: any) => {
        if (i.badgeType === 1) ticketId = i.id

        if (i.badgeType === 2) avatarInfo.value = i
      })

      await $collectionDetails(ticketId).then((res: any) => {
        if (res.data.image) res.data.imageUrl = $picFormat(res.data.image)
        if (res.data.upgradeImage) res.data.upgradeImageUrl = $picFormat(res.data.upgradeImage)
        if (res.data.coverReverse) res.data.coverReverseUrl = $picFormat(res.data.coverReverse)

        if (res.data.time) res.data.time = dayjs(res.data.time).format('YYYY-MM-DD HH:mm')
        if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY-MM-DD HH:mm')

        isUpdate.value = res.data.upgradeStatus === 1 ? true : false

        ticketInfo.value = res.data
      })

      await $userReceivingRecordsText({
        userReceivingRecordsId: globalStore.scanResult[0].id,
      }).then((res: any) => {
        actorInformationList.value = res.data
      })
    }
  })

  /* 旋转模型 */
  const rotateModel = (type: number) => {
    app.$refs.modelRef.rotateModel(type)
  }
  /* 停止旋转模型 */
  const rotateStop = () => {
    app.$refs.modelRef.rotateStop()
  }

  /* 后退页面 */
  const backRoute = () => {
    $replaceAll({ name: 'Personal' })
  }

  /* 分享 */
  const handleShare = (type: number) => {
    switch (type) {
      case 0:
        $push({ name: 'TicketSave', params: { id: ticketInfo.value.id, isUpdate: 1 } })
        break
      case 1:
        $push({ name: 'AvatarSave', params: { id: avatarInfo.value.id, isUpdate: 1 } })
        break
    }
  }
</script>

<style lang="scss" scoped>
  .pageWrap2 {
    background-image: url($icon + 'background/scanBg.webp');

    .infoBox {
      background-image: url($icon + 'background/infoDailog2.webp');
    }
  }
</style>
