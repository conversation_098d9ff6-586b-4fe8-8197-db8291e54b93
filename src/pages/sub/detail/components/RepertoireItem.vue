<template>
  <template :key="item.id" v-for="(item, index) in list as any">
    <view
      class="repertoireItem relative mb-2.5 flex items-stretch justify-start"
      @tap="$push({ name: 'RepertoireDetail', params: { id: item.repertoireId } })"
      v-if="(index < 5 && !isOld) || isShowMore">
      <!-- 海报 -->
      <view class="relative h-[240rpx] w-[180rpx] shrink-0">
        <u-image
          :src="item.coverPicture"
          bgColor="transparent"
          height="240rpx"
          mode="aspectFill"
          radius="16rpx"
          width="180rpx"></u-image>

        <!-- 好评率 -->
        <view
          class="rate absolute left-[10rpx] top-[10rpx] flex h-5 min-w-[110rpx] items-center justify-start rounded-es-[10rpx] rounded-se-[20rpx] rounded-ss-[10rpx] pl-2.5 pr-1">
          <image class="mr-1 block h-3 w-3 shrink-0" :src="$iconFormat('icon/thumbsUp0.svg')" mode="scaleToFill" />
          <text class="text-[22rpx] font-normal leading-[28rpx] text-w-100">{{ item.likeRatio || 0 }}%</text>
        </view>
      </view>

      <!-- 剧目信息 -->
      <view class="ml-2.5 flex grow flex-col items-stretch justify-start">
        <!-- 标题 -->
        <view class="relative mb-[10rpx] w-full shrink-0">
          <text
            class="mr-1 mt-[4rpx] inline-block h-4 w-fit shrink-0 rounded-[8rpx] bg-gradient-to-r from-[#BC98E2] to-[#9C75D3] pl-[6rpx] pr-[6rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
            v-if="item.repertoireLabelName"
            >{{ item.repertoireLabelName }}</text
          >
          <text class="break-all font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100">{{
            item.repertoireName
          }}</text>
        </view>

        <!-- 上映时间 -->
        <view class="mb-[10rpx] grow font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
          >{{ item.startTime }}-{{ item.endTime }}</view
        >

        <!-- 评价 -->
        <view class="flex shrink-0 items-center justify-start pb-[10rpx]">
          <view class="mr-[10rpx] flex items-center justify-start" v-if="item.commentCount">
            <u-avatar
              class="avatar"
              :defaultUrl="$iconFormat('avatar.jpg')"
              :key="sonIndex"
              :src="$picFormat(son)"
              size="40rpx"
              v-for="(son, sonIndex) in item.userAvatar"></u-avatar>
            <image
              class="ml-[-16rpx] block h-5 w-5"
              :src="$iconFormat('icon/more.svg')"
              mode="scaleToFill"
              v-if="item.commentCount > 5" />
          </view>

          <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100"
            >{{ item.commentCount }}人评价过</text
          >
        </view>
      </view>
    </view></template
  >
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat, $picFormat, $push } from '@/utils/methods'

  const props = defineProps({
    list: { type: Array, default: () => [] },
    isShowMore: { type: Boolean, default: false },
    isOld: { type: Boolean, default: false },
  })
</script>

<style lang="scss" scoped>
  .repertoireItem {
    &:nth-last-child(2) {
      margin-bottom: 40rpx;
    }

    .rate {
      background: linear-gradient(270deg, rgb(255 165 36 / 60%) 0%, rgb(255 130 39 / 60%) 100%);
    }
  }

  .avatar {
    margin-left: -16rpx;
    border: 2rpx solid #fff;

    &:first-child {
      margin-left: 0;
    }
  }
</style>
