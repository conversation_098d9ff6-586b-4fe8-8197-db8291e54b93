<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <!-- 导航栏 -->
      <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" />

      <!-- 海报 -->
      <u-swiper
        class="swiperBg w-full"
        :list="detail.pictures"
        autoplay
        circular
        height="400rpx"
        v-if="detail.pictures && detail.pictures.length"></u-swiper>
      <view class="h-[400rpx] w-full" v-else></view>

      <!-- 基本信息 -->
      <view
        class="relative z-10 m-auto mb-5 mt-[-72rpx] w-[702rpx] rounded-[20rpx] bg-boxBg p-2.5 shadow-boxShadow backdrop-blur-[5rpx]">
        <view class="mb-2.5 flex items-center justify-between">
          <!-- 名称 -->
          <text class="mr-2.5 grow font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{
            detail.name || '-'
          }}</text>

          <!-- 关注按钮 -->
          <followBtn
            :followStatus="detail.fansFlag === 1 ? true : false"
            @changeFollowStatus="handleSwitchFollow"
            height="50rpx"
            width="150rpx" />
        </view>

        <view class="flex items-start justify-between">
          <!-- 地址 -->
          <view class="mr-2.5 flex grow items-start justify-start">
            <image class="mr-1 block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/address.svg')" mode="scaleToFill" />
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-80">{{
              detail.address || '-'
            }}</text>
          </view>

          <!-- 关注人数 -->
          <view class="flex shrink-0 items-center justify-end">
            <image
              class="mr-1 block h-[28rpx] w-[28rpx]"
              :src="$iconFormat('icon/favouriteOn.svg')"
              v-if="detail.fansFlag === 1" />
            <image class="mr-1 block h-[28rpx] w-[28rpx]" :src="$iconFormat('icon/favouriteOff.svg')" v-else />
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-80"
              >{{ detail.focusNumber || 0 }}关注</text
            >
          </view>
        </view>
      </view>

      <!-- 点赞率 -->
      <view class="m-auto mb-5 flex w-[702rpx] items-center justify-start rounded-[20rpx] bg-w-10 p-2.5">
        <view class="grow">
          <!-- 赞 -->
          <view class="mb-2.5 flex items-center justify-start">
            <image
              class="mr-[12rpx] block h-[26rpx] w-[26rpx] shrink"
              :src="$iconFormat('icon/thumbsUp2.svg')"
              mode="scaleToFill" />
            <text class="shrink font-Regular text-[24rpx] font-normal leading-[32rpx] text-glod2">赞</text>
            <view class="ml-2.5 h-1 w-[194rpx] rounded-full bg-[#363148]">
              <view class="h-full rounded-full bg-[#5E5A6C]" :style="{ width: (detail.likeRate || 0) + '%' }"></view>
            </view>
          </view>

          <!-- 踩 -->
          <view class="flex items-center justify-start">
            <image
              class="mr-[12rpx] block h-[26rpx] w-[26rpx] shrink"
              :src="$iconFormat('icon/thumbsUp4.svg')"
              mode="scaleToFill" />
            <text class="shrink font-Regular text-[24rpx] font-normal leading-[32rpx] text-gray1">踩</text>
            <view class="ml-2.5 h-1 w-[194rpx] rounded-full bg-[#363148]">
              <view class="h-full rounded-full bg-[#5E5A6C]" :style="{ width: (detail.dislikeRate || 0) + '%' }"></view>
            </view>
          </view>
        </view>

        <!-- 好评率 -->
        <text class="shrink font-Medium text-[28rpx] font-medium leading-[40rpx] text-glod2"
          >“{{ detail.likeRate || 0 }}%的点赞率”</text
        >
      </view>

      <!-- 热门剧目 -->
      <view class="m-auto mb-[20rpx] flex w-[702rpx] items-center justify-between">
        <text class="font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">热门剧目</text>

        <!-- 排序类型 -->
        <view
          class="relative flex h-5 w-[140rpx] items-center justify-center rounded-[8rpx] border-[2rpx] border-solid border-w-40"
          @tap="dropMenuController1 = !dropMenuController1">
          <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">{{ orderByTxt1 }}</text>
          <image
            class="ml-[6rpx] block h-[10rpx] w-[18rpx] shrink-0"
            :src="$iconFormat('arrow/down2.svg')"
            mode="scaleToFill" />

          <u-transition :show="dropMenuController1" mode="fade">
            <view
              class="absolute left-[50%] top-[60rpx] z-20 h-fit w-[160rpx] translate-x-[-50%] overflow-hidden rounded-[20rpx]">
              <view
                class="h-[60rpx] w-full bg-[#1E1A2A] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
                :key="index"
                @tap="handleSelOrderBy1(item)"
                v-for="(item, index) in orderList1"
                >{{ item.name }}</view
              >
            </view>
          </u-transition>
        </view>
      </view>
      <view class="m-auto mb-5 w-[702rpx] rounded-[20rpx] bg-w-10 p-2.5">
        <repertoireItem
          :isShowMore="isShowMore"
          :list="repertoireList"
          v-if="repertoireList && repertoireList.length" />

        <view
          class="m-auto flex h-[80rpx] w-[304rpx] items-center justify-center rounded-full border-[2rpx] border-solid border-w-20"
          :class="{ 'mb-[40rpx]': oldRepertoireList && oldRepertoireList.length }"
          @tap="isShowMore = !isShowMore"
          v-if="repertoireList && repertoireList.length > 5">
          <text class="font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
            >{{ isShowMore ? '收起全部热门剧目' : '展开全部热门剧目' }}
          </text>
          <image class="block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
        </view>

        <repertoireItem
          :isOld="true"
          :isShowMore="isShowOld"
          :list="oldRepertoireList"
          v-if="oldRepertoireList && oldRepertoireList.length" />

        <view
          class="m-auto flex h-[80rpx] w-[304rpx] items-center justify-center rounded-full border-[2rpx] border-solid border-w-20"
          :class="{ 'mt-[40rpx]': repertoireList && repertoireList.length }"
          @tap="isShowOld = !isShowOld"
          v-if="oldRepertoireList && oldRepertoireList.length">
          <text class="font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
            >{{ isShowOld ? '收起往期热门剧目' : '展开往期热门剧目' }}
          </text>
          <image class="block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
        </view>

        <!-- 空状态 -->
        <u-empty
          class="emptyHidePic"
          text="暂无热门剧目"
          v-if="
            (!repertoireList || !repertoireList.length) && (!oldRepertoireList || !oldRepertoireList.length)
          "></u-empty>
      </view>

      <!-- 橱窗展示  -->
      <template v-if="theaterDisplay && theaterDisplay.length">
        <view class="m-auto mb-[20rpx] flex w-[702rpx] items-center justify-between">
          <text class="font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100">橱窗展示</text>
          <view class="flex items-center justify-end" @tap="$push({ name: 'Showcase', params: { theaterId: id } })">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">查看全部</text>
            <image class="ml-[4rpx] block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>
        <scroll-view class="mb-5 h-[300rpx] w-full" scrollX="true">
          <view class="box-border flex h-full w-fit items-center justify-start pl-3 pr-3">
            <view
              class="mr-2.5 flex h-[300rpx] w-[208rpx] flex-col items-center justify-center rounded-[20rpx] bg-w-10 last:mr-0"
              :key="index"
              v-for="(i, index) in theaterDisplay">
              <view class="mb-[14rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                >纪念徽章</view
              >
              <u-image
                :src="$picFormat(i.souvenirBadgeUrl)"
                @click="$previewImage([$picFormat(i.souvenirBadgeUrl)], 0)"
                bgColor="transparent"
                height="140rpx"
                mode="aspectFill"
                radius="10rpx"
                width="140rpx"></u-image>
              <view class="mt-3 text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{
                dayjs(i.auditPassTime).format('YYYY年MM月DD日')
              }}</view>
            </view>
          </view>
        </scroll-view>
      </template>

      <!-- 问大家 -->
      <view class="m-auto mb-5 w-[702rpx] rounded-[20rpx] bg-w-10 p-2.5 pb-0">
        <!-- 无提问 -->
        <view class="mb-[20rpx] flex items-center justify-between pb-2.5" v-if="!issueList || !issueList.length">
          <view class="flex items-center justify-start">
            <text class="mr-[10rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">问大家</text>
          </view>

          <view class="flex items-center justify-end" @tap="$push({ name: 'AskList', params: { id, type: 1 } })">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60"
              >剧场好不好，问问已去过的人</text
            >
            <image class="ml-[4rpx] block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>

        <!-- 有问题 -->
        <view
          class="mb-[20rpx] flex items-center justify-between border-b-[1rpx] border-solid border-w-10 pb-[12rpx]"
          v-else-if="issueList && issueList.length">
          <view class="flex items-center justify-start">
            <text class="mr-[10rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">问大家</text>
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">({{ issueNum }})</text>
          </view>

          <view class="flex items-center justify-end" @tap="$push({ name: 'AskList', params: { id, type: 1 } })">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">查看全部</text>
            <image class="ml-[4rpx] block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>

        <!-- 问题预览 -->
        <view class="w-full pb-2.5" v-if="issueList && issueList.length">
          <view
            class="mb-[30rpx] flex items-center justify-start last:mb-0"
            :key="item.id"
            @tap="handleCheckDetail(item)"
            v-for="item in issueList">
            <image class="mr-[10rpx] block h-4 w-4 shrink-0" :src="$iconFormat('icon/ask1.svg')" mode="scaleToFill" />
            <text
              class="mr-[10rpx] line-clamp-1 grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
              >{{ item.content }}</text
            >
            <text class="shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
              >{{ item.replyCount }}个回答</text
            >
          </view>
        </view>
      </view>

      <!-- 剧场评价 -->
      <view class="m-auto mb-[20rpx] flex w-[702rpx] items-center justify-between">
        <view class="flex items-center justify-start">
          <text class="mr-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">剧场评价</text>
          <!-- 排序类型 -->
          <view
            class="relative flex h-5 w-[140rpx] items-center justify-center rounded-[8rpx] border-[2rpx] border-solid border-w-40"
            @tap="dropMenuController2 = !dropMenuController2">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">{{ orderByTxt2 }}</text>
            <image
              class="ml-[6rpx] block h-[10rpx] w-[18rpx] shrink-0"
              :src="$iconFormat('arrow/down2.svg')"
              mode="scaleToFill" />

            <u-transition :show="dropMenuController2" mode="fade">
              <view
                class="absolute left-[50%] top-[60rpx] z-20 h-fit w-[160rpx] translate-x-[-50%] overflow-hidden rounded-[20rpx]">
                <view
                  class="h-[60rpx] w-full bg-[#1E1A2A] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
                  :key="index"
                  @tap="handleSelOrderBy2(item)"
                  v-for="(item, index) in orderList2"
                  >{{ item.name }}</view
                >
              </view>
            </u-transition>
          </view>
        </view>

        <u-button
          class="followBtn"
          type="primary"
          :customStyle="{ width: '170rpx', height: '60rpx', ...btnStyle1 }"
          :hairline="false"
          @click="$push({ name: 'HistoryList' })"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          iconColor="#282656"
          shape="circle"
          >写评价</u-button
        >
      </view>

      <view class="m-auto mb-5 w-[702rpx]">
        <commentItem
          :commentType="1"
          :hideMore="hideMore"
          :list="commentList"
          :theaterId="id"
          @updateCommentList="updateCommentList" />

        <!-- 空状态 -->
        <view class="mt-[100rpx] w-full" v-if="!commentList || !commentList.length">
          <u-empty
            :icon="$iconFormat('empty/comment.svg')"
            height="322rpx"
            mode="data"
            text="点击下方中央扫一扫，扫描实体票后评价"
            width="472rpx"></u-empty>
        </view>

        <view
          class="m-auto flex h-[80rpx] w-[304rpx] items-center justify-center rounded-full border-[2rpx] border-solid border-w-20"
          @tap="handleShowMoreComment"
          v-if="commentList && commentList.length > 2 && hideMore">
          <text class="font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60">展开全部评价</text>
          <image class="block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
        </view>
      </view>

      <view class="h-[100rpx] w-full"> </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="Tabulation" />
  </view>

  <askDetail :popupContaoller="popupContaoller" :selAsk="selAsk" @close="handleCloseDetailPopup" v-if="selAsk" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import repertoireItem from './components/RepertoireItem.vue'
  import { $issueCountByTheater, $issueList } from '@/api/ask'
  import { $userTreasureSave } from '@/api/base'
  import { $commentListByPage } from '@/api/comment'
  import { $repertoireByTheaterId } from '@/api/repertoire'
  import { $theaterDisplay } from '@/api/showcase'
  import { $theaterDetails } from '@/api/theater'
  import askDetail from '@/components/Ask/Detasil.vue'
  import followBtn from '@/components/Btn/FollowBtn.vue'
  import commentItem from '@/components/Item/CommentItem.vue'
  import network from '@/components/Network.vue'
  import tabbar from '@/components/Tabbar.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $previewImage, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj
  const upOpt = computed(() => {
    let obj = {
      ...upOpt3.value,
      noMoreSize: !hideMore.value ? 1 : 99999,
    }
    return obj
  })

  const id = ref(0) // 剧场id
  const detail = ref<any>('') // 剧场详情

  const dropMenuController1 = ref(false) // 下拉框控制器1
  const orderList1 = ref([
    { name: '评分人数', string: 'commentCount' },
    { name: '点赞率', string: 'likeRatio' },
  ])
  const orderBy1 = ref('repertoireId') // 排序字段1
  const orderByTxt1 = ref('默认排序') // 排序字段名1
  const isAsc = ref('asc') // 倒序顺序

  const repertoireList = ref<any>([]) // 热门剧目列表
  const oldRepertoireList = ref<any>([]) // 往期热门剧目列表
  const isShowMore = ref(false) // 是否展示更多
  const isShowOld = ref(false) // 是否展示往期

  const theaterDisplay = ref<any>([]) // 剧场橱窗

  const issueNum = ref(0) // 问题数量
  const issueList = ref<any>([]) // 问大家
  const popupContaoller = ref(false) // 提问弹窗控制器
  const selAsk = ref<any>('') // 选中的问题

  const dropMenuController2 = ref(false) // 下拉框控制器2
  const orderList2 = ref([
    { name: '时间正序', string: 'createTime', isAsc: 'asc' },
    { name: '时间倒序', string: 'createTime', isAsc: 'desc' },
  ])
  const orderBy2 = ref('top desc,createTime') // 排序字段2
  const orderByTxt2 = ref('默认排序') // 排序字段名2
  const isAsc2 = ref('desc') // 正序倒序

  const commentList = ref<any>([]) // 剧场评论
  const hideMore = ref(true) // 是否折叠

  /* 关注按钮样式 */
  const btnStyle1 = ref({
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '26rpx',
    fontWeight: 500,
    padding: 0,
  })

  onLoad((params: any) => {
    if (params.id) id.value = params.id - 0
    if (params.scene) id.value = params.scene - 0

    uni.$on('updateAsk', getIssueList)
    uni.$on('updateComment', () => {
      mescrollObj.value.resetUpScroll()
    })
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll
    mescrollObj.value.lockUpScroll(true)

    if (mescroll.num == 1) {
      /* 剧场详情 */
      getTheaterDetails()
      /* 热门剧目 */
      getRepertoireByTheaterId()
      /* 橱窗 */
      getTheaterDisplay()
      /* 问大家 */
      getIssueList()
    }

    /* 剧场评价 */
    getCommentListByPage(mescroll)
  }

  /* **************************************************************************************************** */

  /* 获取剧场详情 */
  const getTheaterDetails = () => {
    $theaterDetails({ id: id.value, userId: userInfo.value.id }).then((res: any) => {
      if (res.data.pictures) {
        res.data.pictures = res.data.pictures.split(',').map((i: string) => $picFormat(i))
        if (res.data.coverPicture) res.data.pictures.unshift($picFormat(res.data.coverPicture))
      } else {
        res.data.pictures = []
        if (res.data.coverPicture) res.data.pictures.unshift($picFormat(res.data.coverPicture))
      }

      if ((res.data.likeCount || res.data.likeCount === 0) && (res.data.dislikeCount || res.data.dislikeCount === 0)) {
        const total = res.data.likeCount + res.data.dislikeCount
        res.data.likeRate = Math.ceil((res.data.likeCount / total) * 100)
        res.data.dislikeRate = Math.ceil((res.data.dislikeCount / total) * 100)
      }

      detail.value = res.data
    })
  }

  /* 关注切换 */
  const handleSwitchFollow = (status: boolean) => {
    $userTreasureSave({
      userId: userInfo.value.id,
      merchantId: detail.value.merchantId,
      theaterId: id.value,
    }).then((res: any) => {
      if (status) {
        detail.value.fansFlag = 1
        detail.value.focusNumber++
      } else {
        detail.value.fansFlag = 0
        if (detail.value.focusNumber > 0) detail.value.focusNumber--
      }
    })
  }

  /* **************************************************************************************************** */

  /* 切换排序字段1 */
  const handleSelOrderBy1 = (orderItem: any) => {
    if (orderBy1.value !== orderItem.string) {
      orderBy1.value = orderItem.string
      orderByTxt1.value = orderItem.name
      isAsc.value = 'desc'
    } else {
      orderBy1.value = 'repertoireId'
      orderByTxt1.value = '默认排序'
      isAsc.value = 'asc'
    }

    /* 热门剧目 */
    getRepertoireByTheaterId()
  }

  /* 获取热门剧目 */
  const getRepertoireByTheaterId = () => {
    $repertoireByTheaterId({
      pageNum: 1,
      pageSize: 9999,
      userId: userInfo.value.id,
      theaterId: id.value,
      orderByColumn: orderBy1.value ? orderBy1.value + ' ' + isAsc.value + ',repertoireId' : undefined,
      // isAsc: isAsc.value,
    }).then((res: any) => {
      let oldList: any = []
      let newList: any = []

      res.data.rows.map((i: any) => {
        i.id = i.repertoireId
        if (i.coverPicture) i.coverPicture = $picFormat(i.coverPicture)
        if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY.MM.DD')
        if (i.endTime) {
          if (dayjs().isAfter(dayjs(i.endTime))) {
            // 当前时间是在剧目结束时间之后
            i.endTime = dayjs(i.endTime).format('YYYY.MM.DD')
            oldList.push(i)
          } else {
            i.endTime = dayjs(i.endTime).format('YYYY.MM.DD')
            newList.push(i)
          }
        }
      })

      repertoireList.value = newList
      oldRepertoireList.value = oldList
    })
  }

  /* **************************************************************************************************** */

  /* 获取橱窗展示 */
  const getTheaterDisplay = () => {
    $theaterDisplay({ theaterId: id.value }).then((res: any) => {
      theaterDisplay.value = res.data
    })
  }

  /* **************************************************************************************************** */

  /* 获取问大家 */
  const getIssueList = () => {
    $issueCountByTheater(id.value).then((res: any) => {
      issueNum.value = res.data
    })

    $issueList({
      pageNum: 1,
      pageSize: 2,
      theaterId: id.value,
    }).then((res: any) => {
      issueList.value = res.data.rows
    })
  }

  /* 查看问答详情 */
  const handleCheckDetail = (item: any) => {
    selAsk.value = item
    popupContaoller.value = true
  }

  /* 关闭详情 */
  const handleCloseDetailPopup = () => {
    selAsk.value = {}
    popupContaoller.value = false
  }

  /* **************************************************************************************************** */

  /* 切换排序字段2 */
  const handleSelOrderBy2 = (orderItem: any) => {
    if (orderBy2.value !== orderItem.string || isAsc2.value !== orderItem.isAsc || !orderBy2.value) {
      orderBy2.value = orderItem.string
      orderByTxt2.value = orderItem.name
      isAsc2.value = orderItem.isAsc
    } else {
      orderBy2.value = 'top desc,createTime'
      orderByTxt2.value = '默认排序'
      isAsc2.value = 'desc'
    }

    /* 剧场评价 */
    mescrollObj.value.resetUpScroll()
  }

  /* 显示更多评论 */
  const handleShowMoreComment = () => {
    hideMore.value = false
    mescrollObj.value.lockUpScroll(false)
  }

  /* 获取剧场评价 */
  const getCommentListByPage = (mescroll: any) => {
    $commentListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      flag: 2,
      userId: userInfo.value.id,
      theaterId: id.value,
      orderByColumn: orderBy2.value ? orderBy2.value + ' ' + isAsc2.value + ',id' : undefined,
      // isAsc: isAsc2.value,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY年MM月DD日')
          i.isOpen = false
          if (i.theaterContent) i.text = i.theaterContent.replace(/<[^>]+>/g, '')
        })

        if (mescroll.num == 1) commentList.value = [] // 第一页需手动制空列表

        commentList.value = commentList.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
        // if (!hideMore.value) mescroll.endBySize(curPageData.length, res.data.total)
        // else mescroll.endSuccess(1, false)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 更新评论列表 */
  const updateCommentList = (list: any) => {
    commentList.value = list
  }
</script>

<style lang="scss" scoped>
  .evaluateBtn {
    flex-shrink: 0;

    &:deep(.u-icon) {
      .u-icon__icon {
        margin-right: 10rpx;
        font-size: 18rpx !important;
        font-weight: bold !important;
      }
    }
  }

  :deep(.mescroll-wxs-content) {
    padding-bottom: 100rpx;
  }
</style>
