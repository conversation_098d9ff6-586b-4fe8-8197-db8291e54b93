<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder>
      <template #center>
        <view class="flex w-[574rpx] pr-[80rpx]">
          <!-- 剧场/剧目封面图 -->
          <u-image
            class="shrink-0"
            :src="detail.avatarUrl"
            bgColor="transparent"
            height="72rpx"
            mode="aspectFill"
            radius="50%"
            v-if="detail.avatarUrl"
            width="72rpx"></u-image>
          <view class="ml-2 grow">
            <!-- 剧场/剧目名 -->
            <view class="mb-[2rpx] line-clamp-1 font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{
              type === 1 ? detail.theaterName : detail.repertoireName
            }}</view>
            <!-- 发布时间 -->
            <view class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ detail.createTime }}</view>
          </view>
        </view>
      </template>
    </u-navbar>

    <mescroll-uni
      class="h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <!-- 轮播图 -->
      <swiper
        class="swiper m-auto mt-[10rpx] h-[916rpx] w-[702rpx] rounded-[20rpx]"
        autoplay
        circular
        indicatorActiveColor="rgb(255 255 255 / 80%)"
        indicatorColor="rgb(255 255 255 / 30%)"
        indicatorDots
        v-if="detail.images && detail.images.length">
        <swiper-item :key="index" v-for="(i, index) in detail.images">
          <u-image
            :src="i"
            @tap="$previewImage(detail.images, index)"
            bgColor="transparent"
            height="888rpx"
            mode="aspectFill"
            radius="20rpx"
            width="702rpx"></u-image>
        </swiper-item>
      </swiper>

      <view class="m-auto mt-2 w-[702rpx] pb-[100rpx]">
        <!-- 标题 -->
        <view class="mb-[10rpx] w-full break-all font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
          detail.title
        }}</view>
        <!-- 内容 -->
        <view class="w-full break-all font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-80">{{
          detail.body
        }}</view>
        <!-- <u-parse :content="detail.body" :tagStyle="tagStyle"></u-parse> -->

        <!-- 点赞 -->
        <template v-if="type === 2">
          <view class="m-auto mt-[38rpx] w-[100rpx]" @tap="handleKudos" v-if="detail.kudosStatus === 1">
            <image class="block h-[100rpx] w-[100rpx]" :src="$iconFormat('icon/thumbsUp6.svg')" mode="scaleToFill" />
            <view class="mt-[12rpx] w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
              >取消赞</view
            >
          </view>
          <view class="m-auto mt-[38rpx] w-[112rpx]" @tap="handleKudos" v-else>
            <image class="block h-[116rpx] w-[112rpx]" :src="$iconFormat('icon/thumbsUp5.svg')" mode="aspectFill" />
            <view class="w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-[#FF9723]"
              >赞一个</view
            >
          </view>
        </template>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $dynamicDetails, $dynamicKudos } from '@/api/dynamic'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $previewImage, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const type = ref(1) // 1：剧场动态 2：剧目动态
  const id = ref(0) // 动态id
  const detail = ref<any>('') // 动态详情

  /* 动态详情样式 */
  const tagStyle = ref({
    '*': 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
    div: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
    p: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
    span: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
    ol: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
    ul: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
    li: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #FFFFFF; line-height: 40rpx;',
  })

  onLoad((params: any) => {
    id.value = params.id - 0
    type.value = params.type - 0
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    /* 剧场详情 */
    $dynamicDetails(id.value, userInfo.value.id)
      .then((res: any) => {
        if (res.data.theaterCoverPicture) res.data.theaterCoverPicture = $picFormat(res.data.theaterCoverPicture)
        if (res.data.repertoireCoverPicture)
          res.data.repertoireCoverPicture = $picFormat(res.data.repertoireCoverPicture)
        res.data.avatarUrl = type.value === 1 ? res.data.theaterCoverPicture : res.data.repertoireCoverPicture
        if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY年MM月DD日')

        let swiperList: string[] = []
        if (res.data.coverImage) swiperList.push(res.data.coverImage)
        if (res.data.images) swiperList = swiperList.concat(res.data.images.split(','))
        res.data.images = swiperList.map((i: any) => $picFormat(i))

        detail.value = res.data

        mescroll.endSuccess(1, false)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 点赞 */
  const handleKudos = () => {
    $dynamicKudos({
      dynamicId: id.value,
      type: detail.value.kudosStatus === 1 ? undefined : 1,
      userId: userInfo.value.id,
    }).then((res: any) => {
      uni.$emit('updateDynamic')
      mescrollObj.value.resetUpScroll()
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/ask.webp');

    .swiper {
      &:deep(.wx-swiper-dots) {
        bottom: 0 !important;

        .wx-swiper-dot {
          width: 16rpx;
          height: 8rpx;
          background: #fff;
          border-radius: 4rpx;
          opacity: 0.3;
        }

        .wx-swiper-dot-active {
          width: 40rpx;
          height: 8rpx;
          background: #fff;
          border-radius: 4rpx;
          opacity: 0.8;
        }
      }
    }
  }
</style>
