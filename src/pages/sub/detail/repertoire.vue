<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="剧目详情"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="h-[1rpx] w-full shrink-0 bg-w-10"></view>

    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="m-auto w-[702rpx] pt-5">
        <!-- 基本信息 -->
        <view class="baseInfo relative mb-2 flex items-start justify-start">
          <z-swiper class="swiperW" :options="options" v-model="detail.pictures">
            <z-swiper-item :customStyle="slideCustomStyle" :key="index" v-for="(item, index) in detail.pictures">
              <u-image
                :src="item"
                @click="$previewImage(detail.pictures, index)"
                bgColor="transparent"
                height="240rpx"
                mode="aspectFill"
                radius="10rpx"
                width="160rpx"></u-image>
            </z-swiper-item>
          </z-swiper>

          <view class="ml-2.5 grow">
            <view class="mb-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{
              detail.name || '-'
            }}</view>

            <view class="flex items-center justify-start">
              <image
                class="mr-1 block h-[28rpx] w-[28rpx]"
                :src="$iconFormat('icon/favouriteOn.svg')"
                v-if="detail.fansFlag === 1" />
              <image class="mr-1 block h-[28rpx] w-[28rpx]" :src="$iconFormat('icon/favouriteOff.svg')" v-else />
              <text class="ml-[6rpx] font-Regular text-[24rpx] font-normal leading-[28rpx] text-w-60"
                >{{ detail.focusNumber || 0 }}关注</text
              >
            </view>
          </view>

          <!-- 关注 -->
          <followBtn
            :followStatus="detail.fansFlag === 1 ? true : false"
            @changeFollowStatus="handleSwitchFollow"
            height="50rpx"
            width="150rpx" />
        </view>

        <!-- 相关剧场 -->
        <scroll-view
          class="mb-2.5 h-[100rpx] w-full"
          :scrollIntoView="scrollToView"
          enableFlex
          scrollWithAnimation
          scrollX
          v-if="detail.repertoireInfoList && detail.repertoireInfoList.length">
          <view class="flex w-fit items-start justify-start">
            <view
              class="mr-2.5 w-fit shrink-0 rounded-[8rpx] bg-w-10 pb-[12rpx] pl-[30rpx] pr-[30rpx] pt-[12rpx] last:mr-0"
              :class="{ active: selTheaterId == item.id }"
              :id="'date' + index"
              :key="item.id"
              @tap="handleSwitchTheeater(item)"
              v-for="(item, index) in detail.repertoireInfoList">
              <view class="font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100">{{
                item.theaterName || '-'
              }}</view>
              <view class="text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
                >{{ dayjs(item.startTime).format('MM.DD') }}-{{ dayjs(item.endTime).format('MM.DD') }}</view
              >
            </view>
          </view>
        </scroll-view>

        <!-- <view class="mb-2.5 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60" v-if="playTime">约{{ playTime }}分钟（以现场为准）</view> -->

        <!-- 时间表 -->
        <view class="w-full" v-if="repertoireInfoDetailList && repertoireInfoDetailList.length">
          <template :key="index" v-for="(item, index) in repertoireInfoDetailList">
            <view
              class="mb-2.5 flex items-center justify-center border-b-[2rpx] border-solid border-w-10 pb-2.5 last:border-0"
              v-if="index < 3 || (timeListContraller && index >= 3)">
              <text class="mr-[30rpx] shrink-0 font-Regular text-[28rpx] font-normal leading-[44rpx] text-w-100">{{
                dayjs(item.startTime).format('YYYY-MM-DD')
              }}</text>
              <text class="mr-[20rpx] shrink-0 font-Regular text-[28rpx] font-normal leading-[44rpx] text-w-100">{{
                dayjs(item.startTime).format('dddd')
              }}</text>
              <text class="grow font-Regular text-[28rpx] font-normal leading-[44rpx] text-w-100">{{
                dayjs(item.startTime).format('HH:mm')
              }}</text>
              <!-- -{{ dayjs(item.endTime).format('HH:mm') }} -->

              <view
                class="flex items-center justify-center"
                @click="timeListContraller = true"
                v-if="index === 2 && repertoireInfoDetailList.length > 3 && !timeListContraller">
                <text class="font-Regular text-[28rpx] font-normal leading-[48rpx] text-[#9C75D3]">展开</text>
                <image class="block h-[40rpx] w-[40rpx]" :src="$iconFormat('arrow/down4.svg')" mode="scaleToFill" />
              </view>

              <view
                class="flex items-center justify-center"
                @click="timeListContraller = false"
                v-if="
                  index === repertoireInfoDetailList.length - 1 &&
                  repertoireInfoDetailList.length > 3 &&
                  timeListContraller
                ">
                <text class="font-Regular text-[28rpx] font-normal leading-[48rpx] text-[#9C75D3]">折叠</text>
                <image class="block h-[40rpx] w-[40rpx]" :src="$iconFormat('arrow/up1.svg')" mode="scaleToFill" />
              </view>
            </view>
          </template>
        </view>

        <!-- 点赞率 -->
        <view class="m-auto mb-5 flex w-[702rpx] items-center justify-start rounded-[20rpx] bg-w-10 p-2.5">
          <view class="grow">
            <!-- 赞 -->
            <view class="mb-2.5 flex items-center justify-start">
              <image
                class="mr-[12rpx] block h-[26rpx] w-[26rpx] shrink"
                :src="$iconFormat('icon/thumbsUp2.svg')"
                mode="scaleToFill" />
              <text class="shrink font-Regular text-[24rpx] font-normal leading-[32rpx] text-glod2">赞</text>
              <view class="ml-2.5 h-1 w-[194rpx] rounded-full bg-[#363148]">
                <view class="h-full rounded-full bg-[#5E5A6C]" :style="{ width: (detail.likeRate || 0) + '%' }"></view>
              </view>
            </view>

            <!-- 踩 -->
            <view class="flex items-center justify-start">
              <image
                class="mr-[12rpx] block h-[26rpx] w-[26rpx] shrink"
                :src="$iconFormat('icon/thumbsUp4.svg')"
                mode="scaleToFill" />
              <text class="shrink font-Regular text-[24rpx] font-normal leading-[32rpx] text-gray1">踩</text>
              <view class="ml-2.5 h-1 w-[194rpx] rounded-full bg-[#363148]">
                <view
                  class="h-full rounded-full bg-[#5E5A6C]"
                  :style="{ width: (detail.dislikeRate || 0) + '%' }"></view>
              </view>
            </view>
          </view>

          <!-- 好评率 -->
          <text class="shrink font-Medium text-[28rpx] font-medium leading-[40rpx] text-glod2"
            >“{{ detail.likeRate || 0 }}%的点赞率”</text
          >
        </view>

        <!-- 剧目简介 -->
        <view class="mb-[20rpx] flex w-full items-center justify-between" v-if="detail.introduction">
          <text class="font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">剧目简介</text>

          <text class="font-Regular text-[28rpx] font-normal leading-[48rpx] text-w-30" @tap="handleShowMore"
            >{{ isOpen ? '收起' : '展开' }}
          </text>
        </view>
        <view
          class="mb-5 w-full overflow-hidden text-[28rpx] leading-[48rpx] text-w-100"
          :class="isOpen ? 'h-fit' : 'h-[96rpx]'"
          v-if="detail.introduction">
          <u-parse :content="detail.introduction" :tagStyle="tagStyle"></u-parse>
        </view>

        <!-- 演职人员 -->
        <view
          class="mb-[20rpx] flex w-full items-center justify-between"
          v-if="detail.repertoireActorList && detail.repertoireActorList.length">
          <text class="font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">演职人员</text>

          <text class="font-Regular text-[28rpx] font-normal leading-[48rpx] text-w-30" @tap="isShow = !isShow">{{
            isShow ? '收起' : '展开'
          }}</text>
        </view>
        <scroll-view
          class="m-auto mb-5 h-[344rpx] w-full"
          enableFlex
          scrollWithAnimation
          scrollX
          v-if="detail.repertoireActorList && detail.repertoireActorList.length">
          <view class="flex h-full w-fit items-center justify-start">
            <view
              class="mr-[10rpx] h-[344rpx] w-[160rpx] last:mr-0"
              :key="item.id"
              v-for="item in detail.repertoireActorList">
              <u-image
                :src="$picFormat(item.picture)"
                bgColor="transparent"
                height="240rpx"
                mode="aspectFill"
                radius="10rpx"
                width="100%"></u-image>
              <view
                class="mt-2.5 w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
                >{{ item.actorName || '-' }}</view
              >
              <view
                class="mt-[10rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-40"
                >{{ item.roleName || '-' }}</view
              >
            </view>
          </view>
        </scroll-view>

        <template v-if="isShow">
          <!-- 群演信息 -->
          <view
            class="mb-[20rpx] flex w-full items-center justify-between"
            v-if="detail.repertoireActorList2 && detail.repertoireActorList2.length">
            <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-40">群演信息</text>
          </view>
          <view class="mb-5 w-full" v-if="detail.repertoireActorList2 && detail.repertoireActorList2.length">
            <view
              class="mb-2.5 flex w-full items-start justify-start last:mb-0"
              :key="i.id"
              v-for="i in detail.repertoireActorList2">
              <view class="mr-[40rpx] shrink-0 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100">{{
                i.roleName
              }}</view>
              <view class="mr-[-22rpx] flex grow flex-wrap items-center justify-start">
                <view
                  class="mr-[22rpx] font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
                  :key="j"
                  v-for="j in i.groupPerformanceName"
                  >{{ j }}</view
                >
              </view>
            </view>
          </view>

          <!-- 主创团队 -->
          <view
            class="mb-[20rpx] flex w-full items-center justify-between"
            v-if="detail.repertoireCreativeTeamList && detail.repertoireCreativeTeamList.length">
            <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-40">主创团队</text>
          </view>
          <view
            class="mb-5 w-full"
            v-if="detail.repertoireCreativeTeamList && detail.repertoireCreativeTeamList.length">
            <view
              class="mb-2.5 flex w-full items-start justify-start last:mb-0"
              :key="i.id"
              v-for="i in detail.repertoireCreativeTeamList">
              <view
                class="mr-[40rpx] min-w-[4em] shrink-0 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
                >{{ i.functionName }}</view
              >
              <view class="mr-[-22rpx] flex grow flex-wrap items-center justify-start">
                <view
                  class="mr-[22rpx] font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
                  :key="j"
                  v-for="j in i.name"
                  >{{ j }}</view
                >
              </view>
            </view>
          </view>
        </template>
      </view>

      <!-- 橱窗展示  -->
      <template v-if="repertoireDisplay && repertoireDisplay.length">
        <view class="m-auto mb-[20rpx] flex w-[702rpx] items-center justify-between">
          <text class="font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100">橱窗展示</text>
          <view class="flex items-center justify-end" @tap="$push({ name: 'Showcase', params: { repertoireId: id } })">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">查看全部</text>
            <image class="ml-[4rpx] block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>
        <scroll-view class="mb-5 h-[300rpx] w-full" scrollX="true">
          <view class="box-border flex h-full w-fit items-center justify-start pl-3 pr-3">
            <view
              class="mr-2.5 box-border flex h-[300rpx] min-w-[208rpx] items-center justify-center rounded-[20rpx] bg-w-10 pl-2.5 pr-2.5 last:mr-0"
              :key="index"
              v-for="(i, index) in repertoireDisplay">
              <view class="flex w-fit flex-col items-center justify-center">
                <view class="mb-[14rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                  >电子票根</view
                >
                <u-image
                  :src="$picFormat(i.repertoireTicketUrl)"
                  @click="$previewImage([$picFormat(i.repertoireTicketUrl)], 0)"
                  bgColor="transparent"
                  height="140rpx"
                  mode="aspectFill"
                  radius="10rpx"
                  width="104rpx"></u-image>
                <view
                  class="mt-3 whitespace-nowrap text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                  >{{ dayjs(i.auditPassTime).format('YYYY年MM月DD日') }}</view
                >
              </view>

              <template v-if="i.digitalAvatarUrl">
                <view class="ml-2.5 mr-1 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100">+</view>

                <view class="flex w-fit flex-col items-center justify-center">
                  <view class="mb-[14rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                    >数字头像</view
                  >
                  <u-image
                    :src="$picFormat(i.digitalAvatarUrl)"
                    @click="$previewImage([$picFormat(i.digitalAvatarUrl)], 0)"
                    bgColor="transparent"
                    height="140rpx"
                    mode="aspectFill"
                    radius="10rpx"
                    width="140rpx"></u-image>
                  <view
                    class="mt-3 whitespace-nowrap text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                    >&nbsp;&nbsp;</view
                  >
                </view>
              </template>
            </view>
          </view>
        </scroll-view>
      </template>

      <!-- 问大家 -->
      <view class="m-auto mb-5 w-[702rpx] rounded-[20rpx] bg-w-10 p-2.5 pb-0">
        <!-- 无提问 -->
        <view class="mb-[20rpx] flex items-center justify-between pb-2.5" v-if="!issueList || !issueList.length">
          <view class="flex items-center justify-start">
            <text class="mr-[10rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">问大家</text>
          </view>

          <view
            class="flex items-center justify-end"
            @tap="
              $push({
                name: 'AskList',
                params: { id, type: 2, theaterName: selTheaterItem.theaterName },
              })
            ">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60"
              >剧目好不好看，问问已看过的人</text
            >
            <image class="ml-[4rpx] block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>

        <!-- 有问题 -->
        <view
          class="mb-[20rpx] flex items-center justify-between border-b-[1rpx] border-solid border-w-10 pb-[12rpx]"
          v-else-if="issueList && issueList.length">
          <view class="flex items-center justify-start">
            <text class="mr-[10rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">问大家</text>
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">({{ issueNum }})</text>
          </view>

          <view class="flex items-center justify-end" @tap="$push({ name: 'AskList', params: { id, type: 2 } })">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">查看全部</text>
            <image class="ml-[4rpx] block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>

        <!-- 问题预览 -->
        <view class="w-full pb-2.5" v-if="issueList && issueList.length">
          <view
            class="mb-[30rpx] flex items-center justify-start last:mb-0"
            :key="item.id"
            @tap="handleCheckDetail(item)"
            v-for="item in issueList">
            <image class="mr-[10rpx] block h-4 w-4 shrink-0" :src="$iconFormat('icon/ask1.svg')" mode="scaleToFill" />
            <text
              class="mr-[10rpx] line-clamp-1 grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
              >{{ item.content }}</text
            >
            <text class="shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
              >{{ item.replyCount }}个回答</text
            >
          </view>
        </view>
      </view>

      <!-- 剧目评价 -->
      <view class="m-auto mb-[20rpx] flex w-[702rpx] items-center justify-between" id="comment">
        <view class="flex items-center justify-start">
          <text class="mr-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">剧目评价</text>
          <!-- 排序类型 -->
          <view
            class="relative flex h-5 w-[140rpx] items-center justify-center rounded-[8rpx] border-[2rpx] border-solid border-w-40"
            @tap="dropMenuController = !dropMenuController">
            <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">{{ orderByTxt }}</text>
            <image
              class="ml-[6rpx] block h-[10rpx] w-[18rpx] shrink-0"
              :src="$iconFormat('arrow/down2.svg')"
              mode="scaleToFill" />

            <u-transition :show="dropMenuController" mode="fade">
              <view
                class="absolute left-[50%] top-[60rpx] z-20 h-fit w-[160rpx] translate-x-[-50%] overflow-hidden rounded-[20rpx]">
                <view
                  class="h-[60rpx] w-full bg-[#1E1A2A] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
                  :key="index"
                  @tap="handleSelOrderBy(item)"
                  v-for="(item, index) in orderList"
                  >{{ item.name }}</view
                >
              </view>
            </u-transition>
          </view>
        </view>

        <u-button
          class="followBtn"
          type="primary"
          :customStyle="{ width: '170rpx', height: '60rpx', ...btnStyle1 }"
          :hairline="false"
          @click="$push({ name: 'HistoryList' })"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          iconColor="#282656"
          shape="circle"
          >写评价</u-button
        >
      </view>
      <view class="m-auto mb-5 w-[702rpx]">
        <commentItem
          :commentType="2"
          :hideMore="hideMore"
          :list="commentList"
          :repertoireId="id"
          @updateCommentList="updateCommentList" />

        <!-- 空状态 -->
        <view class="mt-[100rpx] w-full" v-if="!commentList || !commentList.length">
          <u-empty
            :icon="$iconFormat('empty/comment.svg')"
            height="322rpx"
            mode="data"
            text="点击下方中央扫一扫，扫描实体票后评价"
            width="472rpx"></u-empty>
        </view>

        <view
          class="m-auto flex h-[80rpx] w-[304rpx] items-center justify-center rounded-full border-[2rpx] border-solid border-w-20"
          @tap="handleShowMoreComment"
          v-if="commentList && commentList.length > 2 && hideMore">
          <text class="font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60">展开全部评价</text>
          <image class="block h-3 w-3" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
        </view>
      </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="Tabulation" />
  </view>

  <askDetail :popupContaoller="popupContaoller" :selAsk="selAsk" @close="handleCloseDetailPopup" v-if="selAsk" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $issueCountByRepertoire, $issueList } from '@/api/ask'
  import { $userTreasureSave } from '@/api/base'
  import { $commentListByPage } from '@/api/comment'
  import { $repertoireDetails2 } from '@/api/repertoire'
  import { $repertoireDisplay } from '@/api/showcase'
  import askDetail from '@/components/Ask/Detasil.vue'
  import followBtn from '@/components/Btn/FollowBtn.vue'
  import commentItem from '@/components/Item/CommentItem.vue'
  import network from '@/components/Network.vue'
  import tabbar from '@/components/Tabbar.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $previewImage, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt3, titleStyle1 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj
  const upOpt = computed(() => {
    let obj = {
      ...upOpt3.value,
      noMoreSize: !hideMore.value ? 1 : 99999,
    }

    return obj
  })

  const slideCustomStyle = ref({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '10rpx',
  })
  const options = ref({
    effect: 'cards',
    width: uni.upx2px(160),
    height: uni.upx2px(240),
    swiperItemWidth: uni.upx2px(160),
    swiperItemHeight: uni.upx2px(240),
  })

  const id = ref(0) // 剧目id
  const detail = ref<any>('') // 剧目详情

  const selTheaterId = ref(0) // 选中的剧场id
  const selTheaterItem = ref<any>({}) // 选中的剧场
  const playTime = ref(0) // 剧目时间
  const repertoireInfoDetailList = ref<any>([]) // 剧目剧场关联场次时间详情

  const timeListContraller = ref(false) // 时间列表折叠控制器

  /* 剧目简介 */
  const tagStyle = ref({
    div: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: rgb(255 255 255 / 60%); line-height: 48rpx;',
    p: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: rgb(255 255 255 / 60%); line-height: 48rpx;',
    span: 'font-size: 28rpx; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: rgb(255 255 255 / 60%); line-height: 48rpx;',
  })
  const isOpen = ref(false) // 是否展开
  const isShow = ref(false) // 是否展开演员信息

  const repertoireDisplay = ref<any>([]) // 剧目橱窗

  const issueNum = ref(0) // 问题数量
  const issueList = ref<any>([]) // 问大家
  const popupContaoller = ref(false) // 详情弹窗控制器
  const selAsk = ref<any>('') // 选中的问题

  const dropMenuController = ref(false) // 下拉框控制器2
  const orderList = ref([
    { name: '时间正序', string: 'createTime', isAsc: 'asc' },
    { name: '时间倒序', string: 'createTime', isAsc: 'desc' },
  ])
  const orderBy = ref('top desc,createTime') // 排序字段2
  const orderByTxt = ref('默认排序') // 排序字段名2
  const isAsc = ref('desc') // 正序倒序

  const commentList = ref<any>([]) // 剧场评论
  const hideMore = ref(true)

  const toBottom = ref(false) // 回到底部

  const scrollToView = ref('')

  /* 关注按钮样式 */
  const btnStyle1 = ref({
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '26rpx',
    fontWeight: 500,
    padding: 0,
  })

  onLoad((params: any) => {
    if (params.id) id.value = params.id - 0
    if (params.scene) id.value = params.scene - 0
    if (params.bottom) toBottom.value = true

    uni.$on('updateAsk', getIssueList)
    uni.$on('updateComment', () => {
      mescrollObj.value.resetUpScroll()
    })
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    if (mescroll.num == 1) {
      /* 剧目详情 */
      getRepertoireDetails()
      /* 橱窗 */
      getRepertoireDisplay()
      /* 问大家 */
      getIssueList()
    }

    if (toBottom.value) {
      orderBy.value = 'createTime'
      orderByTxt.value = '时间倒序'
      isAsc.value = 'desc'

      handleShowMoreComment()
    }

    /* 剧场评价 */
    getCommentListByPage(mescroll)
  }

  /* **************************************************************************************************** */

  /* 获取剧目详情 */
  const getRepertoireDetails = () => {
    $repertoireDetails2({ id: id.value, userId: userInfo.value.id }).then((res: any) => {
      if (res.data.pictures) {
        res.data.pictures = res.data.pictures.split(',')
        res.data.pictures = res.data.pictures.map((i: any) => (i = $picFormat(i)))
        if (res.data.coverPicture) res.data.pictures.unshift($picFormat(res.data.coverPicture))
      } else {
        res.data.pictures = []
        if (res.data.coverPicture) res.data.pictures.unshift($picFormat(res.data.coverPicture))
      }

      if ((res.data.likeCount || res.data.likeCount === 0) && (res.data.dislikeCount || res.data.dislikeCount === 0)) {
        const total = res.data.likeCount + res.data.dislikeCount
        res.data.likeRate = Math.ceil((res.data.likeCount / total) * 100)
        res.data.dislikeRate = Math.ceil((res.data.dislikeCount / total) * 100)
      }

      let arr1: any = [],
        arr2: any = []

      res.data.repertoireActorList.map((i: any) => {
        if (i.actorType == 1) arr1.push(i)
        else {
          if (i.groupPerformanceName) i.groupPerformanceName = i.groupPerformanceName.split('、')
          arr2.push(i)
        }
      })
      res.data.repertoireActorList = arr1
      res.data.repertoireActorList2 = arr2

      res.data.repertoireCreativeTeamList.map((i: any) => {
        if (i.name) i.name = i.name.split('、')
        else i.name = []
      })

      detail.value = res.data

      let flag: boolean = false

      res.data.repertoireInfoList.map((i: any, index: number) => {
        let temp = dayjs().isSame(dayjs(i.startTime), 'month')
        if (temp) {
          flag = true

          selTheaterId.value = i.id
          selTheaterItem.value = i

          scrollToView.value = 'date' + index
        }
      })

      if (!flag) {
        selTheaterId.value = res.data.repertoireInfoList[0].id
        selTheaterItem.value = res.data.repertoireInfoList[0]
        scrollToView.value = 'date0'
      }

      repertoireInfoDetailList.value = selTheaterItem.value.repertoireInfoDetailList

      if (selTheaterItem.value.repertoireInfoDetailList && selTheaterItem.value.repertoireInfoDetailList.length) {
        let dateS = selTheaterItem.value.repertoireInfoDetailList[0].startTime
        let dateE = selTheaterItem.value.repertoireInfoDetailList[0].endTime

        playTime.value = dayjs(dateE).diff(dayjs(dateS), 'minutes')
      } else {
        playTime.value = 0
      }

      // app.$refs.uReadMore.init()
    })
  }

  /* 关注切换 */
  const handleSwitchFollow = (status: boolean) => {
    $userTreasureSave({
      userId: userInfo.value.id,
      merchantId: detail.value.merchantId,
      repertoireId: id.value,
    }).then((res: any) => {
      if (status) {
        detail.value.fansFlag = 1
        detail.value.focusNumber++
      } else {
        detail.value.fansFlag = 0
        if (detail.value.focusNumber > 0) detail.value.focusNumber--
      }
    })
  }

  /* 切换剧场 */
  const handleSwitchTheeater = (item: any) => {
    selTheaterId.value = item.id
    selTheaterItem.value = item
    repertoireInfoDetailList.value = item.repertoireInfoDetailList

    if (selTheaterItem.value.repertoireInfoDetailList && selTheaterItem.value.repertoireInfoDetailList.length) {
      let dateS = selTheaterItem.value.repertoireInfoDetailList[0].startTime
      let dateE = selTheaterItem.value.repertoireInfoDetailList[0].endTime

      playTime.value = dayjs(dateE).diff(dayjs(dateS), 'minutes')
    } else {
      playTime.value = 0
    }
  }

  /* 展开收取剧目简介 */
  const handleShowMore = () => {
    if (!isOpen.value) isOpen.value = true
    else isOpen.value = false
  }

  /* **************************************************************************************************** */

  /* 获取橱窗展示 */
  const getRepertoireDisplay = () => {
    $repertoireDisplay({ repertoireId: id.value }).then((res: any) => {
      repertoireDisplay.value = res.data
    })
  }

  /* **************************************************************************************************** */

  /* 获取问大家 */
  const getIssueList = () => {
    $issueCountByRepertoire(id.value).then((res: any) => {
      issueNum.value = res.data
    })

    $issueList({
      pageNum: 1,
      pageSize: 2,
      repertoireId: id.value,
    }).then((res: any) => {
      issueList.value = res.data.rows
    })
  }

  /* 查看问答详情 */
  const handleCheckDetail = (item: any) => {
    selAsk.value = item
    popupContaoller.value = true
  }

  /* 关闭详情 */
  const handleCloseDetailPopup = () => {
    selAsk.value = {}
    popupContaoller.value = false
  }

  /* **************************************************************************************************** */

  /* 切换排序字段2 */
  const handleSelOrderBy = (orderItem: any) => {
    if (orderBy.value !== orderItem.string || isAsc.value !== orderItem.isAsc || !isAsc.value) {
      orderBy.value = orderItem.string
      orderByTxt.value = orderItem.name
      isAsc.value = orderItem.isAsc
    } else {
      orderBy.value = 'top desc,createTime'
      orderByTxt.value = '默认排序'
      isAsc.value = 'desc'
    }

    /* 剧场评价 */
    mescrollObj.value.resetUpScroll()
  }

  /* 显示更多评论 */
  const handleShowMoreComment = () => {
    hideMore.value = false
    mescrollObj.value.lockUpScroll(false)
  }

  /* 剧场评价 */
  const getCommentListByPage = (mescroll: any) => {
    $commentListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      flag: 1,
      userId: userInfo.value.id,
      repertoireId: id.value,
      orderByColumn: orderBy.value ? orderBy.value + ' ' + isAsc.value + ',id' : undefined,
      // isAsc: isAsc.value,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY年MM月DD日')
          i.isOpen = false
          if (i.content) i.text = i.content.replace(/<[^>]+>/g, '')
        })

        if (mescroll.num == 1) commentList.value = [] // 第一页需手动制空列表

        commentList.value = commentList.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)

        if (toBottom.value) {
          mescroll.scrollTo('#comment', 1000)
          toBottom.value = false
        }

        // if (!hideMore.value) mescroll.endBySize(curPageData.length, res.data.total)
        // else mescroll.endSuccess(1, false)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 更新评论列表 */
  const updateCommentList = (list: any) => {
    commentList.value = list
  }
</script>

<style lang="scss" scoped>
  .baseInfo {
    &:deep(.followBtn) {
      position: absolute !important;
      right: 0 !important;
      bottom: 0 !important;
    }

    .swiperW {
      width: 180rpx !important;
    }
  }

  .readMoreBtn {
    &:deep(.u-read-more__toggle) {
      display: none;
    }
  }

  .active {
    background: rgb(156 117 211 / 60%);
  }

  :deep(.mescroll-wxs-content) {
    padding-bottom: 100rpx;
  }
</style>
