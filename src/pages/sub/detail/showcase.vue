<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder>
      <template #center>
        <view class="box-border w-full pl-[80rpx] font-Medium text-[36rpx] font-medium leading-[50rpx] text-w-100"
          >橱窗列表展示</view
        >
      </template>
    </u-navbar>

    <mescroll-uni
      class="h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="w-full pt-5">
        <template v-if="theaterId">
          <view
            class="relative box-border w-full pb-6 pl-[34rpx] pr-[34rpx]"
            :key="index"
            v-for="(i, index) in theaterDisplay">
            <view class="mb-[10rpx] flex items-center justify-start">
              <view class="h-2.5 w-2.5 rounded-full bg-[#9C75D3]"></view>
              <view class="ml-[18rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                >{{ dayjs(index).format('YYYY年MM月DD日') }}发行</view
              >
            </view>

            <view class="box-border flex w-full flex-wrap items-start justify-start pl-[38rpx]">
              <view class="mr-2.5 flex flex-col items-center justify-center" :key="jIndex" v-for="(j, jIndex) in i">
                <view class="mb-[14rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                  >纪念徽章</view
                >
                <u-image
                  :src="$picFormat(j.souvenirBadgeUrl)"
                  @click="$previewImage([$picFormat(j.souvenirBadgeUrl)], 0)"
                  bgColor="transparent"
                  height="140rpx"
                  mode="aspectFill"
                  radius="10rpx"
                  width="140rpx"></u-image>
              </view>
            </view>

            <view
              class="absolute left-[42rpx] top-[35rpx] h-[232rpx] w-[1rpx] border-r-[1rpx] border-dashed border-[#9C75D3]"></view>
          </view>
        </template>

        <template v-else-if="repertoireId">
          <view
            class="relative box-border w-full pb-6 pl-[34rpx] pr-[34rpx]"
            :key="index"
            v-for="(i, index) in repertoireDisplay">
            <view class="mb-[10rpx] flex items-center justify-start">
              <view class="h-2.5 w-2.5 rounded-full bg-[#9C75D3]"></view>
              <view class="ml-[18rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                >{{ dayjs(index).format('YYYY年MM月DD日') }}发行</view
              >
            </view>

            <view class="box-border flex w-full flex-wrap items-start justify-start pl-[38rpx]">
              <view class="flex items-start justify-start" :key="jIndex" v-for="(j, jIndex) in i">
                <view class="flex flex-col items-center justify-center">
                  <view class="mb-[14rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                    >电子票根</view
                  >
                  <u-image
                    :src="$picFormat(j.repertoireTicketUrl)"
                    @click="$previewImage([$picFormat(j.repertoireTicketUrl)], 0)"
                    bgColor="transparent"
                    height="140rpx"
                    mode="aspectFill"
                    radius="10rpx"
                    width="104rpx"></u-image>
                </view>

                <template v-if="j.digitalAvatarUrl">
                  <view
                    class="ml-2.5 mr-2.5 mt-6 self-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
                    >+</view
                  >

                  <view class="flex flex-col items-center justify-center">
                    <view class="mb-[14rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100"
                      >数字头像</view
                    >
                    <u-image
                      :src="$picFormat(j.digitalAvatarUrl)"
                      @click="$previewImage([$picFormat(j.digitalAvatarUrl)], 0)"
                      bgColor="transparent"
                      height="140rpx"
                      mode="aspectFill"
                      radius="10rpx"
                      width="140rpx"></u-image>
                  </view>
                </template>
              </view>
            </view>

            <view
              class="absolute left-[42rpx] top-[35rpx] h-[232rpx] w-[1rpx] border-r-[1rpx] border-dashed border-[#9C75D3]"></view>
          </view>
        </template>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $repertoireDisplay, $theaterDisplay } from '@/api/showcase'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $picFormat, $previewImage, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy
  const { downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const theaterId = ref('')
  const repertoireId = ref('')

  const theaterDisplay = ref<any>([])
  const repertoireDisplay = ref<any>([])

  onLoad((params: any) => {
    if (params.theaterId) theaterId.value = params.theaterId
    if (params.repertoireId) repertoireId.value = params.repertoireId
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    mescroll.endSuccess(1, false)

    if (theaterId.value) {
      $theaterDisplay({ theaterId: theaterId.value })
        .then((res: any) => {
          theaterDisplay.value = _.groupBy(res.data, 'auditPassTime')
          mescroll.endSuccess(1, false)
        })
        .catch(() => {
          mescroll.endErr() // 请求失败, 结束加载
        })
    } else if (repertoireId.value) {
      $repertoireDisplay({ repertoireId: repertoireId.value })
        .then((res: any) => {
          repertoireDisplay.value = _.groupBy(res.data, 'auditPassTime')
          mescroll.endSuccess(1, false)
        })
        .catch(() => {
          mescroll.endErr() // 请求失败, 结束加载
        })
    }
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/showcase.webp');
  }
</style>
