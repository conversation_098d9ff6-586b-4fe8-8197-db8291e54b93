<template>
  <view class="pageWrap flex min-h-screen w-screen flex-col items-center justify-between bg-b-100">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      :leftIcon="$iconFormat('camera/back.png')"
      @leftClick="$back()"
      bgColor="#000"
      leftIconSize="72rpx"
      placeholder />

    <camera
      class="cameraWrap"
      @error="error"
      devicePosition="back"
      flash="auto"
      outputDimension="1080P"
      v-if="!isLoading">
      <cover-view class="scanBorder"></cover-view>

      <cover-view class="scanTip1">请将票面信息清晰无遮挡铺满矩形区域内&nbsp;&nbsp;</cover-view>
      <cover-view class="scanTip1 scanTip2">不要拍到副券&nbsp;&nbsp;</cover-view>
    </camera>

    <view
      class="relative box-border flex w-full shrink-0 items-center justify-between pb-[148rpx] pl-[104rpx] pr-[104rpx]">
      <view class="block h-[96rpx] w-[96rpx]"></view>

      <image class="block h-[144rpx] w-[144rpx]" :src="$iconFormat('camera/photograph.png')" @click="takePhoto"></image>

      <image class="block h-[96rpx] w-[96rpx]" :src="$iconFormat('camera/photo.png')" @click="handleSelPic"></image>
    </view>
  </view>

  <ksp-cropper
    :height="1040"
    :maxHeight="1024"
    :maxWidth="1024"
    :url="selPic"
    :width="680"
    @cancel="handleCancel"
    @ok="handleCropPic"
    mode="fixed"></ksp-cropper>
</template>

<script lang="ts" setup>
  import { $imgRecognition } from '@/api/scan'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $push } from '@/utils/methods'

  const globalStore = useGlobalStore()
  const topCss = ref(0)

  const isLoading = ref(false)
  const selPic = ref()

  onLoad(() => {
    let sysInfo: any = uni.$u.sys()

    topCss.value = sysInfo?.safeArea?.top
  })

  /* 选择要裁剪的图片 */
  const handleSelPic = () => {
    isLoading.value = true

    uni.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res: any) => {
        selPic.value = res.tempFiles[0].tempFilePath
      },
      fail: (err: any) => {
        isLoading.value = false
      },
    })
  }

  /* 裁剪头像 */
  const handleCropPic = (e: any) => {
    nextTick(() => {
      $loading('识别中')

      $imgRecognition({
        name: 'file',
        filePath: e.path,
        params: { deviceModel: uni.$u.sys().deviceModel },
      })
        .then((res: any) => {
          globalStore.scanResult = res
          uni.setStorageSync('scanResult', res)
          uni.hideLoading()
          selPic.value = ''
          isLoading.value = false
          $push({ name: 'ScanResult' })
        })
        .catch((err: any) => {
          isLoading.value = false
        })
    })
  }

  /* 取消裁剪图片 */
  const handleCancel = () => {
    isLoading.value = false
    selPic.value = ''
  }

  /* 拍照 */
  const takePhoto = () => {
    const ctx = uni.createCameraContext()

    ctx.takePhoto({
      quality: 'high',
      success: (res: any) => {
        $loading('识别中')
        // isLoading.value = true

        // selPic.value = res.tempImagePath

        $imgRecognition({
          name: 'file',
          filePath: res.tempImagePath,
          params: { deviceModel: uni.$u.sys().deviceModel },
        }).then((res: any) => {
          globalStore.scanResult = res
          uni.setStorageSync('scanResult', res)
          uni.hideLoading()
          $push({ name: 'ScanResult' })
        })
      },
    })
  }

  /* 相机报错 */
  const error = (e: any) => {}
</script>

<style lang="scss" scoped>
  .cameraWrap {
    position: relative;
    width: 100vw;
    height: calc(100vw / 3 * 4);

    .scanBorder {
      position: absolute;
      top: 50%;
      left: 50%;
      box-sizing: border-box;
      width: 90%;
      height: 95%;
      pointer-events: none;
      border: 6rpx solid #ec2a2a;
      border-radius: 20rpx;
      box-shadow: 0rpx 4rpx 20rpx 0rpx rgb(0 0 0 / 5%);
      transform: translate(-50%, -50%);
    }

    .scanTip1 {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 20;
      width: 100%;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 36rpx;
      font-weight: 600;
      line-height: 44rpx;
      color: #ec2a2a;
      text-align: center;
      transform: rotate(90deg) translate(0, calc(2.5% + 80rpx));

      // transform: rotate(90deg) translate(0, 0);
      transform-origin: center center;
    }

    .scanTip2 {
      right: 50%;
      left: initial;
      transform: rotate(90deg) translate(0, calc(-2.5% - 80rpx));

      // transform: rotate(90deg) translate(0, 0);
    }
  }
</style>
