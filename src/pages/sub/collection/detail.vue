<template>
  <view
    class="pageWrap min-h-screen w-screen bg-deepbg bg-fullAuto bg-no-repeat pb-[50rpx]"
    :class="{ bgPatch: badgeType === 1 }">
    <!-- 导航栏 -->
    <u-navbar
      class="z-50 w-full shrink-0"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />

    <!-- 电子票普通样式预览 -->
    <view class="relative mb-[40rpx] mt-[20rpx] min-h-[720rpx] w-full" v-if="!showType && badgeType === 1">
      <u-image
        class="m-auto block w-fit"
        :src="detail.commonImage"
        height="auto"
        mode="widthFix"
        width="520rpx"></u-image>

      <view
        class="absolute right-0 top-[86rpx] h-[64rpx] w-[180rpx] rounded-s-[64rpx] bg-gradient-to-r from-[#DFC097] to-[#C7996A] text-center font-Regular text-[26rpx] font-normal leading-[64rpx] text-[#603420]"
        @click="showType = !showType"
        >{{ showType ? '查看普通版' : '查看升级版' }}</view
      >
    </view>
    <!-- 电子票升级样式预览 -->
    <template v-if="showType && badgeType === 1">
      <veiw class="relative z-20 w-full">
        <view class="relative w-full">
          <view
            class="absolute right-0 top-[86rpx] z-20 h-[64rpx] w-[180rpx] rounded-s-[64rpx] bg-gradient-to-r from-[#DFC097] to-[#C7996A] text-center font-Regular text-[26rpx] font-normal leading-[64rpx] text-[#603420]"
            @click="showType = !showType"
            >{{ showType ? '查看普通版' : '查看升级版' }}</view
          >
        </view>

        <view
          class="relative mt-[20rpx] h-[1000rpx] w-full overflow-hidden"
          @click.stop.prevent
          @touchstart.stop.prevent>
          <model
            class="absolute bottom-0 left-0 right-0 top-0 m-auto"
            type="ticket"
            :url="[detail.coverFront, detail.coverReverse]"
            height="1100rpx"
            ref="modelRef"
            v-if="detail.coverFront && detail.coverReverse" />
        </view>

        <view class="relative z-20 m-auto mt-[-72rpx] flex h-8 w-[622rpx] items-center justify-between">
          <image
            class="block h-8 w-8"
            :src="$iconFormat('icon/rotateR.svg')"
            @longtap.stop.prevent="rotateModel(1)"
            @touchend="rotateStop"
            mode="scaleToFill" />
          <image
            class="block h-8 w-8"
            :src="$iconFormat('icon/rotateL.svg')"
            @longtap.stop.prevent="rotateModel(2)"
            @touchend="rotateStop"
            mode="scaleToFill" />
        </view>
      </veiw>

      <view
        class="relative z-20 mb-[46rpx] w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100"
        >按住画面左右翻动可查看票的背面</view
      >
    </template>

    <!-- 数字头像 -->
    <view
      class="relative m-auto mb-[168rpx] mt-[160rpx] flex h-[480rpx] w-full items-center justify-center"
      v-if="badgeType === 2">
      <!-- 普通样式预览 -->
      <u-image :src="detail.commonImage" height="480rpx" mode="aspectFill " v-if="!showType" width="480rpx"></u-image>
      <!-- 升级样式 -->
      <u-image :src="detail.upgradeImage" height="480rpx" mode="aspectFill " v-else width="480rpx"></u-image>

      <view
        class="absolute right-0 top-[-74rpx] h-[64rpx] w-[180rpx] rounded-s-[64rpx] bg-gradient-to-r from-[#DFC097] to-[#C7996A] text-center font-Regular text-[26rpx] font-normal leading-[64rpx] text-[#603420]"
        @click="showType = !showType"
        v-if="detail.upgradeImage"
        >{{ showType ? '查看普通版' : '查看升级版' }}</view
      >
    </view>

    <!-- 纪念徽章 -->
    <template v-if="badgeType === 3">
      <view class="relative mt-[150rpx] h-[560rpx] w-full overflow-hidden" @click.stop.prevent @touchstart.stop.prevent>
        <model
          class="absolute bottom-0 left-0 top-0 m-auto"
          :url="detail.model"
          height="728rpx"
          ref="modelRef"
          v-if="detail.model" />
      </view>

      <view class="z-10 m-auto mb-[46rpx] mt-[2rpx] flex h-8 w-[582rpx] items-center justify-between">
        <image
          class="block h-8 w-8"
          :src="$iconFormat('icon/rotateR.svg')"
          @longtap.stop.prevent="rotateModel(1)"
          @touchend="rotateStop"
          mode="scaleToFill" />
        <image
          class="block h-8 w-8"
          :src="$iconFormat('icon/rotateL.svg')"
          @longtap.stop.prevent="rotateModel(2)"
          @touchend="rotateStop"
          mode="scaleToFill" />
      </view>
    </template>

    <!-- 商品名称 -->
    <view class="relative m-auto mb-[40rpx] flex w-fit items-center justify-center">
      <view
        class="mr-1 w-[60vw] text-center font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
        v-if="badgeType === 1"
        >{{ detail.portfolioName }}电子票根</view
      >
      <view
        class="mr-1 w-[60vw] text-center font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
        v-if="badgeType === 2"
        >{{ detail.portfolioName }}数字头像</view
      >
      <view
        class="mr-1 w-[60vw] text-center font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
        v-else-if="badgeType === 3"
        >{{ detail.name }}纪念徽章</view
      >

      <view
        class="absolute left-[100%] h-[40rpx] w-[96rpx] rounded-full bg-[#D8D8D8] text-center font-Regular text-[24rpx] font-normal leading-[40rpx] text-[#999999]"
        v-if="(detail.userReceivingRecordsId && [1, 2].includes(badgeType)) || detail.createTime"
        >已获得</view
      >
      <view
        class="absolute left-[100%] h-[40rpx] w-[96rpx] rounded-full bg-[#D8D8D8] text-center font-Regular text-[24rpx] font-normal leading-[40rpx] text-[#999999]"
        v-else
        >未获得</view
      >
    </view>

    <!-- 商品信息 -->
    <view class="relative m-auto mb-[32rpx] h-fit w-[706rpx]">
      <image class="block w-full" :src="$iconFormat('background/infoDailogT.png')" mode="widthFix" />

      <view
        class="relative z-10 mb-[-2rpx] mt-[-4rpx] h-fit w-full border-l-[4rpx] border-r-[4rpx] border-[#BC93FF] bg-[#100636] pb-[2rpx] pl-[24rpx] pr-[24rpx] pt-[2rpx]">
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[20rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >剧目名称</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[40rpx] text-w-100">{{
            detail.repertoireName
          }}</text>
        </view>

        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[20rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >发行时间</text
          >
          <view class="grow whitespace-nowrap font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
            >{{ detail.startTime }}-{{ detail.endTime }}</view
          >
        </view>

        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[20rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >发放数量</text
          >
          <text
            class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
            v-if="showType || badgeType === 3"
            >{{ detail.issuedQuantity }}份</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100" v-else>无数量限制</text>
        </view>

        <view class="flex items-start justify-start">
          <text
            class="mr-[20rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >{{ detail.createTime ? '获得时间' : '获得方式' }}</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            detail.createTime || '观演后扫票获得'
          }}</text>
        </view>
      </view>

      <image class="block w-full" :src="$iconFormat('background/infoDailogB.png')" mode="widthFix" />
    </view>

    <customBtn
      class="m-auto"
      @click="handleToScan"
      text="去扫票"
      v-if="!detail.userReceivingRecordsId && [1, 2].includes(badgeType)" />
    <customBtn
      class="m-auto"
      type="3"
      @click="$push({ name: 'Upgrade', params: { portfolioNo: detail.portfolioNo } })"
      text="升级收藏"
      v-else-if="detail.userReceivingRecordsId && detail.upgradeStatus === 0 && [1, 2].includes(badgeType)" />
    <customBtn
      class="m-auto"
      type="4"
      text="已升级收藏"
      v-else-if="detail.userReceivingRecordsId && detail.upgradeStatus === 1 && [1, 2].includes(badgeType)" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $getDigitalAvatarInfo, $getRepertoireTicketInfo, $souvenirBadgeDetail } from '@/api/collention'
  import { $imgRecognition } from '@/api/scan'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import model from '@/components/Model.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $push } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()

  const modelRef = ref() // 模型对象

  const id = ref() // 商品id
  const portfolioId = ref() // 商品组合id
  const relationId = ref() // 纪念勋章id
  const badgeType = ref(1) // 商品类型
  const detail = ref<any>('') // 商品详情

  const showType = ref(true) // 显示类型 false：普通版 true：升级版

  onLoad((params: any) => {
    id.value = params.id - 0
    portfolioId.value = params.portfolioId - 0
    relationId.value = params.relationId - 0
    badgeType.value = params.badgeType - 0

    showType.value = true

    if (badgeType.value === 1) {
      /* 获取电子票详情 */
      $getRepertoireTicketInfo({ portfolioId: portfolioId.value }).then((res: any) => {
        /* 普通电子票 */
        if (res.data.commonImage) res.data.commonImage = $picFormat(res.data.commonImage)
        /* 升级电子票 */
        if (res.data.coverFront) res.data.coverFront = $picFormat(res.data.coverFront)
        else showType.value = false // 没有升级票
        /* 电子票背面 */
        if (res.data.coverReverse) res.data.coverReverse = $picFormat(res.data.coverReverse)

        if (res.data.startTime) res.data.startTime = dayjs(res.data.startTime).format('YYYY/MM/DD  HH:mm')
        if (res.data.endTime) res.data.endTime = dayjs(res.data.endTime).format('YYYY/MM/DD  HH:mm')
        if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY/MM/DD HH:mm')

        detail.value = res.data
      })
    } else if (badgeType.value === 2) {
      /* 获取数字头像详情 */
      $getDigitalAvatarInfo({ portfolioId: portfolioId.value }).then((res: any) => {
        if (res.data.image) res.data.commonImage = res.data.image

        if (res.data.commonImage) res.data.commonImage = $picFormat(res.data.commonImage)
        if (res.data.overlayImage) res.data.upgradeImage = $picFormat(res.data.overlayImage)
        else showType.value = false

        if (res.data.startTime) res.data.startTime = dayjs(res.data.startTime).format('YYYY/MM/DD  HH:mm')
        if (res.data.endTime) res.data.endTime = dayjs(res.data.endTime).format('YYYY/MM/DD  HH:mm')
        if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY/MM/DD HH:mm')

        detail.value = res.data
      })
    } else if (badgeType.value === 3) {
      /* 获取纪念勋章详情 */
      $souvenirBadgeDetail({ id: id.value, relationId: relationId.value || 0 }).then((res: any) => {
        if (res.data.model) res.data.model = $picFormat(res.data.model)

        if (res.data.startTime) res.data.startTime = dayjs(res.data.startTime).format('YYYY/MM/DD  HH:mm')
        if (res.data.endTime) res.data.endTime = dayjs(res.data.endTime).format('YYYY/MM/DD  HH:mm')
        if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY/MM/DD HH:mm')

        detail.value = res.data
      })
    }
  })

  /* 旋转模型 */
  const rotateModel = (type: number) => {
    app.$refs.modelRef.rotateModel(type)
  }
  /* 停止旋转模型 */
  const rotateStop = () => {
    app.$refs.modelRef.rotateStop()
  }

  /* 去扫票 */
  const handleToScan = () => {
    $push({ name: 'Camera' })

    // uni.chooseMedia({
    //   count: 1,
    //   mediaType: ['image'],
    //   sourceType: ['album', 'camera'],
    //   success: (res: any) => {
    //     $loading('识别中')

    //     $imgRecognition({
    //       name: 'file',
    //       filePath: res.tempFiles[0].tempFilePath,
    //     }).then((res: any) => {
    //       globalStore.scanResult = res
    //       uni.setStorageSync('scanResult', res)
    //       uni.hideLoading()
    //       $push({ name: 'ScanResult' })
    //     })
    //   },
    // })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/collectionBg.webp');

    &.bgPatch {
      background-image: url($icon + 'background/collectionBg2.webp');
    }

    .infoBox {
      background-image: url($icon + 'background/infoDailog2.webp');
    }
  }
</style>
