<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      :title="navTitle"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#1B1629"
      leftIconColor="#FFFFFF"
      placeholder>
    </u-navbar>

    <mescroll-uni
      class="h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt1"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick">
      <view class="w-full px-6 py-4">
        <!-- 问题展示 -->
        <view class="mb-6 rounded-[20rpx] bg-w-5 p-4">
          <view class="mb-2 flex items-center">
            <text class="fas fa-question-circle mr-2 text-lightPurple" style="font-size: 32rpx;"></text>
            <text class="font-Medium text-[26rpx] font-medium leading-[36rpx] text-lightPurple">您的问题</text>
          </view>
          <text class="font-Regular text-[28rpx] font-normal leading-[40rpx]" style="color: #FFFFFF;">{{ question }}</text>
        </view>

        <!-- 加载状态 -->
        <view class="flex flex-col items-center justify-center py-20" v-if="isLoading">
          <u-loading-icon :show="true" color="#8B5CF6" size="40rpx"></u-loading-icon>
          <text class="mt-4 font-Regular text-[26rpx] font-normal" style="color: rgba(255, 255, 255, 0.8);">AI正在分析中...</text>
          <text class="mt-2 font-Regular text-[22rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">{{ loadingText }}</text>
        </view>

        <!-- 无关联内容提示 -->
        <view class="flex flex-col items-center justify-center py-20" v-else-if="!hasRelevantContent && !isLoading">
          <text class="fas fa-search mb-4" style="font-size: 120rpx; color: rgba(255, 255, 255, 0.3);"></text>
          <text class="mb-2 font-Medium text-[28rpx] font-medium" style="color: #FFFFFF;">暂无关联内容</text>
          <text class="text-center font-Regular text-[24rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">
            很抱歉，我们暂时没有找到与"{{ question }}"相关的演出内容
          </text>
          <text class="mt-2 text-center font-Regular text-[22rpx] font-normal" style="color: rgba(255, 255, 255, 0.4);">
            您可以尝试搜索其他关键词或浏览推荐内容
          </text>
        </view>

        <!-- AI分析结果 -->
        <view v-else-if="analysisResult && hasRelevantContent">
          <!-- 站内内容相关性展示 -->
          <view class="mb-6" v-if="analysisResult.contentRelevance">
            <view class="mb-4 flex items-center justify-between">
              <view class="flex items-center">
                <text class="fas fa-database mr-2" style="font-size: 32rpx; color: #FFFFFF;"></text>
                <text class="font-Medium text-[30rpx] font-medium leading-[42rpx]" style="color: #FFFFFF;">内容分析</text>
              </view>
              <view class="rounded-full bg-green-500/20 px-3 py-1">
                <text class="font-Regular text-[20rpx] font-normal" style="color: #4ade80;">站内相关</text>
              </view>
            </view>
            
            <!-- 常规数据分析 -->
            <view class="mb-4 rounded-[16rpx] bg-w-5 p-4">
              <view class="mb-3 font-Medium text-[24rpx] font-medium" style="color: #FFFFFF;">数据统计</view>
              <view class="grid grid-cols-4 gap-3">
                <view class="text-center">
                  <view class="font-Medium text-[28rpx] font-medium leading-[40rpx]" style="color: #9C75D3;">
                    {{ analysisResult.contentRelevance.goodComments || 0 }}
                  </view>
                  <text class="font-Regular text-[20rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">好评数</text>
                </view>
                <view class="text-center">
                  <view class="font-Medium text-[28rpx] font-medium leading-[40rpx]" style="color: #9C75D3;">
                    {{ analysisResult.contentRelevance.badComments || 0 }}
                  </view>
                  <text class="font-Regular text-[20rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">差评数</text>
                </view>
                <view class="text-center">
                  <view class="font-Medium text-[28rpx] font-medium leading-[40rpx]" style="color: #FFFFFF;">
                    {{ analysisResult.contentRelevance.totalComments || 0 }}
                  </view>
                  <text class="font-Regular text-[20rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">评论篇数</text>
                </view>
                <view class="text-center">
                  <view class="font-Medium text-[28rpx] font-medium leading-[40rpx]" style="color: #9C75D3;">
                    {{ analysisResult.contentRelevance.goodRatio || 0 }}%
                  </view>
                  <text class="font-Regular text-[20rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">好评占比</text>
                </view>
              </view>
            </view>

            <!-- AI智能内容分析 -->
            <view class="space-y-3">
              <!-- 优点分析 -->
              <view class="rounded-[16rpx] bg-green-500/10 p-4">
                <view class="mb-2 flex items-center">
                  <text class="fas fa-thumbs-up mr-2" style="font-size: 24rpx; color: #4ade80;"></text>
                  <text class="font-Medium text-[24rpx] font-medium" style="color: #4ade80;">优点分析</text>
                </view>
                <text class="font-Regular text-[22rpx] font-normal leading-[32rpx]" style="color: rgba(255, 255, 255, 0.9);">
                  {{ analysisResult.contentRelevance.advantages || '暂无优点分析' }}
                </text>
                <!-- 好评佐证 -->
                <view class="mt-3 space-y-2" v-if="getPositiveReferences(analysisResult.references)?.length">
                  <view class="flex items-center justify-between">
                    <view class="flex items-center">
                      <text class="fas fa-quote-left mr-1" style="font-size: 16rpx; color: #4ade80;"></text>
                      <text class="font-Regular text-[20rpx] font-normal" style="color: #4ade80;">用户好评佐证</text>
                    </view>
                    <view class="flex items-center" @tap="togglePositiveReferences" v-if="getPositiveReferences(analysisResult.references).length > 1">
                      <text class="mr-1 font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">{{
                        showPositiveReferences ? '收起' : '查看更多'
                      }}</text>
                      <text
                        class="fas fa-chevron-down transition-transform"
                        :class="{ 'rotate-180': showPositiveReferences }"
                        style="font-size: 16rpx; color: rgba(255, 255, 255, 0.6);">
                      </text>
                    </view>
                  </view>
                  <!-- 显示第一条好评 -->
                  <view 
                    class="bg-green-500/5 rounded-lg p-2 border-l-2 border-green-400">
                    <text class="font-Regular text-[20rpx] font-normal leading-[28rpx]" style="color: rgba(255, 255, 255, 0.8);">{{ getPositiveReferences(analysisResult.references)[0].content.slice(0, 60) }}{{ getPositiveReferences(analysisResult.references)[0].content.length > 60 ? '...' : '' }}</text>
                    <view class="flex items-center justify-between mt-1">
                      <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">@{{ getPositiveReferences(analysisResult.references)[0].userName }}</text>
                      <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.4);">{{ getPositiveReferences(analysisResult.references)[0].createTime }}</text>
                    </view>
                  </view>
                  <!-- 展开显示其余好评 -->
                  <view v-show="showPositiveReferences" v-for="(ref, index) in getPositiveReferences(analysisResult.references).slice(1)" :key="'positive-' + index">
                    <view class="bg-green-500/5 rounded-lg p-2 border-l-2 border-green-400">
                      <text class="font-Regular text-[20rpx] font-normal leading-[28rpx]" style="color: rgba(255, 255, 255, 0.8);">{{ ref.content.slice(0, 60) }}{{ ref.content.length > 60 ? '...' : '' }}</text>
                      <view class="flex items-center justify-between mt-1">
                        <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">@{{ ref.userName }}</text>
                        <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.4);">{{ ref.createTime }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 缺点分析 -->
              <view class="rounded-[16rpx] bg-red-500/10 p-4" v-if="analysisResult.contentRelevance.disadvantages">
                <view class="mb-2 flex items-center">
                  <text class="fas fa-thumbs-down mr-2" style="font-size: 24rpx; color: #f87171;"></text>
                  <text class="font-Medium text-[24rpx] font-medium" style="color: #f87171;">缺点分析</text>
                </view>
                <text class="font-Regular text-[22rpx] font-normal leading-[32rpx]" style="color: rgba(255, 255, 255, 0.9);">
                  {{ analysisResult.contentRelevance.disadvantages }}
                </text>
                <!-- 差评佐证 -->
                <view class="mt-3 space-y-2" v-if="getNegativeReferences(analysisResult.references)?.length">
                  <view class="flex items-center justify-between">
                    <view class="flex items-center">
                      <text class="fas fa-quote-left mr-1" style="font-size: 16rpx; color: #f87171;"></text>
                      <text class="font-Regular text-[20rpx] font-normal" style="color: #f87171;">用户差评佐证</text>
                    </view>
                    <view class="flex items-center" @tap="toggleNegativeReferences" v-if="getNegativeReferences(analysisResult.references).length > 1">
                      <text class="mr-1 font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">{{
                        showNegativeReferences ? '收起' : '查看更多'
                      }}</text>
                      <text
                        class="fas fa-chevron-down transition-transform"
                        :class="{ 'rotate-180': showNegativeReferences }"
                        style="font-size: 16rpx; color: rgba(255, 255, 255, 0.6);">
                      </text>
                    </view>
                  </view>
                  <!-- 显示第一条差评 -->
                  <view 
                    class="bg-red-500/5 rounded-lg p-2 border-l-2 border-red-400">
                    <text class="font-Regular text-[20rpx] font-normal leading-[28rpx]" style="color: rgba(255, 255, 255, 0.8);">{{ getNegativeReferences(analysisResult.references)[0].content.slice(0, 60) }}{{ getNegativeReferences(analysisResult.references)[0].content.length > 60 ? '...' : '' }}</text>
                    <view class="flex items-center justify-between mt-1">
                      <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">@{{ getNegativeReferences(analysisResult.references)[0].userName }}</text>
                      <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.4);">{{ getNegativeReferences(analysisResult.references)[0].createTime }}</text>
                    </view>
                  </view>
                  <!-- 展开显示其余差评 -->
                  <view v-show="showNegativeReferences" v-for="(ref, index) in getNegativeReferences(analysisResult.references).slice(1)" :key="'negative-' + index">
                    <view class="bg-red-500/5 rounded-lg p-2 border-l-2 border-red-400">
                      <text class="font-Regular text-[20rpx] font-normal leading-[28rpx]" style="color: rgba(255, 255, 255, 0.8);">{{ ref.content.slice(0, 60) }}{{ ref.content.length > 60 ? '...' : '' }}</text>
                      <view class="flex items-center justify-between mt-1">
                        <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">@{{ ref.userName }}</text>
                        <text class="font-Regular text-[18rpx] font-normal" style="color: rgba(255, 255, 255, 0.4);">{{ ref.createTime }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 总体评价 -->
              <view class="rounded-[16rpx] bg-blue-500/10 p-4">
                <view class="mb-2 flex items-center">
                  <text class="fas fa-star mr-2" style="font-size: 24rpx; color: #60a5fa;"></text>
                  <text class="font-Medium text-[24rpx] font-medium" style="color: #60a5fa;">总体评价</text>
                </view>
                <text class="font-Regular text-[22rpx] font-normal leading-[32rpx]" style="color: rgba(255, 255, 255, 0.9);">
                  {{ analysisResult.contentRelevance.summary || '暂无总体评价' }}
                </text>
              </view>
            </view>
          </view>


        </view>

        <!-- 错误状态 -->
        <view class="flex flex-col items-center justify-center py-20" v-else-if="errorMessage">
          <text class="fas fa-exclamation-triangle mb-4" style="font-size: 120rpx; color: rgba(255, 255, 255, 0.3);"></text>
          <text class="mb-2 font-Regular text-[26rpx] font-normal" style="color: rgba(255, 255, 255, 0.8);">分析失败</text>
          <text class="mb-4 text-center font-Regular text-[22rpx] font-normal" style="color: rgba(255, 255, 255, 0.6);">{{ errorMessage }}</text>
          <u-button :customStyle="retryButtonStyle" @click="retryAnalysis" text="重新分析"> </u-button>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- toast提示 -->
  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $aiAnalyzeRecommend, $aiAnalyzeRepertoire } from '@/api/ai'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $topclick } from '@/utils/methods'

  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)
  const { downOpt1, upOpt1, titleStyle1 } = storeToRefs(useGlobalStore())

  const question = ref('')
  const navTitle = ref('智能搜索结果')
  const isLoading = ref(true)
  const loadingText = ref('正在收集评论数据...')
  const analysisResult = ref<any>(null)
  const errorMessage = ref('')
  const hasRelevantContent = ref(true)
  const showPositiveReferences = ref(false)
  const showNegativeReferences = ref(false)

  const retryButtonStyle = reactive({
    backgroundColor: '#8B5CF6',
    borderColor: '#8B5CF6',
    color: '#ffffff',
  })

  // 加载文案列表
  const loadingTexts = ['正在收集评论数据...', '正在分析用户反馈...', '正在生成推荐理由...', '正在整理分析结果...']

  onLoad((params: any) => {
    if (params.question) {
      question.value = decodeURIComponent(params.question)
      startAnalysis()
    }
  })

  // 开始分析
  const startAnalysis = () => {
    isLoading.value = true
    errorMessage.value = ''
    hasRelevantContent.value = true

    // 模拟加载文案切换
    let textIndex = 0
    const textInterval = setInterval(() => {
      if (textIndex < loadingTexts.length - 1) {
        textIndex++
        loadingText.value = loadingTexts[textIndex]
      } else {
        clearInterval(textInterval)
      }
    }, 1500)

    // 模拟API调用延迟
    setTimeout(() => {
      clearInterval(textInterval)
      isLoading.value = false
      
      // 检查是否为无关联内容的搜索
      const noContentKeywords = ['房价', '天气', '股票', '编程', '游戏']
      const hasNoContent = noContentKeywords.some(keyword => question.value.includes(keyword))
      
      if (hasNoContent) {
        hasRelevantContent.value = false
        analysisResult.value = null
      } else {
        hasRelevantContent.value = true
        analysisResult.value = getMockAnalysisData()
      }
    }, 3000)

    // 注释掉真实API调用，保留以便后续替换
    /*
    // 判断问题类型并调用相应API
    const isSpecificRepertoire =
      question.value.includes('值得看吗') || question.value.includes('怎么样') || question.value.includes('评价')

    const apiCall = isSpecificRepertoire
      ? $aiAnalyzeRepertoire({
          question: question.value,
          userId: userInfo.value?.id || 0,
        })
      : $aiAnalyzeRecommend({
          question: question.value,
          userId: userInfo.value?.id || 0,
        })

    apiCall
      .then((res: any) => {
        setTimeout(() => {
          clearInterval(textInterval)
          isLoading.value = false
          
          // 检查是否有相关内容
          if (res.data && (res.data.recommendations?.length || res.data.repertoireAnalysis || res.data.contentRelevance)) {
            hasRelevantContent.value = true
            analysisResult.value = res.data
          } else {
            hasRelevantContent.value = false
            analysisResult.value = null
          }
        }, 3000) // 确保至少显示3秒加载动画
      })
      .catch((error: any) => {
        clearInterval(textInterval)
        isLoading.value = false
        errorMessage.value = error.message || 'AI分析服务暂时不可用，请稍后重试'
      })
    */
  }

  // 模拟数据生成函数 - 便于后续替换为真实API
  const getMockAnalysisData = () => {
    // 统一返回综合分析数据，保留评论佐证
    return {
      contentRelevance: {
        goodComments: 1250,
        badComments: 120,
        totalComments: 1420,
        goodRatio: 88,
        advantages: '推荐的剧目类型丰富，包含音乐剧、话剧、儿童剧等多种形式。大部分剧目口碑良好，制作精良。价格区间覆盖面广，适合不同预算的观众。',
        disadvantages: '热门剧目票源紧张，需要提前购买。部分小众剧目宣传不足，知名度有待提高。',
        summary: '根据您的搜索，我们为您分析了相关演出内容。建议根据个人喜好和预算进行选择，热门剧目建议提前购票。'
      },
      references: [
        {
          id: 11,
          userName: '资深剧迷张小姐',
          userAvatar: 'https://picsum.photos/100/100?random=211',
          content: '最近看了好几部音乐剧，《狮子王》和《歌剧魅影》都很棒！《狮子王》更适合带孩子，《歌剧魅影》更适合情侣约会。',
          createTime: '2024-07-26',
          sentiment: 'positive',
          repertoireName: '多部音乐剧对比',
          theaterName: '多个剧院',
          likeCount: 234,
          replyCount: 45
        },
        {
          id: 12,
          userName: '音乐剧新手小刘',
          userAvatar: 'https://picsum.photos/100/100?random=212',
          content: '作为音乐剧新手，我觉得这些推荐都很不错。舞台效果的确震撼，音乐也很棒，值得一看。',
          createTime: '2024-07-25',
          sentiment: 'positive',
          repertoireName: '音乐剧体验',
          theaterName: '多个剧院',
          likeCount: 89,
          replyCount: 12
        },
        {
          id: 13,
          userName: '戏剧评论家李老师',
          userAvatar: 'https://picsum.photos/100/100?random=213',
          content: '票价确实不便宜，但制作水准很高。建议大家根据自己的预算选择，提前关注优惠活动。',
          createTime: '2024-07-24',
          sentiment: 'negative',
          repertoireName: '音乐剧观演建议',
          theaterName: '多个剧院',
          likeCount: 56,
          replyCount: 8
        },
        {
          id: 14,
          userName: '剧院常客小王',
          userAvatar: 'https://picsum.photos/100/100?random=214',
          content: '整体来说音乐剧的质量都很高，演员的表演也很到位。特别是现场的气氛非常棒，很容易被感染。',
          createTime: '2024-07-23',
          sentiment: 'positive',
          repertoireName: '音乐剧体验分享',
          theaterName: '多个剧院',
          likeCount: 167,
          replyCount: 23
        },
        {
          id: 15,
          userName: '首次观演的观众',
          userAvatar: 'https://picsum.photos/100/100?random=215',
          content: '说实话，我觉得有点贵，而且剧情进展比较慢，开头半小时我差点睡着了。不过后面确实精彩了很多。',
          createTime: '2024-07-22',
          sentiment: 'negative',
          repertoireName: '狮子王',
          theaterName: '上海大剧院',
          likeCount: 45,
          replyCount: 8
        }
      ]
    }
  }

  // 重新分析
  const retryAnalysis = () => {
    startAnalysis()
  }

  // 切换好评佐证显示
  const togglePositiveReferences = () => {
    showPositiveReferences.value = !showPositiveReferences.value
  }

  // 切换差评佐证显示
  const toggleNegativeReferences = () => {
    showNegativeReferences.value = !showNegativeReferences.value
  }

  // 获取正面评论
  const getPositiveReferences = (references: any[]) => {
    return references?.filter(ref => ref.sentiment === 'positive') || []
  }

  // 获取负面评论
  const getNegativeReferences = (references: any[]) => {
    return references?.filter(ref => ref.sentiment === 'negative') || []
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .grid {
      display: grid;

      &.grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      &.gap-3 {
        gap: 12rpx;
      }
    }

    .space-y-2 > * + * {
      margin-top: 8rpx;
    }

    .space-y-3 > * + * {
      margin-top: 12rpx;
    }

    .space-y-4 > * + * {
      margin-top: 16rpx;
    }

    .grid {
      display: grid;

      &.grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
      }

      &.gap-3 {
        gap: 12rpx;
      }
    }

    .border-l-4 {
      border-left-width: 4rpx;
      border-left-style: solid;
    }

    .border-green-400 {
      border-left-color: #4ade80;
    }

    .border-red-400 {
      border-left-color: #f87171;
    }

    .border-gray-400 {
      border-left-color: #9ca3af;
    }

    .rotate-180 {
      transform: rotate(180deg);
    }

    .transition-transform {
      transition: transform 0.3s ease;
    }
  }
</style>
