<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="我的评论"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="m-auto w-[702rpx] pt-[18rpx]">
        <view
          class="mb-2.5 w-full rounded-[20rpx] bg-w-10 pb-2 pl-[10rpx] pr-[10rpx] pt-[12rpx] last:mb-0"
          :key="i.id"
          @tap="
            $push({
              name: 'CommentDetail',
              params: {
                id: i.parentId,
                commentType: 2,
                theaterId: i.theaterId,
                repertoireId: i.repertoireId,
              },
            })
          "
          v-for="i in list">
          <!-- 剧目信息 -->
          <view class="mb-2.5 flex w-full items-center justify-start rounded-[20rpx] bg-deepbg2 p-1 pr-[12rpx]">
            <u-image
              class="shrink-0"
              :src="$picFormat(i.repertoireCoverPicture)"
              bgColor="transparent"
              height="80rpx"
              mode="aspectFill"
              radius="10rpx"
              width="80rpx"></u-image>

            <view class="ml-2.5 grow">
              <view class="mb-[4rpx] font-Medium text-[24rpx] font-medium leading-[34rpx] text-w-100">{{
                i.repertoireName || '-'
              }}</view>
              <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
                >演出时间:{{ i.startTime || '-' }} | 演出场地:{{ i.theaterName || '-' }}</view
              >
            </view>
          </view>
          <!-- 评论 -->
          <view class="mb-2 flex items-center justify-start pl-1">
            <view class="mr-[10rpx] h-[60rpx] w-[2rpx] bg-w-60"></view>
            <view class="line-clamp-2 break-all font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
              >{{ i.userName }}：{{ i.text }}</view
            >
          </view>
          <!-- 线 -->
          <view class="mb-2.5 w-full border-b border-solid border-w-10"></view>
          <!-- 我的回复 -->
          <view
            class="mb-[10rpx] line-clamp-2 break-all pl-[10rpx] pr-[10rpx] font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
            >{{ i.replyName }}：{{ i.replyContent }}</view
          >
          <!-- 时间 -->
          <view class="pl-[10rpx] pr-[10rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
            i.createTime
          }}</view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/comment.svg')"
          height="322rpx"
          mode="data"
          text="暂无评论，去发表第一个评论吧"
          width="472rpx"></u-empty>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $commentListReplyByUser } from '@/api/comment'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const list = ref<any>() // 电子票列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $commentListReplyByUser({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
      orderByColumn: 'createTime desc,id',
      // isAsc: 'desc',
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.userMerchantId !== '0') {
            if (i.userMerchantId === '-1') {
              i.userName = i.repertoireName
            } else if (i.userMerchantId === '-2') {
              i.userName = i.theaterName
            }

            if (i.repertoireReplyStatus === 1) {
              i.replyName = i.repertoireName
            } else if (i.theaterReplyStatus === 1) {
              i.replyName = i.theaterName
            }
          }

          if (i.content) i.text = i.content.replace(/<[^>]+>/g, '')
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY/MM/DD HH:mm:ss')
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }
</script>

<style lang="scss" scoped></style>
