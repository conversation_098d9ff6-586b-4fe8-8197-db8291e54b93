<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="系统通知"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder />

    <mescroll-uni
      class="block h-0 w-screen grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="w-full pl-3 pr-3 pt-2.5" v-if="list && list.length">
        <view
          class="mb-2.5 w-full rounded-[20rpx] bg-bgPurple p-2.5 pb-0"
          :key="index"
          @tap="$push({ name: 'MsgDetail', params: { id: i.id } })"
          v-for="(i, index) in list">
          <view class="mb-[10rpx] flex items-center justify-between">
            <text class="grow break-all font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100">{{
              i.title
            }}</text>
            <text class="ml-2.5 shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
              i.createTime
            }}</text>
          </view>
          <view class="w-full border-b border-solid border-w-10 pb-[10rpx]">
            <view class="line-clamp-2 w-full break-all font-Regular text-[28rpx] font-normal leading-5 text-w-60">{{
              i.body
            }}</view>
          </view>
          <view class="flex h-[88rpx] w-full items-center justify-between">
            <text class="font-Regular text-[28rpx] font-normal leading-5 text-purple">查看详情</text>
            <image class="block h-4 w-4" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/comment.svg')"
          height="322rpx"
          mode="data"
          text="哔嘟哔嘟~暂无系统通知"
          width="472rpx"></u-empty>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userNotifyListByPage } from '@/api/message'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { titleStyle1, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const list = ref<any>([]) // 系统消息列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    $userNotifyListByPage({ pageNum: mescroll.num, pageSize: mescroll.size })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.body) {
            i.body = i.body.replace(/<[^>]+>/g, '')
            i.body = i.body.replace(/&nbsp;/gi, '')
          }
          if (i.createTime) i.createTime = dayjs(i.createTime).format('M月D日 HH:mm')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) // 追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }
</script>
