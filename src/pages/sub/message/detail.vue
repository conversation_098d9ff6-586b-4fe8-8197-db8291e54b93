<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="系统通知详情"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder />

    <view class="w-full border-b border-solid border-w-10"></view>

    <view class="w-full pb-[100rpx] pl-3 pr-3 pt-[38rpx]">
      <view class="mb-[10rpx] w-full text-center font-Medium text-base font-medium leading-[44rpx] text-w-100">{{
        title
      }}</view>
      <view class="mb-5 w-full text-center font-Regular text-xs font-normal leading-[34rpx] text-w-60">{{ date }}</view>

      <u-parse class="mainCon" :content="content" :tagStyle="tagStyle"></u-parse>
    </view>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userNotifyDetails } from '@/api/message'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back } from '@/utils/methods'
  import dayjs from 'dayjs'

  const { titleStyle1 } = storeToRefs(useGlobalStore())

  const title = ref('')
  const date = ref('')
  const content = ref('')

  /* 富文本样式 */
  const tagStyle = reactive({
    p: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: rgb(255 255 255 / 60%);line-height: 40rpx;',
    span: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: rgb(255 255 255 / 60%);line-height: 40rpx;',
    div: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: rgb(255 255 255 / 60%);line-height: 40rpx;',
    ol: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: rgb(255 255 255 / 60%);line-height: 40rpx;',
    ul: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: rgb(255 255 255 / 60%);line-height: 40rpx;',
    li: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: rgb(255 255 255 / 60%);line-height: 40rpx;',
  })

  onLoad((params: any) => {
    $userNotifyDetails(params.id).then((res: any) => {
      title.value = res.data.title
      date.value = dayjs(res.data.createTime).format('M月D日 HH:mm')
      content.value = res.data.body
    })
  })
</script>

<style lang="scss" scoped>
  .mainCon {
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx;
    color: rgb(255 255 255 / 60%);
  }
</style>
