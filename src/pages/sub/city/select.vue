<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="切换城市"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <!-- 搜索 -->
    <view
      class="searchWrap m-auto mb-5 mt-[10rpx] flex h-8 w-[702rpx] shrink-0 items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
      <image class="mr-1 h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @clear="handleSearch"
        @confirm="handleSearch"
        border="none"
        clearable
        placeholder="请输入城市名搜索"
        v-model="keyword" />
      <view class="ml-2.5 shrink-0 font-Regular text-base font-normal leading-[44rpx] text-purple" @tap="handleSearch"
        >搜索</view
      >
    </view>

    <!-- 当前定位城市 -->
    <view class="m-auto mb-[30rpx] flex w-[702rpx] items-center justify-start">
      <image class="block h-4 w-4 shrink-0" :src="$iconFormat('icon/address.svg')" mode="scaleToFill" />
      <text class="ml-[10rpx] shrink-0 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80"
        >当前定位城市</text
      >
      <text class="ml-[10rpx] grow font-Medium text-[26rpx] font-medium leading-[36rpx] text-w-80">{{
        addressInfo.name || '-'
      }}</text>

      <image
        class="block h-[48rpx] w-[48rpx] shrink-0"
        :src="$iconFormat('icon/refresh.svg')"
        @click="handleReLocation"
        mode="scaleToFill" />
    </view>

    <view class="line m-auto mb-2.5 h-px w-[702rpx] bg-w-10"></view>

    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <!-- 选择城市 -->
      <view class="tetx-[26rpx] m-auto mb-2.5 w-[702rpx] font-Medium font-medium leading-[36rpx] text-w-60"
        >选择城市</view
      >

      <view class="m-auto w-[702rpx] pb-[100rpx]">
        <view class="w-full" :key="item.letter" v-for="item in list">
          <view
            class="flex w-full items-center justify-start pl-[12rpx] last:mb-0"
            :id="sonIndex === 0 ? item.letter : ''"
            :key="son.id"
            @tap="handleSelCity(son)"
            v-for="(son, sonIndex) in item.data">
            <text class="w-[72rpx] shrink-0 font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60">{{
              sonIndex === 0 ? item.letter : ''
            }}</text>
            <text class="font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60">{{ son.name }}</text>
          </view>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $areaByCity, $areaTree } from '@/api/area'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $citySort, $iconFormat, $push, $topclick } from '@/utils/methods'
  import QQMapWX from '@jonny1994/qqmap-wx-jssdk'
  import _ from 'lodash'

  let qqmapsdk: any

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()
  const { addressInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const keyword = ref('') // 搜索关键字

  const list = ref<any>([]) // 城市列表

  /* 搜索 */
  const handleSearch = () => {
    mescrollObj.value.resetUpScroll()
  }

  /* 加载数据 */
  const upCallback = (mescroll?: any) => {
    mescrollObj.value = mescroll

    uni.showLoading()

    $areaByCity(keyword.value)
      .then((res: any) => {
        list.value = $citySort(_.clone(res.data), 'name')

        uni.hideLoading()

        mescroll.endSuccess(1, false)
      })
      .catch(() => {
        uni.hideLoading()
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 重新定位 */
  const handleReLocation = () => {
    uni.showLoading({ title: '定位中' })

    qqmapsdk = new QQMapWX({ key: 'N5ZBZ-W53KI-WSWGE-5X22E-Z46WH-OIFA5' })

    qqmapsdk.reverseGeocoder({
      success: (res: any) => {
        const adcode: number = res.result.ad_info.city_code?.replace(res.result.ad_info.nation_code, '')

        let isFoundInfo = false

        $areaTree()
          .then((res: any) => {
            res.data.map((i: any) => {
              if (i.id == adcode) {
                isFoundInfo = true
                globalStore.addressInfo = { id: i.id, name: i.name }
                uni.setStorageSync('addressInfo', { id: i.id, name: i.name })
              }

              if (!isFoundInfo) {
                i.children.map((j: any) => {
                  if (j.id == adcode) {
                    isFoundInfo = true
                    globalStore.addressInfo = { id: j.id, name: j.name }
                    uni.setStorageSync('addressInfo', { id: j.id, name: j.name })
                  }
                })
              }
            })

            uni.hideLoading()

            if (!isFoundInfo) {
              uni.$u.toast('自动定位失败，请手动选择所在城市')
              uni.$u.sleep(2000).then(() => {
                $push({ name: 'CitySelect' })
              })
            }
          })
          .catch(() => {
            uni.hideLoading()

            uni.$u.toast('自动定位失败，请手动选择所在城市')
            uni.$u.sleep(2000).then(() => {
              $push({ name: 'CitySelect' })
            })
          })
      },
      fail: (error: any) => {
        uni.hideLoading()

        uni.$u.toast('自动定位失败，请手动选择所在城市')
        uni.$u.sleep(2000).then(() => {
          $push({ name: 'CitySelect' })
        })
      },
    })
  }

  /* 选择城市 */
  const handleSelCity = (city: any) => {
    globalStore.addressInfo = city
    uni.setStorageSync('addressInfo', globalStore.addressInfo)

    $back()
  }
</script>
