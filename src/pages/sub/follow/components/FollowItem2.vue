<template>
  <view
    class="relative mb-2.5 flex h-[240rpx] w-full items-stretch justify-start overflow-hidden rounded-[20rpx] bg-w-10 last:mb-0"
    @tap="$push({ name: 'RepertoireDetail', params: { id: repertoire.repertoireId } })">
    <viwe
      class="absolute left-[12rpx] top-[10rpx] z-10 h-5 rounded-t-[25rpx] rounded-bl-[25rpx] bg-gradient-to-l from-[#FFA524] to-[#FF8227] pl-[14rpx] pr-[14rpx] font-Regular text-[24rpx] font-normal leading-[40rpx] text-w-100"
      v-if="repertoire.lastUpdateTime"
      >{{ repertoire.lastUpdateTime }}前更新</viwe
    >

    <u-image
      class="shrink-0"
      :src="repertoire.repertoireCoverPicture"
      bgColor="transparent"
      height="240rpx"
      mode="aspectFill"
      width="240rpx"></u-image>

    <view class="relative flex grow flex-col items-start justify-start p-2.5">
      <view class="w-full grow">
        <view class="mb-[10rpx] flex items-start justify-start">
          <text class="grow font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100">{{
            repertoire.repertoireName
          }}</text>
          <image
            class="ml-2.5 mt-[6rpx] block h-4 w-4 shrink-0"
            :src="$iconFormat('icon/favouriteOn.svg')"
            mode="scaleToFill" />
        </view>
        <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
          >演出时间：{{ repertoire.startTime || '未知' }}</view
        >
        <!-- <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">演出场地：{{ repertoire.theaterName || '-' }}</view> -->
      </view>

      <view class="w-full shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        >关注时间：{{ repertoire.createTime || '-' }}</view
      >
    </view>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat, $push } from '@/utils/methods'

  defineProps({
    repertoire: { type: Object, default: () => new Object() }, // 剧目信息
  })
</script>

<style lang="scss" scoped></style>
