<template>
  <view
    class="relative mb-2.5 h-[240rpx] w-full overflow-hidden rounded-[20rpx] bg-w-10 last:mb-0"
    @tap="$push({ name: 'TheaterDetail', params: { id: theater.theaterId } })">
    <u-image
      :src="theater.theaterCoverPicture"
      bgColor="transparent"
      height="240rpx"
      mode="aspectFill"
      width="100%"></u-image>

    <viwe
      class="absolute right-2.5 top-[10rpx] h-5 rounded-t-[25rpx] rounded-br-[25rpx] bg-gradient-to-l from-[#FFA524] to-[#FF8227] pl-[14rpx] pr-[14rpx] font-Regular text-[24rpx] font-normal leading-[40rpx] text-w-100"
      v-if="theater.lastUpdateTime"
      >{{ theater.lastUpdateTime }}前更新</viwe
    >

    <view class="absolute bottom-0 left-0 h-[88rpx] w-full bg-gradient-to-b from-b-0 to-b-100 opacity-70"></view>
    <view class="absolute bottom-0 left-0 flex h-fit w-full items-center justify-between pb-2.5 pl-2.5 pr-2.5">
      <text class="grow font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{
        theater.theaterName || '-'
      }}</text>

      <view class="flex shrink-0 items-center justify-end">
        <image class="mr-[10rpx] block h-4 w-4" :src="$iconFormat('icon/favouriteOn.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{
          theater.createTime || '-'
        }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $iconFormat, $push } from '@/utils/methods'

  defineProps({
    theater: { type: Object, default: () => {} }, // 剧场信息
  })
</script>

<style lang="scss" scoped></style>
