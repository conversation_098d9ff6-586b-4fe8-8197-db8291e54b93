<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="我的关注"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="h-[1rpx] w-full shrink-0 bg-w-10"></view>

    <view class="filterBar relative z-10 mb-2.5 mt-2.5 flex w-full items-center justify-start pl-3 pr-3">
      <!-- 搜索 -->
      <view class="searchWrap flex h-8 w-[410rpx] grow items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
        <image class="mr-[6rpx] h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
        <u-input
          class="searchBox"
          @clear="handleSearch"
          @confirm="handleSearch"
          border="none"
          clearable
          placeholder="请输入剧场/剧目名称"
          v-model="keyword" />
      </view>
      <!-- 排序类型 -->
      <view
        class="relative ml-2.5 flex h-8 w-[188rpx] shrink-0 items-center justify-center rounded-full bg-w-10 pl-2 pr-[10rpx]"
        @tap="orderListController = !orderListController">
        <text class="grow text-center font-Regular text-[26rpx] font-normal leading-[32rpx] text-w-60">{{
          orderByTxt
        }}</text>
        <image class="mr-[-3rpx] block h-4 w-4 shrink-0" :src="$iconFormat('arrow/down1.svg')" mode="scaleToFill" />

        <u-transition :show="orderListController" mode="fade">
          <view
            class="absolute left-0 right-0 top-[80rpx] z-20 m-auto h-fit w-[160rpx] overflow-hidden rounded-[20rpx]">
            <view
              class="h-[60rpx] w-full bg-[#342B54] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
              :key="index"
              @tap="handleSelOrderBy(item)"
              v-for="(item, index) in orderList"
              >{{ item.name }}</view
            >
          </view>
        </u-transition>
      </view>
    </view>

    <mescroll-uni
      class="block h-0 w-screen grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="m-auto w-[702rpx]">
        <template :key="i.id" v-for="i in list">
          <FollowItem2 :repertoire="i" v-if="i.repertoireName" />
          <FollowItem1 :theater="i" v-else-if="i.theaterName" />
        </template>

        <!-- 空状态 -->
        <view class="emptyWrap mtPatch2 w-full pt-[200rpx]" v-if="!list || !list.length">
          <u-empty
            :icon="$iconFormat('empty/collection.webp')"
            :text="`哔嘟哔嘟~暂无关注剧场/剧目~`"
            height="385rpx"
            mode="data"
            width="487rpx"></u-empty>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import FollowItem1 from './components/FollowItem1.vue'
  import FollowItem2 from './components/FollowItem2.vue'
  import { $userTreasureListByPage } from '@/api/base'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'
  import relativeTime from 'dayjs/plugin/relativeTime'

  dayjs.extend(relativeTime)

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const keyword = ref('') // 搜索关键字
  /* 排序字段列表 */
  const orderList = ref([
    { name: '按更新时间', string: 'lastUpdateTime' },
    { name: '按关注时间', string: 'createTime' },
  ])
  const orderListController = ref(false) // 排序字段列表弹窗控制器
  const orderBy = ref('lastUpdateTime') // 排序字段
  const orderByTxt = ref('按更新时间') // 排序字段文案

  const list = ref<any>() // 电子票列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userTreasureListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
      keyword: keyword.value,
      orderByColumn: orderBy.value + ' desc,id',
      // isAsc: 'desc',
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.theaterCoverPicture) i.theaterCoverPicture = $picFormat(i.theaterCoverPicture)
          if (i.repertoireCoverPicture) i.repertoireCoverPicture = $picFormat(i.repertoireCoverPicture)
          if (i.lastUpdateTime) i.lastUpdateTime = dayjs().from(dayjs(i.lastUpdateTime), true)
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY/MM/DD')
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleSearch = () => {
    mescrollObj.value.resetUpScroll()
  }

  /* 切换排序字段 */
  const handleSelOrderBy = (orderItem: any) => {
    orderBy.value = orderItem.string
    orderByTxt.value = orderItem.name

    mescrollObj.value.resetUpScroll()
  }
</script>

<style lang="scss" scoped></style>
