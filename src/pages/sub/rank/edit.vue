<template>
  <view
    class="pageWrap flex min-h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      :title="formData.id ? '修改单项' : '创建单项'"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#282142"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="w-full border-b border-solid border-w-10"></view>

    <view class="box-border flex w-full items-center justify-start pb-[44rpx] pl-3 pr-3 pt-[44rpx]">
      <image class="mr-1 block h-4 w-4" :src="$iconFormat('icon/group.svg')" mode="scaleToFill" />
      <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">单项名称</text>
    </view>

    <view class="box-border w-full pl-3 pr-3">
      <u-input
        class="nameBox"
        @change="handleInput"
        border="none"
        clearable
        maxlength="12"
        placeholder="请输入单项名称"
        v-model="formData.name"></u-input>
      <u-line color="rgba(255, 255, 255, 0.4)" margin="20rpx 0 0" />
    </view>

    <view class="mb-[38rpx] box-border flex w-full items-center justify-start pl-3 pr-3 pt-[44rpx]">
      <image class="mr-1 block h-4 w-4" :src="$iconFormat('icon/add1.svg')" mode="scaleToFill" />
      <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">添加场次</text>
      <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">（只能上传一个）</text>
    </view>

    <view class="box-border flex w-full flex-wrap items-start justify-start pl-3 pr-3">
      <view
        class="mPatch1 relative mb-[30rpx] mr-2.5 h-[160rpx] w-[160rpx] rounded-[16rpx]"
        v-if="formData.userLeaderboard">
        <image
          class="blcok h-full w-full rounded-[16rpx]"
          :src="$picFormat(formData.userLeaderboard.coverPicture)"
          mode="aspectFill" />

        <image
          class="absolute left-[-6rpx] top-[-10rpx] z-10 block h-5 w-5"
          :src="$iconFormat('icon/close5.svg')"
          @click="handleDelTicket"
          mode="scaleToFill" />
      </view>

      <view
        class="mb-[30rpx] mr-2.5 flex h-[160rpx] w-[160rpx] flex-col items-center justify-center rounded-[20rpx] bg-w-20"
        @click="handleAddTicket"
        v-else>
        <u-icon name="plus" color="#ffffff" size="54rpx"></u-icon>
        <text class="mt-2 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">选择场次</text>
      </view>
    </view>

    <!-- 保存 -->
    <customBtn class="!fixed bottom-10 left-0 right-0 m-auto" @tap="handleSave" text="保存" />
  </view>

  <!-- 提示 -->
  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $leaderboardAdd, $leaderboardDetails, $leaderboardUpdate } from '@/api/rank'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $picFormat, $push, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy

  const { userInfo, titleStyle1 } = storeToRefs(useGlobalStore())

  const formData = reactive<any>({
    name: '',
    userLeaderboard: '',
  })
  const canSave = ref(false) // 是否能保存

  onLoad(async (params: any) => {
    if (params.name) formData.name = params.name
    if (params.id) {
      formData.id = params.id
      formData.userLeaderboard = (await $leaderboardDetails({ id: params.id })).data
    }

    if (params.canSave) canSave.value = true

    uni.$on('selTicket3', (item: any) => {
      formData.userLeaderboard = {
        userReceivingRecordsId: item.id,
        repertoireId: item.relationId,
        coverPicture: item.coverPicture,
      }
    })
  })

  /* 输入限制 */
  const handleInput = (value: any) => {
    // 判断是否超过12个字符或6个汉字
    if (value.replace(/[\u4e00-\u9fa5]/g, '**').length > 12) {
      value = truncateMixedString(value)
    }

    nextTick(() => {
      formData.name = value
    })
  }

  // 计算字符串实际占用的字符数（一个汉字视为两个字符）
  const getRealLength = (str: any) => {
    let realLength = 0
    for (let i = 0; i < str.length; i++) {
      if (str.charCodeAt(i) > 0x400) {
        // 非ASCII字符，可能是全角字符或汉字
        realLength += 2
      } else {
        // ASCII字符
        realLength += 1
      }
    }
    return realLength
  }

  // 截取中英文混合字符串前12个字符
  const truncateMixedString = (str: any) => {
    let result = ''
    let count = 0
    for (let i = 0; i < str.length; i++) {
      if (getRealLength(result + str.charAt(i)) <= 12) {
        result += str.charAt(i)
      } else {
        break
      }
    }
    return result
  }

  /* 添加电子票 */
  const handleAddTicket = () => {
    $push({ name: 'SelTicket', params: { type: '3' } })
  }

  /* 删除选中的电子票 */
  const handleDelTicket = () => {
    formData.userLeaderboard = ''
  }

  /* 保存分类 */
  const handleSave = () => {
    if (!formData.name) {
      $toast(app, '分组名称不能为空')
      return
    }

    if (!formData.userLeaderboard) {
      $toast(app, '票根不能为空')
      return
    }

    if (canSave.value) {
      if (formData.id) {
        $leaderboardUpdate({
          id: formData.id,
          name: formData.name,
          type: 1,
          userId: userInfo.value.id,
          userLeaderboard: formData.userLeaderboard,
        }).then((res: any) => {
          uni.$emit('updateRankList')
          $back()
        })
      } else {
        $leaderboardAdd({
          name: formData.name,
          type: 1,
          userId: userInfo.value.id,
          userLeaderboard: formData.userLeaderboard,
        }).then((res: any) => {
          uni.$emit('updateRankList')
          $back()
        })
      }
    } else {
      uni.$emit('addNewRank', {
        name: formData.name,
        type: 1,
        userId: userInfo.value.id,
        userLeaderboard: formData.userLeaderboard,
      })

      $back()
    }
  }
</script>

<style lang="scss" scoped>
  .nameBox {
    &:deep(.u-input__content) {
      .u-input__content__field-wrapper {
        .u-input__content__field-wrapper__field {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 30rpx !important;
          font-weight: 400 !important;
          color: #fff !important;
        }

        .input-placeholder {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 30rpx !important;
          font-weight: 400 !important;
          color: rgb(255 255 255 / 60%) !important;
        }
      }
    }
  }

  .keyword {
    padding: 0 20rpx !important;
    border-color: rgb(151 151 151 / 30%) !important;

    &:deep(.u-input__content) {
      padding-bottom: 12rpx;

      .u-input__content__field-wrapper {
        .u-input__content__field-wrapper__field {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 26rpx !important;
          font-weight: 400 !important;
          color: #fff !important;
        }

        .input-placeholder {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 26rpx !important;
          font-weight: 400 !important;
          color: rgb(255 255 255 / 60%) !important;
        }
      }
    }
  }

  .mPatch1 {
    &:nth-child(4n) {
      margin-right: 0;
    }
  }

  .mPatch2 {
    &:nth-child(3n) {
      margin-right: 0;
    }
  }
</style>
