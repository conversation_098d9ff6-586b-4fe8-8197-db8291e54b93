<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="单项管理"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#282142"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="w-full border-b border-solid border-w-10"></view>

    <view
      class="ml-auto mr-auto mt-2.5 box-border flex h-[128rpx] w-[702rpx] shrink-0 flex-col items-center justify-center rounded-[20rpx] border-[2rpx] border-dashed border-[#9C75D3] bg-purpleBg"
      @click="$push({ name: 'RankEdit' })"
      v-if="addCount > 0">
      <view class="flex items-center justify-center">
        <u-icon name="plus" color="#C59BFF" size="20rpx"></u-icon>
        <text class="ml-1 font-Regular text-[32rpx] font-normal leading-[44rpx] text-[#C59BFF]">设置新单项</text>
      </view>
      <view class="mt-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-50"
        >还可创建{{ addCount }}个单项</view
      >
    </view>

    <mescroll-uni
      class="mt-[50rpx] block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt1"
      @down="upCallback"
      @init="mescrollInit">
      <view class="w-full pb-[200rpx]">
        <view class="mb-[60rpx] box-border w-full pl-3 pr-3" :key="index" v-for="(i, index) in list">
          <view class="flex items-center justify-start">
            <view class="mr-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{ i.name }}</view>
            <view
              class="h-4 w-[100rpx] bg-w-20 text-center font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-50"
              v-if="i.type === 0"
              >系统分组</view
            >
            <view class="ml-[22rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-50"
              >共{{ i.coverPicture ? 1 : 0 }}个剧目</view
            >
          </view>

          <view class="mt-2.5 flex items-center justify-between">
            <image
              class="mr-2.5 block h-[160rpx] w-[160rpx] rounded-[16rpx] bg-b-50"
              :src="$picFormat(i.coverPicture)"
              mode="aspectFill"
              v-if="i.coverPicture" />
            <view class="h-[160rpx] w-[160rpx]" v-else></view>

            <view class="w-[128rpx]">
              <view
                class="mb-[18rpx] flex h-[64rpx] w-[128rpx] items-center justify-center rounded-[10rpx] bg-w-20"
                @click="handleAddTicket(i)">
                <image class="mr-1 block h-5 w-5" :src="$iconFormat('icon/change1.svg')" mode="scaleToFill" />
                <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">替换</text>
              </view>

              <view
                class="flex h-[64rpx] w-[128rpx] items-center justify-center rounded-[10rpx] bg-w-20"
                @click="handleClear(i)"
                v-if="i.type === 0 && i.coverPicture">
                <image class="mr-1 block h-5 w-5" :src="$iconFormat('icon/clear.svg')" mode="scaleToFill" />
                <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#8C8C8C]">清空</text>
              </view>

              <view
                class="flex h-[64rpx] w-[128rpx] items-center justify-center rounded-[10rpx] bg-w-20"
                @click="handleDelGroup(i)"
                v-if="i.type !== 0">
                <image class="mr-1 block h-5 w-5" :src="$iconFormat('icon/del2.svg')" mode="scaleToFill" />
                <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#EC2A2A]">删除</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- 清空 -->
  <customBtn class="!fixed bottom-10 left-0 right-0 m-auto" @tap="handleSave" text="保存榜单" />

  <u-modal
    class="modalWrap"
    title="确定删除该单项？"
    :show="delModalController"
    @cancel="delModalController = false"
    @confirm="handleCheckDel"
    content="删除该单项将删除单项内所有添加过的剧目票根"
    showCancelButton
    width="640rpx"></u-modal>

  <u-modal
    class="modalWrap"
    :show="clearModalController"
    @cancel="clearModalController = false"
    @confirm="handleCheckClear"
    content="确定清空该单项？"
    showCancelButton
    width="640rpx"></u-modal>

  <!-- 提示 -->
  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $leaderboardAdd, $leaderboardDelete, $leaderboardList, $leaderboardUpdate } from '@/api/rank'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $push, $toast } from '@/utils/methods'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy

  const { userInfo, titleStyle1 } = storeToRefs(useGlobalStore())
  const { downOpt1, upOpt1 } = storeToRefs(useGlobalStore())
  const { mescrollInit } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  /* 可以添加的单项数量 */
  const addCount = computed(() => {
    let temp = 3

    list.value?.map((i: any) => {
      if (i.type === 1) temp--
    })

    return temp
  })

  const list = ref<any>([]) // 榜单列表
  const selTicket = ref<any>('') // 选中的电子票
  const clearModalController = ref(false) // 清空弹窗控制器
  const delModalController = ref(false) // 删除弹窗控制器
  const delId = ref() // 删除单项的id

  const addList = ref<any>([]) // 新增列表
  const updateList = ref<any>([]) // 修改列表
  const delList = ref<any>([]) // 删除列表

  onLoad(() => {
    uni.$on('updateRankList', () => {
      mescrollObj.value.triggerDownScroll()
    })

    uni.$off('addNewRank')
    uni.$on('addNewRank', (item: any) => {
      list.value.push({
        name: item.name,
        type: 1,
        repertoireId: item.userLeaderboard.repertoireId,
        userReceivingRecordsId: item.userLeaderboard.userReceivingRecordsId,
        coverPicture: item.userLeaderboard.coverPicture,
      })
      addList.value.push({ ...item })
    })

    /* 替换电子票 */
    uni.$off('selTicket2')
    uni.$on('selTicket2', (item: any) => {
      handleLogData({
        coverPicture: item.coverPicture,
        repertoireId: item.relationId,
        userReceivingRecordsId: item.id,
      })
    })
  })

  /* 数据加载 */
  const upCallback = async (mescroll: any) => {
    mescrollObj.value = mescroll

    selTicket.value = ''
    delId.value = ''

    addList.value = []
    updateList.value = []
    delList.value = []

    $leaderboardList()
      .then((res: any) => {
        list.value = res.data

        mescroll.endSuccess(1, false)
      })
      .catch((err: any) => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 对比记录修改数据数组 */
  const handleLogData = (item: any) => {
    // console.log('🚀 ~ handleLogData ~ item:', item)

    let isget = false

    /* 修改新增未保存内容 */
    addList.value.map((i: any) => {
      if (selTicket.value.id === i.id) {
        isget = true
        i.userLeaderboard = {
          repertoireId: item.repertoireId,
          userReceivingRecordsId: item.userReceivingRecordsId,
        }
      }
    })

    /* 修改已有修改内容 */
    updateList.value.map((i: any) => {
      if (selTicket.value.id === i.id) {
        isget = true
        i.userLeaderboard = {
          repertoireId: item.repertoireId,
          userReceivingRecordsId: item.userReceivingRecordsId,
        }
      }
    })

    /* 修改未有修改内容 */
    if (!isget) {
      updateList.value.push({
        id: selTicket.value.id,
        name: selTicket.value.name,
        type: selTicket.value.type,
        userLeaderboard: {
          repertoireId: item.repertoireId,
          userReceivingRecordsId: item.userReceivingRecordsId,
        },
      })
    }

    list.value.map((i: any) => {
      if (i.id === selTicket.value.id) {
        i.coverPicture = item.coverPicture
        i.repertoireId = item.repertoireId
        i.userReceivingRecordsId = item.userReceivingRecordsId
      }
    })

    selTicket.value = ''
  }

  /* 添加电子票 */
  const handleAddTicket = (i: any) => {
    selTicket.value = i
    $push({ name: 'SelTicket', params: { type: '2' } })
  }

  /* 删除单项弹窗 */
  const handleDelGroup = (i: any) => {
    delId.value = i.id
    delModalController.value = true
  }

  /* 确认删除单项分组 */
  const handleCheckDel = () => {
    delList.value.push(delId.value)
    let delIndex = 0
    list.value.map((i: any, index: number) => {
      if (i.id === delId.value) delIndex = index
    })
    list.value.splice(delIndex, 1)
    delModalController.value = false
  }

  /* 显示清空提示弹窗 */
  const handleClear = (i: any) => {
    selTicket.value = _.cloneDeep(i)
    clearModalController.value = true
  }

  /* 清空榜单 */
  const handleCheckClear = () => {
    let data = _.cloneDeep(selTicket.value)
    data.userLeaderboard = []
    handleLogData({
      coverPicture: '',
      repertoireId: '',
      userReceivingRecordsId: '',
    })
    clearModalController.value = false
  }

  /* 保存榜单 */
  const handleSave = () => {
    let asyncFun: any = []

    /* 新增 */
    addList.value.forEach((i: any) => {
      asyncFun.push($leaderboardAdd(i))
    })

    /* 修改 */
    updateList.value.forEach((i: any) => {
      asyncFun.push(
        $leaderboardUpdate({
          id: i.id,
          name: i.name,
          type: i.type,
          userId: userInfo.value.id,
          userLeaderboard: {
            repertoireId: i.userLeaderboard.repertoireId,
            userReceivingRecordsId: i.userLeaderboard.userReceivingRecordsId,
          },
        })
      )
    })

    /* 删除 */
    delList.value.forEach((i: any) => {
      asyncFun.push($leaderboardDelete(i))
    })

    Promise.all(asyncFun).then(() => {
      $toast(app, '保存成功')
      uni.$emit('updateRankList')
    })
  }
</script>

<style lang="scss" scoped>
  .modalWrap {
    &:deep(.u-popup__content) {
      background: none;
    }

    &:deep(.u-modal) {
      background: #1e1e1e;
      border-radius: 24rpx;

      .u-modal__title {
        padding-top: 64rpx !important;
        font-family: PingFangSC, 'PingFang SC';
        font-size: 34rpx;
        font-weight: 500;
        line-height: 48rpx;
        color: #fff;
        text-align: center;
      }

      .u-modal__content {
        padding: 32rpx 48rpx 48rpx;

        .u-modal__content__text {
          font-family: PingFangSC, 'PingFang SC';
          font-size: 34rpx;
          font-weight: 400;
          line-height: 48rpx;
          color: rgb(255 255 255 / 60%);
          text-align: center;
        }
      }

      .u-line {
        border-color: rgb(255 255 255 / 10%) !important;
      }

      .u-modal__button-group {
        height: 110rpx;

        .u-modal__button-group__wrapper--cancel {
          height: 100%;

          &:active,
          &:focus,
          &:hover {
            background: none !important;
          }

          .u-modal__button-group__wrapper__text {
            font-family: PingFangSC, 'PingFang SC';
            font-size: 34rpx;
            font-weight: 500;
            line-height: 110rpx;
            color: rgb(255 255 255 / 50%) !important;
            letter-spacing: 1px;
          }
        }

        .u-modal__button-group__wrapper--confirm {
          height: 100%;

          &:active,
          &:focus,
          &:hover {
            background: none !important;
          }

          .u-modal__button-group__wrapper__text {
            font-family: PingFangSC, 'PingFang SC';
            font-size: 34rpx;
            font-weight: 500;
            line-height: 110rpx;
            color: #9c75d3 !important;
            letter-spacing: 1px;
          }
        }
      }
    }
  }

  .mPatch1 {
    &:nth-child(4n) {
      margin-right: 0;
    }
  }

  .mPatch2 {
    &:nth-child(3n) {
      margin-right: 0;
    }
  }
</style>
