<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder />

    <view
      class="mb-2.5 mt-[100rpx] box-border pl-[54rpx] pr-[54rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
      >{{ lastTime ? lastTime + '更新' : '' }}</view
    >

    <!-- 管理/分享 -->
    <view
      class="mb-5 ml-[54rpx] box-border flex h-[56rpx] w-[248rpx] items-center justify-center rounded-[36rpx] border-[2rpx] border-solid border-w-20">
      <view class="flex w-1/2 grow items-center justify-center" @click="$push({ name: 'RankList' })">
        <image class="mr-[4rpx] block h-[38rpx] w-[38rpx]" :src="$iconFormat('icon/setting2.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">管理</text>
      </view>

      <view class="h-5 w-[2rpx] shrink-0 bg-w-30"></view>

      <view class="flex w-1/2 grow items-center justify-center" @click="shareController = true">
        <image class="block h-5 w-5" :src="$iconFormat('icon/share1.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">分享</text>
      </view>
    </view>

    <mescroll-uni
      class="h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt1"
      @down="upCallback"
      @init="mescrollInit">
      <view class="w-full" :class="updateList && updateList.length ? 'pb-[200rpx]' : ''">
        <!-- 榜单列表 -->
        <view class="box-border flex w-full flex-wrap items-start justify-between pl-3 pr-3">
          <view class="mb-[60rpx] w-[336rpx]" :key="index" v-for="(i, index) in list">
            <view class="flex w-full items-center justify-center" :class="i.type === 0 ? '' : 'mb-[52rpx]'">
              <image
                class="block h-[46rpx] w-[46rpx] shrink-0"
                :src="$iconFormat('icon/titlePartl1.svg')"
                mode="scaleToFill"
                v-if="i" />
              <view class="line-clamp-1 font-Medium text-[36rpx] font-medium leading-[50rpx] text-[#EFC395]">{{
                i.name
              }}</view>
              <image
                class="block h-[46rpx] w-[46rpx] shrink-0"
                :src="$iconFormat('icon/titlePartr1.svg')"
                mode="scaleToFill"
                v-if="i" />
            </view>
            <view
              class="mb-2.5 ml-auto mr-auto h-4 w-[100rpx] rounded-[8rpx] bg-w-20 text-center font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-50"
              v-if="i.type === 0"
              >系统榜单</view
            >

            <template v-if="i.coverPicture">
              <view class="relative mb-2.5 w-full">
                <image
                  class="block h-[224rpx] w-full rounded-t-[20rpx]"
                  :src="$picFormat(i.coverPicture)"
                  mode="aspectFill" />

                <view
                  class="absolute left-2.5 top-2.5 h-5 w-fit bg-w-30 pl-[10rpx] pr-[10rpx] font-Regular text-[22rpx] font-normal leading-[40rpx] text-w-100"
                  >{{ i.cityName }}</view
                >
              </view>

              <view
                class="mb-1 line-clamp-2 min-h-[80rpx] w-full font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-100"
                >{{ i.repertoireName }}</view
              >
              <view class="mb-[6rpx] line-clamp-1 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
                >演出场地：{{ i.theaterName }}</view
              >
              <view class="whitespace-nowrap font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
                >演出时间：{{ i.time }}</view
              >
            </template>
            <view
              class="flex h-[400rpx] w-[336] flex-col items-center justify-center rounded-[20rpx] bg-w-10"
              @click="handleAddTicket(i)"
              v-else>
              <u-icon name="plus" color="rgba(255, 255, 255, 0.6)" size="54rpx"></u-icon>
              <view
                class="ml-auto mr-auto mt-2 w-[236rpx] text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
                >还未添加内容，去添加一个吧~</view
              >
            </view>
          </view>

          <view
            class="mb-[60rpx] mt-[102rpx] box-border flex h-[400rpx] w-[336rpx] flex-col items-center justify-center rounded-[20rpx] border-[2rpx] border-dashed border-[#9C75D3] bg-purpleBg"
            @click="$push({ name: 'RankEdit', params: { canSave: 1 } })"
            v-if="addCount > 0">
            <view class="mb-[4rpx] flex items-center justify-center">
              <u-icon name="plus" color="#C59BFF" size="20rpx"></u-icon>
              <text class="ml-[18rpx] font-Regular text-[32rpx] font-normal leading-[44rpx] text-[#C59BFF]"
                >设置新单项</text
              >
            </view>
            <view class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-50"
              >还可创建{{ addCount }}个榜单</view
            >
          </view>
        </view>

        <!-- 个人点评 -->
        <template v-if="commentText">
          <view
            class="mb-2.5 box-border w-full pl-3 pr-3 font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
            >说点什么</view
          >
          <view class="mb-2.5 box-border w-full pb-2.5 pl-3 pr-3 last:mb-0">
            <view
              class="box-border w-full break-all rounded-e-[40rpx] rounded-bl-[40rpx] bg-[#87769E] p-2 pb-2.5 pl-3 pt-2.5 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
              >“{{ commentText }}”</view
            >
          </view>
        </template>
      </view>

      <!-- 保存 -->
      <customBtn
        class="!fixed bottom-10 left-0 right-0 m-auto"
        @tap="handleSureAdd"
        text="保存"
        v-if="updateList && updateList.length" />
    </mescroll-uni>

    <!-- 回复输入框 -->
    <view class="w-full shrink-0 bg-[#3E365F]" v-if="!updateList || !updateList.length">
      <view class="flex min-h-[116rpx] items-center justify-start pb-[10rpx] pl-3 pr-3 pt-[10rpx]">
        <!-- 回复框 -->

        <view class="inputWrap relative h-fit grow">
          <u-textarea
            class="replyBox w-full"
            :adjustPosition="false"
            :focus="focus"
            :showConfirmBar="false"
            :style="textareaR"
            @blur="handleBlur"
            @focus="handleEmojiClose"
            @linechange="handleLineChange"
            autoHeight
            border="none"
            cursorSpacing="10"
            maxlength="40"
            placeholder="说点什么"
            ref="replyRef"
            v-model="replyCon"></u-textarea>
          <!-- @confirm="handleReplyComment" -->

          <image
            class="absolute bottom-0 right-[10rpx] top-0 m-auto block h-6 w-6"
            :src="$iconFormat('icon/emjoy.svg')"
            @click="handleEmojiOpen"
            mode="scaleToFill" />
        </view>

        <!-- 发送 -->
        <u-button
          class="sendBtn"
          type="primary"
          :customStyle="{ ...btnStyle }"
          :hairline="false"
          @click.stop.prevent="handleReplyComment"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          iconColor="#282656"
          shape="circle"
          v-if="replyCon"
          >发送</u-button
        >
      </view>

      <emoji :keyboardH="keyboardH" :show="emojiController" @emoji="handleEmojiChange" />

      <view class="w-full" :style="bheight"></view>
    </view>
  </view>

  <u-popup
    :closeOnClickOverlay="false"
    :show="shareController"
    @close="shareController = false"
    bgColor="transparent"
    mode="center"
    safeAreaInsetBottom>
    <view class="box-border flex h-0 w-screen grow flex-col items-start justify-start pt-[108rpx]">
      <image
        class="ml-3 block h-[68rpx] w-[68rpx] shrink-0"
        :src="$iconFormat('icon/close2.svg')"
        @click="shareController = false"
        mode="scaleToFill" />

      <scroll-view class="mb-[56rpx] ml-auto mr-auto mt-[56rpx] h-0 w-[660rpx] grow" scrollY>
        <!-- <image class="h-auto w-full" :src="sharePath" mode="widthFix"></image> -->
        <u-image :src="sharePath" height="auto" mode="widthFix" width="100%"></u-image>
      </scroll-view>

      <view
        class="flex w-full shrink-0 items-start justify-start rounded-t-[30rpx] bg-w-100 pb-2.5 pl-[42rpx] pr-[42rpx] pt-2.5">
        <button class="btnDef mr-[80rpx] h-fit w-fit p-0" open-type="share">
          <view class="flex w-[104rpx] flex-col items-center justify-start">
            <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/wechat.svg')" mode="scaleToFill" />
            <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">微信好友</text>
          </view>
        </button>

        <view class="flex w-[104rpx] flex-col items-center justify-start" @click="handleSavePost">
          <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/save.svg')" mode="scaleToFill" />
          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">保存图片</text>
        </view>
      </view>
    </view>
  </u-popup>

  <!-- padding: 32rpx 20rpx 58rpx 20rpx; -->
  <l-painter
    :css="`width: 660rpx; box-sizing: border-box; border-radius: 40rpx; background-image: url(${rankModal}); background-size: 100% auto;`"
    :pixelRatio="2"
    @fail="handleGetFail"
    @success="handleGetPost"
    fileType="png"
    hidden
    isCanvasToTempFilePath
    pathType="url">
    <l-painter-view css="width: 100%; padding: 32rpx 20rpx 58rpx 20rpx; box-sizing: border-box; border-radius: 40rpx;">
      <!-- 头像 -->
      <l-painter-image
        :src="userInfo.avatarUrl"
        css="position: absolute; top: 24rpx; left: 54rpx; display: block; object-fit: cover; width: 82rpx; height: 82rpx; border-radius: 50%;" />

      <!-- 昵称简介 -->
      <l-painter-text
        :text="userInfo.name"
        css="display: block; width: 490rpx; margin-bottom: 8rpx; padding-left: 132rpx; font-size: 32rpx; color: #000000; line-height: 32rpx; font-weight: bold;" />
      <l-painter-text
        :text="userInfo.personalizedSignature"
        css="display: block; width: 490rpx; margin-bottom: 8rpx; padding-left: 132rpx; font-size: 24rpx; color: rgba(0, 0, 0, 0.4); line-height: 32rpx; font-weight: bold; line-clamp: 1;" />
      <l-painter-text
        :text="`${lastTime}更新`"
        css="display: block; width: 490rpx; margin-bottom: 50rpx; padding-left: 132rpx; font-size: 24rpx; color: rgba(0, 0, 0, 0.4); line-height: 32rpx; font-weight: bold;" />

      <l-painter-text
        css="display: block; font-size: 40rpx; color: #3D3050; line-height: 56rpx; font-weight: bold; text-align: center;"
        text="演都榜单" />
      <l-painter-image
        :src="star3"
        css="display: block; object-fit: cover; width: 506rpx; height: 24rpx; margin: 0 auto 50rpx;" />

      <!-- 榜单 -->
      <l-painter-view css="display: block; width: 100%;">
        <l-painter-view
          :css="`display: inline-block; width: 300rpx; margin-left: ${
            (index + 1) % 2 === 0 ? '18rpx' : '0'
          }; margin-bottom: 50rpx;`"
          :key="index"
          v-for="(i, index) in shareList">
          <!-- 标题 -->
          <l-painter-view css="text-align: center; margin-bottom: 20rpx;">
            <l-painter-image
              :src="titlePartl2"
              css="display: inline-block; object-fit: cover; width: 46rpx; height: 46rpx;" />
            <l-painter-text
              :text="i.name"
              css="display: inline-block; max-width: 200rpx; font-size: 34rpx; color: #3D3050; line-height: 50rpx; font-weight: bold; text-align: center; line-clamp: 1;" />
            <l-painter-image
              :src="titlePartr2"
              css="display: inline-block; object-fit: cover; width: 46rpx; height: 46rpx;" />
          </l-painter-view>

          <!-- 封面 -->
          <l-painter-view css="width: 100%; margin-bottom: 20rpx; height: 200rpx;">
            <l-painter-image
              :src="i.pic"
              css="display: block; object-fit: cover; width: 300rpx; height: 200rpx; border-radius: 20rpx 20rpx 0 0;" />
          </l-painter-view>

          <!-- 剧目信息 -->
          <l-painter-text
            :text="i.repertoireName"
            css="display: block; width: 100%; height: 80rpx; margin-bottom: 10rpx; font-size: 28rpx; color: #3D3050; line-height: 40rpx; font-weight: bold; line-clamp: 2;" />
          <l-painter-text
            :text="'演出场地: ' + i.theaterName"
            css="display: block; width: 100%; margin-bottom: 8rpx; font-size: 22rpx; color: rgba(61, 48, 80, 0.6); line-height: 32rpx; line-clamp: 1;" />
          <l-painter-text
            :text="'演出时间: ' + i.time"
            css="display: block; width: 100%; font-size: 22rpx; color: rgba(61, 48, 80, 0.6); line-height: 32rpx;" />
        </l-painter-view>
      </l-painter-view>

      <template v-if="commentText">
        <l-painter-text
          css="display: block; width: 100%; font-size: 32rpx; color: #000000; line-height: 44rpx; font-weight: bold; margin-bottom: 20rpx;"
          text="说点什么" />

        <l-painter-view
          css="display: block; width: 100%; padding: 20rpx; background: rgba(255, 255, 255, 0.4); border-radius: 0rpx 40rpx 40rpx 40rpx; box-sizing: border-box;">
          <l-painter-text
            :text="commentText"
            css="display: block; width: 100%; font-size: 28rpx; color: rgba(0, 0, 0, 0.8); line-height: 40rpx;" />
        </l-painter-view>

        <l-painter-view css="display: block;  margin-top: 60rpx; text-align: right;">
          <l-painter-view css="display: inline-block; margin-bottom: 20rpx; text-align: right;">
            <l-painter-view css="display: block;">
              <l-painter-text
                css="display: inline-block; font-size: 24rpx; color: rgba(0, 0, 0, 0.6); line-height: 56rpx;"
                text="扫码生成个性榜单" />
              <l-painter-image
                :src="rightIcon"
                css="display: inline-block; object-fit: cover; width: 56rpx; height: 56rpx;" />
            </l-painter-view>

            <l-painter-text
              css="display: block; font-size: 28rpx; color: #000000; line-height: 40rpx; text-align: right;"
              text="上演都评演出" />
          </l-painter-view>

          <l-painter-image
            :src="qrcode"
            css="display: inline-block; object-fit: cover; width: 100rpx; height: 100rpx; margin-left: 16rpx; border-radius: 10rpx;" />
        </l-painter-view>
      </template>

      <l-painter-view css="display: block;  margin-top: 60rpx; text-align: right;" v-else>
        <l-painter-view css="display: inline-block; margin-bottom: 20rpx; text-align: right;">
          <l-painter-view css="display: block;">
            <l-painter-text
              css="display: inline-block; font-size: 24rpx; color: rgba(0, 0, 0, 0.6); line-height: 56rpx;"
              text="扫码生成个性榜单" />
            <l-painter-image
              :src="rightIcon"
              css="display: inline-block; object-fit: cover; width: 56rpx; height: 56rpx;" />
          </l-painter-view>

          <l-painter-text
            css="display: block; font-size: 28rpx; color: #000000; line-height: 40rpx; text-align: right;"
            text="上演都评演出" />
        </l-painter-view>

        <l-painter-image
          :src="qrcode"
          css="display: inline-block; object-fit: cover; width: 100rpx; height: 100rpx; margin-left: 16rpx; border-radius: 10rpx;" />
      </l-painter-view>
    </l-painter-view>
  </l-painter>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import {
    $LeaderboardComment,
    $LeaderboardCommentAdd,
    $LeaderboardCommentUpdate,
    $leaderboardLastTime,
    $leaderboardList,
    $leaderboardUpdate,
  } from '@/api/rank'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import emoji from '@/components/Emoji.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $push, $savePic, $toast } from '@/utils/methods'
  import dayjs from 'dayjs'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy
  const { downOpt1, upOpt1, userInfo } = storeToRefs(useGlobalStore())
  const { mescrollInit } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const lastTime = ref('') // 更新时间
  const list = ref<any>([]) // 榜单列表
  const commentList = ref<any>([]) // 评价列表

  const selTicket = ref<any>('') // 选中的电子票
  const updateList = ref<any>([])

  const replyCon = ref('') // 回复内容
  const focus = ref(false)
  const emojiController = ref(false) // 表情列表显示控制器

  const sharePath = ref('') // 分享图路径
  const rankModal = ref($iconFormat('rank/rankModal.webp'))
  const star3 = ref($iconFormat('rank/start.webp'))
  const titlePartl2 = ref($iconFormat('rank/titlePartl.webp'))
  const titlePartr2 = ref($iconFormat('rank/titlePartr.webp'))
  const rightIcon = ref($iconFormat('rank/right.webp'))
  const qrcode = ref($iconFormat('qrcode.png'))

  const commentText = computed(() => {
    let temp = commentList.value[commentList.value.length - 1]?.commentText

    return temp
  })

  /* 可以添加的单项数量 */
  const addCount = computed(() => {
    let temp = 3

    list.value?.map((i: any) => {
      if (i.type === 1) temp--
    })

    return temp
  })

  /* 发送按钮样式 */
  const btnStyle = ref({
    width: '180rpx',
    height: '62rpx',
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '28rpx',
    fontWeight: 600,
    padding: 0,
  })

  /* 底部安全高度 */
  const bheight = computed(() => {
    return { height: uni.$u.sys().safeAreaInsets.bottom + 'rpx' }
  })
  const keyboardH = ref(0) // 键盘高度
  const textareaR = ref('border-radius: 999999rpx;')

  const shareController = ref(false) // 分享控制器
  const shareList = ref<any>([])

  uni.onKeyboardHeightChange((res: any) => {
    keyboardH.value = res.height
  })

  onLoad(() => {
    uni.$on('updateRankList', () => {
      mescrollObj.value.triggerDownScroll()
    })

    uni.$on('selTicket1', (item: any) => {
      list.value.map((i: any) => {
        if (i.id === selTicket.value.id) {
          i.userLeaderboard.repertoireId = item.relationId
          i.userLeaderboard.userReceivingRecordsId = item.id
          i.coverPicture = item.coverPicture
          i.cityName = item.cityName
          i.repertoireName = item.repertoireName
          i.theaterName = item.theaterName
          i.time = item.time
        }
      })

      let isHas: boolean = false

      updateList.value.map((i: any) => {
        if (i.id === selTicket.value.id) {
          i.userLeaderboard.repertoireId = item.relationId
          i.userLeaderboard.userReceivingRecordsId = item.id
          isHas = true
        }
      })

      if (!isHas && selTicket.value) {
        updateList.value.push({
          id: selTicket.value.id,
          name: selTicket.value.name,
          type: selTicket.value.type,
          userId: userInfo.value.id,
          coverPicture: item.coverPicture,
          userLeaderboard: {
            repertoireId: item.relationId,
            userReceivingRecordsId: item.id,
          },
        })
      }
    })
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $LeaderboardComment().then((res: any) => {
      commentList.value = res.data
    })

    $leaderboardLastTime().then((res: any) => {
      lastTime.value = res.data ? dayjs(res.data).format('YYYY-MM-DD') : ''
    })

    $leaderboardList()
      .then((res: any) => {
        let temp: any = []

        let oldList: any = []
        if (updateList.value && updateList.value.length) oldList = _.cloneDeep(list.value)
        res.data.map((i: any) => {
          i.time = dayjs(i.time).format('YYYY-MM-DD HH:mm')

          if (!i.userLeaderboard) i.userLeaderboard = {}

          if (i.coverPicture) {
            i.pic = $picFormat(i.coverPicture)

            temp.push(i)
          }

          if (oldList && oldList.length) {
            oldList.map((j: any) => {
              if (i.id === j.id && j.type === 0) {
                i.cityName = j.cityName
                i.coverPicture = j.coverPicture
                i.name = j.name
                i.repertoireName = j.repertoireName
                i.theaterName = j.theaterName
                i.time = j.time
                i.userLeaderboard.repertoireId = j.userLeaderboard.repertoireId
                i.userLeaderboard.userReceivingRecordsId = j.userLeaderboard.userReceivingRecordsId
              }
            })
          }
        })

        shareList.value = temp

        list.value = res.data

        mescroll.endSuccess(1, false)
      })
      .catch((err: any) => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 添加电子票 */
  const handleAddTicket = (i: any) => {
    selTicket.value = i
    $push({ name: 'SelTicket', params: { type: '1' } })
  }

  /* 确认添加电子票 */
  const handleSureAdd = async () => {
    updateList.value.map(async (i: any, index: number) => {
      await $leaderboardUpdate(i)

      if (index === updateList.value.length - 1) {
        selTicket.value = ''
        updateList.value = []
        mescrollObj.value.triggerDownScroll()
        $toast(app, '替换成功')
      }
    })
  }

  /* 回复输入框失焦 */
  const handleBlur = () => {
    setTimeout(() => {
      if (!emojiController.value) {
        replyCon.value = ''
        focus.value = false
      }
    }, 1000)
  }

  /* 表情列表打开 */
  const handleEmojiOpen = () => {
    emojiController.value = !emojiController.value
  }

  /* 表情列表关闭 */
  const handleEmojiClose = () => {
    emojiController.value = false
  }

  /* 选中表情 */
  const handleEmojiChange = (e: any) => {
    replyCon.value += e
  }

  /* 回复评论 */
  const handleReplyComment = () => {
    if (replyCon.value) {
      if (commentList.value && commentList.value.length) {
        $LeaderboardCommentUpdate({
          id: commentList.value[0].id,
          userId: userInfo.value.id,
          commentUserId: userInfo.value.id,
          commentText: replyCon.value,
        }).then((res: any) => {
          replyCon.value = ''
          mescrollObj.value.triggerDownScroll()
        })
      } else {
        $LeaderboardCommentAdd({
          userId: userInfo.value.id,
          commentUserId: userInfo.value.id,
          commentText: replyCon.value,
        }).then((res: any) => {
          replyCon.value = ''
          mescrollObj.value.triggerDownScroll()
        })
      }
    } else {
      $toast(app, '回复内容不能为空')
    }
  }

  /* 行数变化 */
  const handleLineChange = (e: any) => {
    if (e.detail.lineCount <= 1) {
      textareaR.value = 'border-radius: 999999rpx;'
    } else {
      textareaR.value = 'border-radius: 20rpx;'
    }
  }

  /* 获取生成的海报 */
  const handleGetPost = (val: any) => {
    // $toast(app, '生成海报成功')

    sharePath.value = val

    uni.$u.mpShare.imageUrl = val
  }

  /* 生成失败 */
  const handleGetFail = (err: any) => {
    $toast(app, '生成海报失败')
  }

  /* 保存海报 */
  const handleSavePost = () => {
    $savePic(app, sharePath.value)
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/rank.webp');

    .replyBox {
      @apply bg-w-10 pl-[38rpx] pr-[68rpx] #{!important};

      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      height: fit-content;
      min-height: 80rpx !important;
      padding-top: 10rpx !important;
      padding-bottom: 10rpx !important;

      &:deep(.u-textarea__field) {
        width: 100% !important;
        min-height: 36rpx !important;
        max-height: 400rpx;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 28rpx !important;
        font-weight: 400;
        line-height: 36rpx !important;
        color: #fff !important;
        resize: none;
      }

      &:deep(.input-placeholder) {
        padding: 12rpx 0 !important;
        margin-top: 20rpx !important;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 28rpx !important;
        font-weight: 400;
        line-height: 36rpx !important;
        color: rgb(255 255 255 / 40%) !important;
      }
    }

    .sendBtn {
      margin-left: 20rpx;
    }
  }

  .u-popup {
    &:deep(> .u-transition) {
      height: 100vh !important;
    }

    &:deep(.u-popup__content) {
      flex-shrink: 0;
      height: 100vh !important;

      .u-safe-bottom {
        flex-shrink: 0 !important;
        background: #fff;
      }
    }
  }
</style>
