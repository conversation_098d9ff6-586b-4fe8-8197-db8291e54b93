<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-[#1F1933] bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="问大家"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <view class="flex w-full grow flex-col items-start justify-start rounded-t-[30rpx] bg-[#1F1933] pt-2.5">
      <!-- 剧场信息 -->
      <view
        class="m-auto mb-2.5 flex w-[682rpx] shrink-0 items-start justify-start rounded-[20rpx] bg-deepbg pb-1 pl-[10rpx] pr-[10rpx] pt-1"
        v-if="type === 1">
        <u-image
          class="shrink-0"
          :src="detail.coverPicture"
          bgColor="transparent"
          height="80rpx"
          mode="aspectFill"
          radius="10rpx"
          width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{
            detail.name || '-'
          }}</view>
          <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
            >地址:{{ detail.address || '-' }}</view
          >
        </view>
      </view>
      <!-- 剧目信息 -->
      <view
        class="m-auto mb-2.5 flex w-[682rpx] shrink-0 items-center justify-start rounded-[20rpx] bg-deepbg pb-1 pl-[10rpx] pr-[10rpx] pt-1"
        v-else-if="type === 2">
        <u-image
          class="shrink-0"
          :src="detail.coverPicture"
          bgColor="transparent"
          height="80rpx"
          mode="aspectFill"
          radius="10rpx"
          width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{
            detail.name || '-'
          }}</view>
          <!-- <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60">演出时间: {{ '-' }} | 演出场地:{{ '-' }}</view> -->
        </view>
      </view>

      <view
        class="mb-[34rpx] w-full shrink-0 text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#FFC374] opacity-60"
        >问大家随机邀请真实已看过的用户回答您的问题</view
      >

      <mescroll-uni
        class="block h-0 w-full grow"
        :down="downOpt1"
        :fixed="false"
        :up="upOpt3"
        @down="downCallback"
        @init="mescrollInit"
        @topclick="$topclick"
        @up="upCallback">
        <!-- 空状态 -->
        <view class="emptyWrap w-full" v-if="!list || !list.length">
          <u-empty
            :icon="$iconFormat('empty/ask.svg')"
            height="322rpx"
            mode="data"
            text="暂无提问，做第一个提问的人吧~"
            width="472rpx"></u-empty>
        </view>

        <!-- 问题列表 -->
        <view class="m-auto w-[702rpx]">
          <!-- 问答 item -->
          <view
            class="mb-2.5 w-full border-b-[1rpx] border-solid border-w-10 pb-2.5 last:mb-0"
            :key="i.id"
            v-for="i in list">
            <!-- 提问 -->
            <view class="w-fuill mb-2.5 flex items-start justify-start last:mb-0">
              <!-- 头像 -->
              <view class="relative mr-2.5 h-[60rpx] w-[60rpx] shrink-0">
                <u-avatar
                  :defaultUrl="$iconFormat('avatar.jpg')"
                  :src="$picFormat(i.userMerchantId ? i.merchantUserAvatar : i.userAvatar)"
                  size="60rpx"></u-avatar>
                <image
                  class="absolute bottom-0 right-0 block h-4 w-4"
                  :src="$iconFormat('icon/ask3.svg')"
                  mode="scaleToFill" />
              </view>
              <!-- 问题信息 -->
              <view class="grow">
                <view class="mb-[4rpx] flex w-full items-center justify-start">
                  <!-- 用户名 -->
                  <text class="mr-1.5 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80">{{
                    i.userMerchantId ? i.userName : i.userName
                  }}</text>
                  <!-- 勋章 -->
                  <medal :color="i.color" :level="i.rankMedalLevel" :text="i.rankMedalName" />
                </view>
                <!-- 问题 -->
                <view
                  class="mb-[4rpx] w-full break-all font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
                  >{{ i.content }}</view
                >
                <!-- 时间 -->
                <view class="w-full font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
                  i.createTime
                }}</view>
              </view>
            </view>

            <!-- 回答 -->
            <view class="w-fuill mb-2.5 flex items-start justify-start last:mb-0" v-if="i.issueResponse">
              <!-- 头像 -->
              <view class="relative mr-2.5 h-[60rpx] w-[60rpx] shrink-0">
                <u-avatar
                  :defaultUrl="$iconFormat('avatar.jpg')"
                  :src="
                    $picFormat(
                      i.issueResponse.userMerchantId ? i.issueResponse.merchantUserAvatar : i.issueResponse.userAvatar
                    )
                  "
                  size="60rpx"></u-avatar>
                <image
                  class="absolute bottom-0 right-0 block h-4 w-4"
                  :src="$iconFormat('icon/ask4.svg')"
                  mode="scaleToFill" />
              </view>
              <!-- 答案信息 -->
              <view class="grow">
                <view class="mb-[4rpx] flex w-full items-center justify-start">
                  <!-- 用户名 -->
                  <text class="mr-1.5 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-80">{{
                    i.issueResponse.userName
                  }}</text>
                  <!-- 勋章 -->
                  <medal
                    :color="i.issueResponse.color"
                    :level="i.issueResponse.rankMedalLevel"
                    :text="i.issueResponse.rankMedalName" />
                </view>
                <!-- 回答 -->
                <view
                  class="mb-[4rpx] w-full break-all font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
                  >{{ i.issueResponse.lastContent || '-' }}</view
                >
                <!-- 时间 -->
                <view class="w-full font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
                  i.issueResponse.createTime
                }}</view>
              </view>
            </view>

            <!-- 回答折叠 -->
            <view
              class="flex w-full items-center justify-start pl-[80rpx]"
              @tap="handleCheckDetail(i)"
              v-if="i.replyCount > 1">
              <text class="font-Regular text-[26rpx] font-normal leading-[32rpx] text-[#C984E9] opacity-80"
                >全部{{ i.replyCount || 0 }}个回答</text
              >
              <image class="block h-2.5 w-2.5" :src="$iconFormat('arrow/right2.svg')" mode="scaleToFill" />
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
  </view>

  <view class="fixed bottom-[100rpx] left-0 right-0 z-10 m-auto w-fit">
    <customBtn :text="!list || !list.length ? '立即去提问' : '我也要提问'" @tap="handleAsk" icon="icon/ask2.svg" />
  </view>

  <ask :askType="type" :infoId="id" :popupContaoller="popupContaoller1" :way="1" @close="handleCloseAskPopup" />
  <askDetail :popupContaoller="popupContaoller2" :selAsk="selAsk" @close="handleCloseDetailPopup" v-if="selAsk" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $issueList } from '@/api/ask'
  import { $repertoireDetails2 } from '@/api/repertoire'
  import { $theaterDetails } from '@/api/theater'
  import ask from '@/components/Ask/Ask.vue'
  import askDetail from '@/components/Ask/Detasil.vue'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import medal from '@/components/Medal.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $hideName, $iconFormat, $picFormat, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const type = ref(1) // 1：剧场 2：剧目
  const id = ref(0) // 剧场/剧目id
  const theaterName = ref('')
  const detail = ref<any>('') // 剧场/剧目详情
  const list = ref<any>('') // 问题列表

  const popupContaoller1 = ref(false) // 提问弹窗控制器

  const popupContaoller2 = ref(false) // 详情弹窗控制器
  const selAsk = ref<any>('') // 选中的问题

  onLoad((params: any) => {
    id.value = params.id - 0
    type.value = params.type - 0
    theaterName.value = params.theaterName
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    if (type.value == 1) {
      /* 剧场详情 */
      $theaterDetails({ id: id.value, userId: userInfo.value.id }).then((res: any) => {
        if (res.data.coverPicture) res.data.coverPicture = $picFormat(res.data.coverPicture)

        detail.value = res.data
      })
    } else if (type.value == 2) {
      /* 剧目详情 */
      $repertoireDetails2({ id: id.value, userId: userInfo.value.id }).then((res: any) => {
        if (res.data.coverPicture) res.data.coverPicture = $picFormat(res.data.coverPicture)

        detail.value = res.data
      })
    }

    /* 问题列表 */
    $issueList({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      parentId: 0,
      theaterId: type.value === 1 ? id.value : undefined,
      repertoireId: type.value === 2 ? id.value : undefined,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.userName) i.userName = $hideName(i.userName)
          if (i.merchantUserName) i.merchantUserName = $hideName(i.merchantUserName)
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY/MM/DD')

          if (i.issueResponse) {
            if (i.issueResponse.userName) i.issueResponse.userName = $hideName(i.issueResponse.userName)
            if (i.issueResponse.merchantUserName)
              i.issueResponse.merchantUserName = $hideName(i.issueResponse.merchantUserName)
            if (i.issueResponse.createTime)
              i.issueResponse.createTime = dayjs(i.issueResponse.createTime).format('YYYY/MM/DD')
          }
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 我要提问 */
  const handleAsk = () => {
    popupContaoller1.value = true
  }

  /* 发布成功 */
  const handleCloseAskPopup = (flag?: boolean) => {
    popupContaoller1.value = false

    if (flag) mescrollObj.value.resetUpScroll()
  }

  /* 查看问答详情 */
  const handleCheckDetail = (i: any) => {
    selAsk.value = i
    popupContaoller2.value = true
  }

  /* 关闭详情 */
  const handleCloseDetailPopup = () => {
    selAsk.value = {}
    popupContaoller2.value = false
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/history.webp');

    .emptyWrap {
      .u-empty {
        &:deep(.u-empty__text) {
          margin: 0;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 26rpx !important;
          font-weight: 400;
          line-height: 36rpx;
          color: #fff !important;
          opacity: 0.6;
        }
      }
    }
  }

  :deep(.mescroll-wxs-content) {
    padding-bottom: 100rpx;
  }
</style>
