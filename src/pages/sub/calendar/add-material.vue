<template>
  <view class="min-h-screen bg-deepbg">
    <!-- 自定义导航栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-deepbg" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="flex items-center">
        <u-icon name="arrow-left" color="#fff" size="20" @click="goBack" />
        <text class="text-white text-lg font-medium ml-3">{{ isEdit ? '编辑物料' : '发布物料' }}</text>
      </view>
      <view class="px-4 py-1 bg-primary rounded-full" @click="saveMaterial">
        <text class="text-white text-sm">{{ isEdit ? '保存' : '发布' }}</text>
      </view>
    </view>

    <scroll-view scroll-y class="flex-1">
      <view class="px-4 py-4">
        <!-- 物料图片 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">物料图片 *</text>
          <view class="grid grid-cols-3 gap-3">
            <view 
              v-for="(image, index) in formData.images" 
              :key="index"
              class="relative aspect-square bg-gray-800 rounded-lg overflow-hidden"
            >
              <image :src="image" class="w-full h-full object-cover" />
              <view 
                class="absolute top-1 right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                @click="removeImage(index)"
              >
                <u-icon name="close" color="#fff" size="12" />
              </view>
            </view>
            
            <view 
              v-if="formData.images.length < 9"
              class="aspect-square bg-gray-800 rounded-lg flex flex-col items-center justify-center border-2 border-dashed border-gray-600"
              @click="chooseImage"
            >
              <u-icon name="plus" color="#666" size="24" />
              <text class="text-gray-500 text-xs mt-1">添加图片</text>
            </view>
          </view>
        </view>

        <!-- 物料标题 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">物料标题 *</text>
          <u-input
            v-model="formData.title"
            placeholder="请输入物料标题"
            :customStyle="inputStyle"
            placeholderStyle="color: #666"
          />
        </view>

        <!-- 发放时间 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">发放时间 *</text>
          <view class="flex space-x-3">
            <view class="flex-1">
              <u-input
                v-model="formData.date"
                placeholder="选择日期"
                :customStyle="inputStyle"
                placeholderStyle="color: #666"
                readonly
                @click="showDatePicker = true"
              />
            </view>
            <view class="flex-1">
              <u-input
                v-model="formData.time"
                placeholder="选择时间"
                :customStyle="inputStyle"
                placeholderStyle="color: #666"
                readonly
                @click="showTimePicker = true"
              />
            </view>
          </view>
        </view>

        <!-- 发放地点 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">发放地点 *</text>
          <u-input
            v-model="formData.location"
            placeholder="请输入发放地点"
            :customStyle="inputStyle"
            placeholderStyle="color: #666"
          />
        </view>

        <!-- 领取条件 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">领取条件 *</text>
          <u-textarea
            v-model="formData.condition"
            placeholder="请详细描述领取条件，如：需要持有当日演出票据，每人限领一份等"
            :customStyle="textareaStyle"
            placeholderStyle="color: #666"
            maxlength="500"
            count
          />
        </view>

        <!-- 关联演出 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">关联演出</text>
          <view 
            class="bg-gray-800 rounded-lg p-4 flex items-center justify-between"
            @click="selectShow"
          >
            <view v-if="formData.relatedShow" class="flex items-center">
              <image :src="formData.relatedShow.poster" class="w-12 h-16 rounded object-cover mr-3" />
              <view>
                <text class="text-white text-sm font-medium block">{{ formData.relatedShow.title }}</text>
                <text class="text-gray-400 text-xs">{{ formData.relatedShow.date }}</text>
              </view>
            </view>
            <view v-else class="flex items-center">
              <text class="text-gray-400 text-sm">选择关联演出（可选）</text>
            </view>
            <u-icon name="arrow-right" color="#666" size="16" />
          </view>
        </view>

        <!-- 可见性设置 -->
        <view class="mb-6">
          <text class="text-white text-base font-medium block mb-3">可见性设置</text>
          <view class="space-y-3">
            <view 
              v-for="option in visibilityOptions" 
              :key="option.value"
              class="flex items-center justify-between bg-gray-800 rounded-lg p-4"
              @click="formData.visibility = option.value"
            >
              <view>
                <text class="text-white text-sm font-medium block">{{ option.label }}</text>
                <text class="text-gray-400 text-xs">{{ option.desc }}</text>
              </view>
              <u-radio 
                :checked="formData.visibility === option.value"
                activeColor="#9C75D3"
              />
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 日期选择器 -->
    <u-datetime-picker
      v-model:show="showDatePicker"
      v-model:value="dateValue"
      mode="date"
      @confirm="onDateConfirm"
    />

    <!-- 时间选择器 -->
    <u-datetime-picker
      v-model:show="showTimePicker"
      v-model:value="timeValue"
      mode="time"
      @confirm="onTimeConfirm"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 状态管理
const statusBarHeight = ref(0)
const isEdit = ref(false)
const showDatePicker = ref(false)
const showTimePicker = ref(false)
const dateValue = ref(Date.now())
const timeValue = ref(Date.now())

// 表单数据
const formData = ref({
  images: [],
  title: '',
  date: '',
  time: '',
  location: '',
  condition: '',
  relatedShow: null,
  visibility: 'public'
})

// 样式配置
const inputStyle = {
  backgroundColor: '#374151',
  borderColor: 'transparent',
  color: '#fff',
  height: '44px',
  borderRadius: '8px'
}

const textareaStyle = {
  backgroundColor: '#374151',
  borderColor: 'transparent',
  color: '#fff',
  borderRadius: '8px'
}

// 可见性选项
const visibilityOptions = [
  {
    value: 'public',
    label: '公开',
    desc: '所有用户都可以看到'
  },
  {
    value: 'show_audience',
    label: '同场观众',
    desc: '只有同场次观众可以看到'
  },
  {
    value: 'followers',
    label: '关注者',
    desc: '只有关注我的用户可以看到'
  }
]

// 方法
const goBack = () => {
  uni.navigateBack()
}

const chooseImage = () => {
  uni.chooseImage({
    count: 9 - formData.value.images.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.value.images.push(...res.tempFilePaths)
    }
  })
}

const removeImage = (index: number) => {
  formData.value.images.splice(index, 1)
}

const onDateConfirm = (value: any) => {
  formData.value.date = value.result
  showDatePicker.value = false
}

const onTimeConfirm = (value: any) => {
  formData.value.time = value.result
  showTimePicker.value = false
}

const selectShow = () => {
  // 选择关联演出的逻辑
  uni.showToast({
    title: '选择演出功能开发中',
    icon: 'none'
  })
}

const saveMaterial = () => {
  // 表单验证
  if (!formData.value.title.trim()) {
    uni.showToast({
      title: '请输入物料标题',
      icon: 'none'
    })
    return
  }

  if (formData.value.images.length === 0) {
    uni.showToast({
      title: '请上传物料图片',
      icon: 'none'
    })
    return
  }

  // 保存逻辑
  uni.showLoading({
    title: isEdit.value ? '保存中...' : '发布中...'
  })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: isEdit.value ? '保存成功' : '发布成功',
      icon: 'success'
    })
    
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 2000)
}

onLoad((options) => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  if (options?.mode === 'edit') {
    isEdit.value = true
    // 加载编辑数据
  }
})
</script>