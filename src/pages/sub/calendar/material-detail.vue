<template>
  <view class="min-h-screen bg-deepbg">
    <!-- 自定义导航栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-deepbg" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="flex items-center">
        <text class="fas fa-arrow-left" style="font-size: 20rpx; color: #fff;" @click="goBack"></text>
        <text class="text-lg font-medium ml-3" style="color: #FFFFFF;">物料详情</text>
      </view>
      <view v-if="isOwner" class="flex items-center space-x-3">
        <text class="fas fa-edit" style="font-size: 18rpx; color: #9C75D3;" @click="editMaterial"></text>
        <text class="fas fa-trash" style="font-size: 18rpx; color: #ff4757;" @click="deleteMaterial"></text>
      </view>
    </view>

    <!-- 物料信息 -->
    <view class="px-4 py-4">
      <view class="bg-gray-800 rounded-lg overflow-hidden">
        <!-- 物料图片 -->
        <image :src="materialInfo.image" class="w-full h-48 object-cover" />
        
        <view class="p-4">
          <!-- 基本信息 -->
          <view class="mb-4">
            <text class="text-lg font-medium block mb-2" style="color: #FFFFFF;">{{ materialInfo.title }}</text>
            <view class="flex items-center space-x-4 text-sm text-gray-400">
              <view class="flex items-center">
                <text class="fas fa-clock mr-1" style="font-size: 14rpx; color: #666;"></text>
                <text style="color: rgba(255, 255, 255, 0.6);">{{ materialInfo.publishTime }}</text>
              </view>
              <view class="flex items-center">
                <text class="fas fa-map-marker-alt mr-1" style="font-size: 14rpx; color: #666;"></text>
                <text style="color: rgba(255, 255, 255, 0.6);">{{ materialInfo.location }}</text>
              </view>
            </view>
          </view>

          <!-- 领取条件 -->
          <view class="mb-4">
            <text class="text-base font-medium block mb-2" style="color: #FFFFFF;">领取条件</text>
            <text class="text-sm leading-relaxed" style="color: rgba(255, 255, 255, 0.8);">{{ materialInfo.condition }}</text>
          </view>

          <!-- 发布者信息 -->
          <view class="flex items-center justify-between mb-4 p-3 bg-gray-700 rounded-lg">
            <view class="flex items-center">
              <image :src="materialInfo.publisher.avatar" class="w-8 h-8 rounded-full mr-3" />
              <view>
                <text class="text-sm font-medium block" style="color: #FFFFFF;">{{ materialInfo.publisher.name }}</text>
                <text class="text-xs" style="color: rgba(255, 255, 255, 0.6);">发布者</text>
              </view>
            </view>
            <view v-if="!isOwner" class="px-3 py-1 bg-primary rounded-full">
              <text class="text-xs" style="color: #FFFFFF;">关注</text>
            </view>
          </view>

          <!-- 想领统计 -->
          <view class="mb-4">
            <view class="flex items-center justify-between mb-3">
              <text class="text-base font-medium" style="color: #FFFFFF;">想领名单 ({{ wantList.length }})</text>
              <text class="text-sm" style="color: #9C75D3;" @click="showAllWantList">查看全部</text>
            </view>
            
            <view v-if="wantList.length > 0" class="flex items-center">
              <view class="flex -space-x-2 mr-3">
                <image 
                  v-for="(user, index) in wantList.slice(0, 5)" 
                  :key="user.id"
                  :src="user.avatar" 
                  class="w-8 h-8 rounded-full border-2 border-gray-800"
                />
                <view v-if="wantList.length > 5" class="w-8 h-8 rounded-full bg-gray-600 border-2 border-gray-800 flex items-center justify-center">
                  <text class="text-xs" style="color: #FFFFFF;">+{{ wantList.length - 5 }}</text>
                </view>
              </view>
              <text class="text-sm" style="color: rgba(255, 255, 255, 0.6);">等{{ wantList.length }}人想领</text>
            </view>
            
            <view v-else class="text-center py-4">
              <text class="text-sm" style="color: rgba(255, 255, 255, 0.6);">暂无人想领</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-deepbg border-t border-gray-700 p-4">
      <view v-if="!isOwner" class="flex space-x-3">
        <view class="flex-1">
          <u-button 
            :type="hasWanted ? 'default' : 'primary'"
            :text="hasWanted ? '取消想领' : '我想领'"
            @click="toggleWant"
            customStyle="height: 44px; border-radius: 22px;"
          />
        </view>
        <view class="w-12 h-11 bg-gray-800 rounded-full flex items-center justify-center" @click="shareMaterial">
          <text class="fas fa-share" style="font-size: 18rpx; color: #9C75D3;"></text>
        </view>
      </view>
      
      <view v-else class="text-center">
        <text class="text-sm" style="color: rgba(255, 255, 255, 0.6);">这是您发布的物料</text>
      </view>
    </view>

    <!-- 想领名单弹窗 -->
    <u-popup v-model:show="showWantListModal" mode="bottom" :round="20">
      <view class="bg-gray-800 p-4" style="max-height: 60vh;">
        <view class="text-center mb-4">
          <text class="text-lg font-medium" style="color: #FFFFFF;">想领名单</text>
        </view>
        
        <scroll-view scroll-y style="max-height: 50vh;">
          <view v-for="user in wantList" :key="user.id" class="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
            <view class="flex items-center">
              <image :src="user.avatar" class="w-10 h-10 rounded-full mr-3" />
              <view>
                <text class="text-sm font-medium block" style="color: #FFFFFF;">{{ user.name }}</text>
                <text class="text-xs" style="color: rgba(255, 255, 255, 0.6);">{{ user.wantTime }}</text>
              </view>
            </view>
            <view class="px-3 py-1 bg-primary rounded-full">
              <text class="text-xs" style="color: #FFFFFF;">关注</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 状态管理
const statusBarHeight = ref(0)
const materialId = ref('')
const showWantListModal = ref(false)
const hasWanted = ref(false)

// 模拟数据 - 便于后续替换为真实API
const mockMaterialData = {
  '1': {
    id: '1',
    title: '《狮子王》限量版海报',
    image: 'https://picsum.photos/400/300?random=301',
    publishTime: '2024-07-28 14:30',
    location: '上海大剧院门厅',
    condition: '需要持有当日演出票据，每人限领一份。领取时间为演出开始前30分钟至演出结束后30分钟。海报数量有限，先到先得。',
    publisher: {
      id: '1',
      name: '上海大剧院官方',
      avatar: 'https://picsum.photos/100/100?random=101'
    }
  },
  '2': {
    id: '2',
    title: '音乐剧纪念品套装',
    image: 'https://picsum.photos/400/300?random=302',
    publishTime: '2024-07-30 19:00',
    location: '剧院商店',
    condition: '购买任意价位演出票即可免费领取一套纪念品，包含徽章、贴纸、明信片等。每张票限领一套。',
    publisher: {
      id: '2',
      name: '剧迷小组长',
      avatar: 'https://picsum.photos/100/100?random=102'
    }
  }
}

const mockWantListData = {
  '1': [
    {
      id: '1',
      name: '音乐剧爱好者小王',
      avatar: 'https://picsum.photos/100/100?random=201',
      wantTime: '2小时前'
    },
    {
      id: '2',
      name: '戏剧迷小李',
      avatar: 'https://picsum.photos/100/100?random=202',
      wantTime: '3小时前'
    },
    {
      id: '3',
      name: '看剧小达人',
      avatar: 'https://picsum.photos/100/100?random=203',
      wantTime: '5小时前'
    },
    {
      id: '4',
      name: '剧场常客',
      avatar: 'https://picsum.photos/100/100?random=204',
      wantTime: '1天前'
    },
    {
      id: '5',
      name: '演出收藏家',
      avatar: 'https://picsum.photos/100/100?random=205',
      wantTime: '1天前'
    },
    {
      id: '6',
      name: '音乐剧新手',
      avatar: 'https://picsum.photos/100/100?random=206',
      wantTime: '2天前'
    }
  ],
  '2': [
    {
      id: '7',
      name: '剧迷团长',
      avatar: 'https://picsum.photos/100/100?random=207',
      wantTime: '1小时前'
    },
    {
      id: '8',
      name: '周末看剧人',
      avatar: 'https://picsum.photos/100/100?random=208',
      wantTime: '4小时前'
    }
  ]
}

// 数据
const materialInfo = ref(mockMaterialData['1'])
const wantList = ref(mockWantListData['1'] || [])

// 计算属性
const isOwner = computed(() => {
  // 判断是否为发布者
  return false
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const toggleWant = () => {
  hasWanted.value = !hasWanted.value
  uni.showToast({
    title: hasWanted.value ? '已登记想领' : '已取消想领',
    icon: 'success'
  })
}

const showAllWantList = () => {
  showWantListModal.value = true
}

const shareMaterial = () => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

const editMaterial = () => {
  uni.navigateTo({
    url: `/pages/sub/calendar/add-material?id=${materialId.value}&mode=edit`
  })
}

const deleteMaterial = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个物料信息吗？',
    success: (res) => {
      if (res.confirm) {
        // 删除逻辑
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}

onLoad((options) => {
  materialId.value = options?.id || ''
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 加载物料详情
  loadMaterialDetail()
})

const loadMaterialDetail = () => {
  // 模拟加载数据
  const data = mockMaterialData[materialId.value]
  const wants = mockWantListData[materialId.value]
  
  if (data) {
    materialInfo.value = data
    wantList.value = wants || []
  } else {
    uni.showToast({
      title: '物料不存在',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
  
  // 模拟检查是否已经想领
  hasWanted.value = Math.random() > 0.5
}
</script>