<template>
  <view class="min-h-screen bg-deepbg">
    <!-- 自定义导航栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-deepbg" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="flex items-center">
        <text class="fas fa-arrow-left" style="font-size: 20rpx; color: #fff;" @click="goBack"></text>
        <text class="text-lg font-medium ml-3" style="color: #FFFFFF;">物料管理</text>
      </view>
      <view class="px-4 py-1 bg-primary rounded-full" @click="addMaterial">
        <text class="text-sm" style="color: #FFFFFF;">发布</text>
      </view>
    </view>

    <!-- 标签切换 -->
    <view class="px-4 py-3">
      <view class="flex bg-gray-800 rounded-lg p-1">
        <view 
          class="flex-1 text-center py-2 rounded-md transition-all"
          :class="currentTab === 'published' ? 'bg-primary text-white' : 'text-gray-400'"
          @click="switchTab('published')"
        >
          <text style="color: #FFFFFF;">我发布的</text>
        </view>
        <view 
          class="flex-1 text-center py-2 rounded-md transition-all"
          :class="currentTab === 'wanted' ? 'bg-primary text-white' : 'text-gray-400'"
          @click="switchTab('wanted')"
        >
          <text style="color: #FFFFFF;">我登记的</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="flex-1 px-4">
      <!-- 我发布的 -->
      <view v-if="currentTab === 'published'">
        <view v-if="publishedList.length === 0" class="text-center py-20">
          <text class="fas fa-folder-open" style="font-size: 48rpx; color: #666;"></text>
          <text class="text-sm block mt-3" style="color: rgba(255, 255, 255, 0.6);">暂无发布的物料</text>
          <view class="mt-4">
            <u-button 
              type="primary" 
              text="发布物料" 
              size="small"
              @click="addMaterial"
              customStyle="width: 120px;"
            />
          </view>
        </view>

        <view v-else class="space-y-3">
          <view 
            v-for="item in publishedList" 
            :key="item.id"
            class="bg-gray-800 rounded-lg p-4"
            @click="viewMaterial(item)"
          >
            <view class="flex space-x-3">
              <image :src="item.image" class="w-16 h-16 rounded-lg object-cover" />
              <view class="flex-1">
                <view class="flex items-start justify-between mb-2">
                  <text class="text-sm font-medium flex-1 mr-2" style="color: #FFFFFF;">{{ item.title }}</text>
                  <view class="flex items-center space-x-2">
                    <text class="fas fa-edit" style="font-size: 16rpx; color: #9C75D3;" @click.stop="editMaterial(item)"></text>
                    <text class="fas fa-trash" style="font-size: 16rpx; color: #ff4757;" @click.stop="deleteMaterial(item)"></text>
                  </view>
                </view>
                
                <view class="flex items-center space-x-4 text-xs text-gray-400 mb-2">
                  <view class="flex items-center">
                    <text class="fas fa-clock mr-1" style="font-size: 12rpx; color: #666;"></text>
                    <text style="color: rgba(255, 255, 255, 0.6);">{{ item.publishTime }}</text>
                  </view>
                  <view class="flex items-center">
                    <text class="fas fa-map-marker-alt mr-1" style="font-size: 12rpx; color: #666;"></text>
                    <text style="color: rgba(255, 255, 255, 0.6);">{{ item.location }}</text>
                  </view>
                </view>

                <view class="flex items-center justify-between">
                  <view class="flex items-center space-x-3">
                    <view class="flex items-center">
                      <text class="fas fa-heart mr-1" style="font-size: 14rpx; color: #ff6b6b;"></text>
                      <text class="text-xs" style="color: rgba(255, 255, 255, 0.6);">{{ item.wantCount }}人想领</text>
                    </view>
                    <view 
                      class="px-2 py-1 rounded text-xs"
                      :class="getStatusClass(item.status)"
                    >
                      {{ getStatusText(item.status) }}
                    </view>
                  </view>
                  <text class="fas fa-chevron-right" style="font-size: 12rpx; color: #666;"></text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 我登记的 -->
      <view v-if="currentTab === 'wanted'">
        <view v-if="wantedList.length === 0" class="text-center py-20">
          <text class="fas fa-heart" style="font-size: 48rpx; color: #666;"></text>
          <text class="text-sm block mt-3" style="color: rgba(255, 255, 255, 0.6);">暂无登记的物料</text>
          <text class="text-xs mt-1" style="color: rgba(255, 255, 255, 0.4);">去日历页面看看有什么物料吧</text>
        </view>

        <view v-else class="space-y-3">
          <view 
            v-for="item in wantedList" 
            :key="item.id"
            class="bg-gray-800 rounded-lg p-4"
            @click="viewMaterial(item)"
          >
            <view class="flex space-x-3">
              <image :src="item.image" class="w-16 h-16 rounded-lg object-cover" />
              <view class="flex-1">
                <view class="flex items-start justify-between mb-2">
                  <text class="text-sm font-medium flex-1 mr-2" style="color: #FFFFFF;">{{ item.title }}</text>
                  <view 
                    class="px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs"
                    @click.stop="cancelWant(item)"
                  >
                    <text style="color: #ff6b6b;">取消登记</text>
                  </view>
                </view>
                
                <view class="flex items-center space-x-4 text-xs text-gray-400 mb-2">
                  <view class="flex items-center">
                    <text class="fas fa-clock mr-1" style="font-size: 12rpx; color: #666;"></text>
                    <text>{{ item.distributeTime }}</text>
                  </view>
                  <view class="flex items-center">
                    <text class="fas fa-map-marker-alt mr-1" style="font-size: 12rpx; color: #666;"></text>
                    <text style="color: rgba(255, 255, 255, 0.6);">{{ item.location }}</text>
                  </view>
                </view>

                <view class="flex items-center justify-between">
                  <view class="flex items-center space-x-3">
                    <text class="text-xs" style="color: rgba(255, 255, 255, 0.6);">登记时间：{{ item.wantTime }}</text>
                  </view>
                  <view class="flex items-center">
                    <text class="text-xs mr-2" style="color: #9C75D3;">{{ item.totalWantCount }}人想领</text>
                    <text class="fas fa-chevron-right" style="font-size: 12rpx; color: #666;"></text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 状态管理
const statusBarHeight = ref(0)
const currentTab = ref<'published' | 'wanted'>('published')

// 模拟数据 - 便于后续替换为真实API
const mockPublishedData = [
  {
    id: '1',
    title: '《狮子王》限量版海报',
    image: 'https://picsum.photos/300/300?random=401',
    publishTime: '07-28 14:30',
    location: '上海大剧院门厅',
    wantCount: 26,
    status: 'active'
  },
  {
    id: '2',
    title: '音乐剧纪念品套装',
    image: 'https://picsum.photos/300/300?random=402',
    publishTime: '07-26 19:00',
    location: '剧院商店',
    wantCount: 18,
    status: 'active'
  },
  {
    id: '3',
    title: '《歌剧魅影》面具收藏版',
    image: 'https://picsum.photos/300/300?random=403',
    publishTime: '07-20 20:15',
    location: '文化广场门口',
    wantCount: 45,
    status: 'expired'
  }
]

const mockWantedData = [
  {
    id: '4',
    title: '《芝加哥》官方T恤',
    image: 'https://picsum.photos/300/300?random=404',
    distributeTime: '07-30 19:00',
    location: '天桥艺术中心',
    wantTime: '2小时前',
    totalWantCount: 32
  },
  {
    id: '5',
    title: '音乐剧主题明信片套装',
    image: 'https://picsum.photos/300/300?random=405',
    distributeTime: '08-01 14:00',
    location: '剧院门厅',
    wantTime: '1天前',
    totalWantCount: 15
  }
]

// 数据
const publishedList = ref(mockPublishedData)
const wantedList = ref(mockWantedData)

// 方法
const goBack = () => {
  uni.navigateBack()
}

const switchTab = (tab: 'published' | 'wanted') => {
  currentTab.value = tab
}

const addMaterial = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/add-material'
  })
}

const viewMaterial = (item: any) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/material-detail?id=${item.id}`
  })
}

const editMaterial = (item: any) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/add-material?id=${item.id}&mode=edit`
  })
}

const deleteMaterial = (item: any) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个物料信息吗？',
    success: (res) => {
      if (res.confirm) {
        const index = publishedList.value.findIndex(p => p.id === item.id)
        if (index > -1) {
          publishedList.value.splice(index, 1)
        }
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

const cancelWant = (item: any) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消登记这个物料吗？',
    success: (res) => {
      if (res.confirm) {
        const index = wantedList.value.findIndex(w => w.id === item.id)
        if (index > -1) {
          wantedList.value.splice(index, 1)
        }
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
      }
    }
  })
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-500/20 text-green-400'
    case 'expired':
      return 'bg-gray-500/20 text-gray-400'
    case 'cancelled':
      return 'bg-red-500/20 text-red-400'
    default:
      return 'bg-gray-500/20 text-gray-400'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '进行中'
    case 'expired':
      return '已过期'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

onLoad(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>