<template>
  <view class="min-h-screen bg-deepbg">
    <!-- 自定义导航栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-deepbg" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="flex items-center">
        <text class="fas fa-arrow-left mr-3" style="font-size: 20rpx; color: #fff;" @click="goBack"></text>
        <text class="font-medium ml-3" style="color: white; font-size: 36rpx;">我的日历</text>
      </view>
      <view class="flex items-center space-x-3">
        <text class="fas fa-plus" style="font-size: 20rpx; color: #9C75D3;" @click="addMaterial"></text>
        <text class="fas fa-cog ml-3" style="font-size: 20rpx; color: #fff;" @click="openSettings"></text>
      </view>
    </view>

    <!-- 模式切换 -->
    <view class="px-4 py-3">
      <view class="flex bg-gray-800 rounded-lg p-1">
        <view 
          class="flex-1 text-center py-2 rounded-md transition-all"
          :class="currentMode === 'show' ? 'bg-primary text-white' : 'text-gray-400'"
          @click="switchMode('show')"
        >
          <text style="color: white;">看剧日历</text>
        </view>
        <view 
          class="flex-1 text-center py-2 rounded-md transition-all"
          :class="currentMode === 'material' ? 'bg-primary text-white' : 'text-gray-400'"
          @click="switchMode('material')"
        >
          <text style="color: white;">物料日历</text>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view v-if="currentMode === 'show'" class="mx-4 mb-6 bg-gray-800 rounded-xl p-5 shadow-lg">
      <view class="mb-4">
        <text style="color: white; font-size: 34rpx; font-weight: 600;">观演统计</text>
      </view>
      <view class="flex justify-between gap-4">
        <view class="flex-1 text-center bg-gray-700/50 rounded-lg p-4">
          <text class="block text-2xl font-bold mb-1" style="color: #9C75D3; font-size: 40rpx;">{{ calculatedStats.totalShows || 0 }}</text>
          <text class="block" style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx;">总场次</text>
        </view>
        <view class="flex-1 text-center bg-gray-700/50 rounded-lg p-4">
          <text class="block text-2xl font-bold mb-1" style="color: #9C75D3; font-size: 40rpx;">¥{{ calculatedStats.totalAmount || 0 }}</text>
          <text class="block" style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx;">票房贡献</text>
        </view>
      </view>
    </view>

    <!-- 月份显示和翻页 -->
    <view class="px-4 mb-4">
      <view class="flex items-center justify-between">
        <text class="text-lg font-medium" style="color: white;">{{ currentMonthStr }}</text>
        <view class="flex items-center">
          <view class="p-3 mr-4" @click="previousMonth">
            <text class="fas fa-chevron-left" style="font-size: 24rpx; color: #fff;"></text>
          </view>
          <view class="p-3" @click="nextMonth">
            <text class="fas fa-chevron-right" style="font-size: 24rpx; color: #fff;"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历组件 -->
    <view class="px-4 mb-6">
      <u-calendar
        v-model:show="showCalendar"
        :mode="'multiple'"
        :default-date="selectedDates"
        :custom-list="calendarData"
        :formatter="dateFormatter"
        @confirm="onDateConfirm"
        @close="showCalendar = false"
      />
      
      <!-- 自定义日历显示 -->
      <view class="bg-gray-800 rounded-xl p-5 shadow-lg">
        <view class="grid grid-cols-7 gap-2 mb-4">
          <view v-for="day in weekDays" :key="day" class="text-center py-3">
            <text style="color: rgba(255, 255, 255, 0.5); font-size: 28rpx; font-weight: 500;">{{ day }}</text>
          </view>
        </view>
        
        <view v-for="week in calendarWeeks" :key="week.id" class="grid grid-cols-7 gap-2 mb-3 last:mb-0">
          <view 
            v-for="day in week.days" 
            :key="day.date"
            class="relative aspect-square flex flex-col rounded-xl overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg"
            :class="getDayClass(day)"
            @click="selectDate(day)"
          >
            <!-- 日期显示 -->
            <view class="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
              <text class="font-medium" :style="{ color: day.isCurrentMonth ? '#ffffff' : '#888888', textShadow: '0 0 6px rgba(0,0,0,0.8)', fontSize: '26rpx', fontWeight: '600' }">
                {{ day.day }}
              </text>
            </view>
            
            <!-- 看剧模式：显示剧目海报 -->
            <view v-if="currentMode === 'show' && day.shows?.length" class="w-full h-full">
              <!-- 1个演出：全图 -->
              <image v-if="day.shows.length === 1" :src="day.shows[0].poster" class="w-full h-full object-cover rounded-lg" />
              
              <!-- 2个演出：上下两部分 -->
              <view v-else-if="day.shows.length === 2" class="w-full h-full flex flex-col gap-0.5">
                <image :src="day.shows[0].poster" class="w-full h-1/2 object-cover rounded-t-lg" />
                <image :src="day.shows[1].poster" class="w-full h-1/2 object-cover rounded-b-lg" />
              </view>
              
              <!-- 3个演出：三等分 -->
              <view v-else-if="day.shows.length === 3" class="w-full h-full flex flex-col gap-0.5">
                <image :src="day.shows[0].poster" class="w-full h-1/3 object-cover rounded-t-lg" />
                <image :src="day.shows[1].poster" class="w-full h-1/3 object-cover" />
                <image :src="day.shows[2].poster" class="w-full h-1/3 object-cover rounded-b-lg" />
              </view>
              
              <!-- 4个或更多演出：四等分 -->
              <view v-else-if="day.shows.length >= 4" class="w-full h-full flex flex-col gap-0.5">
                <image :src="day.shows[0].poster" class="w-full h-1/4 object-cover rounded-t-lg" />
                <image :src="day.shows[1].poster" class="w-full h-1/4 object-cover" />
                <image :src="day.shows[2].poster" class="w-full h-1/4 object-cover" />
                <image :src="day.shows[3].poster" class="w-full h-1/4 object-cover rounded-b-lg" />
              </view>
              
              <!-- 多个演出指示器 -->
              <view v-if="day.shows.length > 4" class="absolute bottom-1 right-1 bg-black/70 rounded-full px-2 py-0.5">
                <text class="text-xs" style="color: #ffffff; font-size: 18rpx; font-weight: 600;">+{{ day.shows.length - 4 }}</text>
              </view>
              
              <!-- 观看状态指示 -->
              <view class="absolute top-1 right-1 flex flex-col gap-0.5">
                <view 
                  v-for="show in day.shows.slice(0, 4)" 
                  :key="show.id"
                  class="w-1.5 h-1.5 rounded-full"
                  :class="show.watched ? 'bg-green-400' : 'bg-yellow-400'"
                />
              </view>
            </view>
            
            <!-- 物料模式标记 -->
            <view v-if="currentMode === 'material' && day.materials?.length" class="absolute bottom-1 right-1">
              <view class="w-2 h-2 bg-primary rounded-full" />
            </view>
            
            <!-- 无演出时的背景 -->
            <view v-if="currentMode === 'show' && !day.shows?.length" class="w-full h-full flex items-center justify-center">
              <!-- 无内容时显示日期 -->
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选中日期详情 -->
    <view v-if="selectedDay" class="mx-4 mt-4 bg-gray-800 rounded-lg p-4">
      <view class="flex justify-between items-center mb-4">
        <text style="color: white; font-size: 32rpx; font-weight: 500;">{{ selectedDay.dateStr }}</text>
        <view class="p-2" @click="selectedDay = null">
          <text class="fas fa-times" style="font-size: 28rpx; color: #999;"></text>
        </view>
      </view>
      
      <!-- 看剧模式详情 -->
      <view v-if="currentMode === 'show' && selectedDay.shows?.length">
        <view v-for="show in selectedDay.shows" :key="show.id" class="flex items-start" style="margin-bottom: 24rpx; padding: 16rpx; background: rgba(255,255,255,0.05); border-radius: 12rpx;">
          <image :src="show.poster" style="width: 96rpx; height: 128rpx; border-radius: 8rpx; margin-right: 24rpx; flex-shrink: 0;" mode="aspectFill" />
          <view class="flex-1">
            <text class="block font-medium" style="color: white; font-size: 30rpx; line-height: 1.4; margin-bottom: 8rpx;">{{ show.title }}</text>
            <view style="margin-bottom: 6rpx;">
              <text style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx;">{{ show.theater }}</text>
            </view>
            <view style="margin-bottom: 12rpx;">
              <text style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx;">{{ show.time }}</text>
            </view>
            <view 
              style="padding: 8rpx 16rpx; border-radius: 20rpx; display: inline-block;"
              :style="{ backgroundColor: show.watched ? 'rgba(34, 197, 94, 0.2)' : 'rgba(234, 179, 8, 0.2)' }"
            >
              <text :style="{ fontSize: '24rpx', color: show.watched ? '#22c55e' : '#eab308' }">{{ show.watched ? '已观看' : '未观看' }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 物料模式详情 -->
      <view v-if="currentMode === 'material' && selectedDay.materials?.length">
        <view v-for="material in selectedDay.materials" :key="material.id" class="flex items-start" style="margin-bottom: 24rpx; padding: 16rpx; background: rgba(255,255,255,0.05); border-radius: 12rpx;" @click="viewMaterial(material)">
          <image :src="material.image" style="width: 96rpx; height: 96rpx; border-radius: 8rpx; margin-right: 24rpx; flex-shrink: 0;" mode="aspectFill" />
          <view class="flex-1">
            <text class="block font-medium" style="color: white; font-size: 30rpx; line-height: 1.4; margin-bottom: 8rpx;">{{ material.title }}</text>
            <view style="margin-bottom: 12rpx;">
              <text style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx;">{{ material.location }}</text>
            </view>
            <view class="flex items-center justify-between">
              <text style="font-size: 24rpx; color: #9C75D3;">{{ material.wantCount }}人想领</text>
              <text class="fas fa-chevron-right" style="font-size: 24rpx; color: #666;"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态提示 -->
      <view v-if="currentMode === 'show' && (!selectedDay.shows || selectedDay.shows.length === 0)" class="text-center py-8">
        <text style="color: rgba(255, 255, 255, 0.4); font-size: 28rpx;">该日期暂无演出</text>
      </view>
      
      <view v-if="currentMode === 'material' && (!selectedDay.materials || selectedDay.materials.length === 0)" class="text-center py-8">
        <text style="color: rgba(255, 255, 255, 0.4); font-size: 28rpx;">该日期暂无物料</text>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view v-if="currentMode === 'show'" class="fixed bottom-20 right-4">
      <view class="w-12 h-12 bg-primary rounded-full flex items-center justify-center" @click="shareCalendar">
        <text class="fas fa-share" style="font-size: 20rpx; color: #fff;"></text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

interface Show {
  id: string
  watched: boolean
  title: string
  theater: string
  time: string
  poster: string
}

interface Material {
  id: string
  title: string
  location: string
  wantCount: number
  image: string
}

interface DayData {
  date: string
  day: number
  isCurrentMonth: boolean
  dateStr: string
  shows: Show[]
  materials: Material[]
}

// 状态管理
const statusBarHeight = ref(0)
const currentMode = ref<'show' | 'material'>('show')
const showCalendar = ref(false)
const selectedDates = ref([])
const selectedDay = ref<DayData | null>(null)
const currentDate = ref(new Date())

// 日历数据
const calendarData = ref([])

const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 计算属性
const calendarWeeks = computed(() => {
  // 生成日历数据的逻辑
  return generateCalendarData()
})

const currentMonthStr = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  return `${year}年${month}月`
})

// 计算统计数据
const calculatedStats = computed(() => {
  const weeks = calendarWeeks.value
  let totalShows = 0
  let totalAmount = 0
  
  weeks.forEach(week => {
    week.days.forEach(day => {
      if (day.isCurrentMonth && day.shows && day.shows.length > 0) {
        totalShows += day.shows.length
        // 计算票房：每场演出基础票价350元，不管是否已观看都计算
        day.shows.forEach(show => {
          totalAmount += 350
        })
      }
    })
  })
  
  return {
    totalShows,
    totalAmount
  }
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const switchMode = (mode: 'show' | 'material') => {
  currentMode.value = mode
}

const addMaterial = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/add-material'
  })
}

const viewMaterial = (material: Material) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/material-detail?id=${material.id}`
  })
}

const shareCalendar = () => {
  // 分享功能实现
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

const generateCalendarData = () => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  const weeks = []
  let currentWeek = []
  
  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    
    const dayData: DayData = {
      date: date.toISOString().split('T')[0],
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === month,
      dateStr: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
      shows: getMockShowsForDate(date),
      materials: getMockMaterialsForDate(date)
    }
    
    currentWeek.push(dayData)
    
    if (currentWeek.length === 7) {
      weeks.push({ id: weeks.length, days: currentWeek })
      currentWeek = []
    }
  }
  
  return weeks
}

// 生成模拟看剧数据 - 便于后续替换为真实API
const getMockShowsForDate = (date: Date): Show[] => {
  const dayOfMonth = date.getDate()
  const shows: Show[] = []
  
  // 根据日期模拟不同的演出数据
  if (dayOfMonth % 5 === 0) {
    shows.push({
      id: `show_${dayOfMonth}_1`,
      watched: Math.random() > 0.5,
      title: '狮子王',
      theater: '上海大剧院',
      time: '19:30',
      poster: 'https://picsum.photos/300/400?random=' + dayOfMonth
    })
  }
  
  if (dayOfMonth % 7 === 0) {
    shows.push({
      id: `show_${dayOfMonth}_2`,
      watched: Math.random() > 0.3,
      title: '歌剧魅影',
      theater: '上海文化广场',
      time: '20:00',
      poster: 'https://picsum.photos/300/400?random=' + (dayOfMonth + 100)
    })
  }
  
  if (dayOfMonth % 3 === 0 && dayOfMonth % 6 !== 0) {
    shows.push({
      id: `show_${dayOfMonth}_3`,
      watched: Math.random() > 0.7,
      title: '芝加哥',
      theater: '北京天桥艺术中心',
      time: '14:00',
      poster: 'https://picsum.photos/300/400?random=' + (dayOfMonth + 200)
    })
  }
  
  return shows
}

// 生成模拟物料数据 - 便于后续替换为真实API
const getMockMaterialsForDate = (date: Date): Material[] => {
  const dayOfMonth = date.getDate()
  const materials: Material[] = []
  
  // 根据日期模拟不同的物料数据
  if (dayOfMonth % 4 === 0) {
    materials.push({
      id: `material_${dayOfMonth}_1`,
      title: '《狮子王》限量版海报',
      location: '上海大剧院门厅',
      wantCount: Math.floor(Math.random() * 100) + 20,
      image: 'https://picsum.photos/200/200?random=' + (dayOfMonth + 300)
    })
  }
  
  if (dayOfMonth % 6 === 0) {
    materials.push({
      id: `material_${dayOfMonth}_2`,
      title: '音乐剧纪念品套装',
      location: '剧院商店',
      wantCount: Math.floor(Math.random() * 50) + 10,
      image: 'https://picsum.photos/200/200?random=' + (dayOfMonth + 400)
    })
  }
  
  return materials
}

const dateFormatter = (day: any) => {
  return day
}

const onDateConfirm = (dates: any) => {
  selectedDates.value = dates
  showCalendar.value = false
}

const selectDate = (day: DayData) => {
  selectedDay.value = day
}

const getDayClass = (day: DayData) => {
  const classes = ['bg-gray-700 hover:bg-gray-600']
  
  if (day.isCurrentMonth) {
    classes.push('bg-opacity-100')
  } else {
    classes.push('bg-opacity-50')
  }
  
  const today = new Date().toISOString().split('T')[0]
  if (day.date === today) {
    classes.push('bg-primary')
  }
  
  return classes.join(' ')
}

const openSettings = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/material-manage'
  })
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

onLoad(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>