<template>
  <view class="min-h-screen bg-deepbg">
    <!-- 自定义导航栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-deepbg" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="flex items-center">
        <text class="fas fa-arrow-left mr-3" style="font-size: 20rpx; color: #fff" @click="goBack"></text>
        <text class="font-medium ml-3" style="color: white; font-size: 36rpx">我的日历</text>
      </view>
      <view class="flex items-center space-x-3">
        <!-- 模式切换移至右上角 -->
        <view class="mode-switch-compact">
          <view class="mode-option" :class="{ active: currentMode === 'show' }" @click="switchMode('show')">
            <text class="mode-text">看剧</text>
          </view>
          <view class="mode-divider"></view>
          <view class="mode-option" :class="{ active: currentMode === 'material' }" @click="switchMode('material')">
            <text class="mode-text">物料</text>
          </view>
        </view>
        <text class="fas fa-plus" style="font-size: 20rpx; color: #9c75d3" @click="addMaterial"></text>
        <text class="fas fa-cog ml-3" style="font-size: 20rpx; color: #fff" @click="openSettings"></text>
      </view>
    </view>

    <!-- 月份显示、统计信息和翻页 -->
    <view class="px-4 mb-4">
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <text class="text-lg font-medium mr-3" style="color: white">{{ getViewTitle() }}</text>
          <!-- 视图切换按钮 -->
          <view class="view-switch">
            <view class="view-option" :class="{ active: calendarView === 'month' }" @click="switchView('month')">
              <text class="view-text">月</text>
            </view>
            <view class="view-option" :class="{ active: calendarView === 'year' }" @click="switchView('year')">
              <text class="view-text">年</text>
            </view>
            <view class="view-option" :class="{ active: calendarView === 'decade' }" @click="switchView('decade')">
              <text class="view-text">10年</text>
            </view>
          </view>
        </view>
        <view class="flex items-center">
          <!-- 统计信息右对齐显示 -->
          <view v-if="currentMode === 'show'" class="stats-compact mr-4">
            <view class="stats-item">
              <text class="stats-number">{{ calculatedStats.totalShows || 0 }}</text>
              <text class="stats-label">场次</text>
            </view>
            <view class="stats-divider" v-if="showAmount"></view>
            <view class="stats-item" v-if="showAmount">
              <text class="stats-number">¥{{ calculatedStats.totalAmount || 0 }}</text>
              <text class="stats-label">票房</text>
            </view>
            <!-- 金额显示控制按钮 -->
            <view class="amount-toggle ml-2" @click="toggleAmountDisplay">
              <text class="fas" :class="showAmount ? 'fa-eye' : 'fa-eye-slash'" style="font-size: 18rpx; color: rgba(255, 255, 255, 0.6)"></text>
            </view>
          </view>
          <!-- 翻页按钮 -->
          <view class="p-3 mr-4" @click="previousMonth">
            <text class="fas fa-chevron-left" style="font-size: 24rpx; color: #fff"></text>
          </view>
          <view class="p-3" @click="nextMonth">
            <text class="fas fa-chevron-right" style="font-size: 24rpx; color: #fff"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历组件 -->
    <view class="px-4 mb-6">
      <u-calendar
        v-model:show="showCalendar"
        :mode="'multiple'"
        :default-date="selectedDates"
        :custom-list="calendarData"
        :formatter="dateFormatter"
        @confirm="onDateConfirm"
        @close="showCalendar = false" />

      <!-- 自定义日历显示 -->
      <view class="bg-gray-800 rounded-xl p-5 shadow-lg">
        <!-- 月视图 -->
        <view v-if="calendarView === 'month'">
        <view class="grid grid-cols-7 gap-2 mb-4">
          <view v-for="day in weekDays" :key="day" class="text-center py-3">
            <text style="color: rgba(255, 255, 255, 0.5); font-size: 28rpx; font-weight: 500">{{ day }}</text>
          </view>
        </view>

        <view v-for="week in calendarWeeks" :key="week.id" class="calendar-week">
          <view v-for="day in week.days" :key="day.date" class="calendar-day" :class="getDayClass(day)" @click="selectDate(day)">
            <!-- 日期显示 -->
            <view class="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
              <text class="font-medium" :style="{ color: day.isCurrentMonth ? '#ffffff' : '#888888', textShadow: '0 0 6px rgba(0,0,0,0.8)', fontSize: '26rpx', fontWeight: '600' }">
                {{ day.day }}
              </text>
            </view>

            <!-- 看剧模式：显示剧目海报 -->
            <view v-if="currentMode === 'show' && day.shows?.length" class="w-full h-full">
              <!-- 1个演出：全图 -->
              <image v-if="day.shows.length === 1" :src="day.shows[0].poster" class="w-full h-full object-cover rounded-lg" />

              <!-- 2个演出：上下两部分 -->
              <view v-else-if="day.shows.length === 2" class="w-full h-full flex flex-col gap-0.5">
                <image :src="day.shows[0].poster" class="w-full h-1/2 object-cover rounded-t-lg" />
                <image :src="day.shows[1].poster" class="w-full h-1/2 object-cover rounded-b-lg" />
              </view>

              <!-- 3个演出：三等分 -->
              <view v-else-if="day.shows.length === 3" class="w-full h-full flex flex-col gap-0.5">
                <image :src="day.shows[0].poster" class="w-full h-1/3 object-cover rounded-t-lg" />
                <image :src="day.shows[1].poster" class="w-full h-1/3 object-cover" />
                <image :src="day.shows[2].poster" class="w-full h-1/3 object-cover rounded-b-lg" />
              </view>

              <!-- 4个或更多演出：四等分 -->
              <view v-else-if="day.shows.length >= 4" class="w-full h-full flex flex-col gap-0.5">
                <image :src="day.shows[0].poster" class="w-full h-1/4 object-cover rounded-t-lg" />
                <image :src="day.shows[1].poster" class="w-full h-1/4 object-cover" />
                <image :src="day.shows[2].poster" class="w-full h-1/4 object-cover" />
                <image :src="day.shows[3].poster" class="w-full h-1/4 object-cover rounded-b-lg" />
              </view>

              <!-- 多个演出指示器 -->
              <view v-if="day.shows.length > 4" class="absolute bottom-1 right-1 bg-black/70 rounded-full px-2 py-0.5">
                <text class="text-xs" style="color: #ffffff; font-size: 18rpx; font-weight: 600">+{{ day.shows.length - 4 }}</text>
              </view>

              <!-- 观看状态指示 -->
              <view class="absolute top-1 right-1 flex flex-col gap-0.5">
                <view v-for="show in day.shows.slice(0, 4)" :key="show.id" class="w-1.5 h-1.5 rounded-full" :class="show.watched ? 'bg-green-400' : 'bg-yellow-400'" />
              </view>
            </view>

            <!-- 物料模式标记 -->
            <view v-if="currentMode === 'material' && day.materials?.length" class="absolute bottom-1 right-1">
              <view class="w-2 h-2 bg-primary rounded-full" />
            </view>

            <!-- 无演出时的背景 -->
            <view v-if="currentMode === 'show' && !day.shows?.length" class="w-full h-full flex items-center justify-center">
              <!-- 无内容时显示日期 -->
            </view>
          </view>
        </view>

        <!-- 年视图 -->
        <view v-if="calendarView === 'year'" class="year-view">
          <view class="grid grid-cols-4 gap-3">
            <view
              v-for="month in 12"
              :key="month"
              class="month-item"
              @click="selectMonth(month)"
            >
              <text class="month-text">{{ month }}月</text>
              <view class="month-stats" v-if="currentMode === 'show'">
                <text class="month-shows">{{ getMonthStats(month).shows }}场</text>
                <text class="month-amount" v-if="showAmount">¥{{ getMonthStats(month).amount }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 十年视图 -->
        <view v-if="calendarView === 'decade'" class="decade-view">
          <view class="grid grid-cols-5 gap-3">
            <view
              v-for="year in getDecadeYears()"
              :key="year"
              class="year-item"
              @click="selectYear(year)"
            >
              <text class="year-text">{{ year }}</text>
              <view class="year-stats" v-if="currentMode === 'show'">
                <text class="year-shows">{{ getYearStats(year).shows }}场</text>
                <text class="year-amount" v-if="showAmount">¥{{ getYearStats(year).amount }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选中日期详情 -->
    <view v-if="selectedDay" class="mx-4 mt-4 bg-gray-800 rounded-lg p-4">
      <view class="flex justify-between items-center mb-4">
        <text style="color: white; font-size: 32rpx; font-weight: 500">{{ selectedDay.dateStr }}</text>
        <view class="p-2" @click="selectedDay = null">
          <text class="fas fa-times" style="font-size: 28rpx; color: #999"></text>
        </view>
      </view>

      <!-- 看剧模式详情 -->
      <view v-if="currentMode === 'show' && selectedDay.shows?.length">
        <view v-for="show in selectedDay.shows" :key="show.id" class="flex items-start" style="margin-bottom: 24rpx; padding: 16rpx; background: rgba(255, 255, 255, 0.05); border-radius: 12rpx">
          <image :src="show.poster" style="width: 96rpx; height: 128rpx; border-radius: 8rpx; margin-right: 24rpx; flex-shrink: 0" mode="aspectFill" />
          <view class="flex-1">
            <text class="block font-medium" style="color: white; font-size: 30rpx; line-height: 1.4; margin-bottom: 8rpx">{{ show.title }}</text>
            <view style="margin-bottom: 6rpx">
              <text style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx">{{ show.theater }}</text>
            </view>
            <view style="margin-bottom: 12rpx">
              <text style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx">{{ show.time }}</text>
            </view>
            <view style="padding: 8rpx 16rpx; border-radius: 20rpx; display: inline-block" :style="{ backgroundColor: show.watched ? 'rgba(34, 197, 94, 0.2)' : 'rgba(234, 179, 8, 0.2)' }">
              <text :style="{ fontSize: '24rpx', color: show.watched ? '#22c55e' : '#eab308' }">{{ show.watched ? '已观看' : '未观看' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 物料模式详情 -->
      <view v-if="currentMode === 'material' && selectedDay.materials?.length">
        <view
          v-for="material in selectedDay.materials"
          :key="material.id"
          class="flex items-start"
          style="margin-bottom: 24rpx; padding: 16rpx; background: rgba(255, 255, 255, 0.05); border-radius: 12rpx"
          @click="viewMaterial(material)">
          <image :src="material.image" style="width: 96rpx; height: 96rpx; border-radius: 8rpx; margin-right: 24rpx; flex-shrink: 0" mode="aspectFill" />
          <view class="flex-1">
            <text class="block font-medium" style="color: white; font-size: 30rpx; line-height: 1.4; margin-bottom: 8rpx">{{ material.title }}</text>
            <view style="margin-bottom: 12rpx">
              <text style="color: rgba(255, 255, 255, 0.6); font-size: 26rpx">{{ material.location }}</text>
            </view>
            <view class="flex items-center justify-between">
              <text style="font-size: 24rpx; color: #9c75d3">{{ material.wantCount }}人想领</text>
              <text class="fas fa-chevron-right" style="font-size: 24rpx; color: #666"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态提示 -->
      <view v-if="currentMode === 'show' && (!selectedDay.shows || selectedDay.shows.length === 0)" class="text-center py-8">
        <text style="color: rgba(255, 255, 255, 0.4); font-size: 28rpx">该日期暂无演出</text>
      </view>

      <view v-if="currentMode === 'material' && (!selectedDay.materials || selectedDay.materials.length === 0)" class="text-center py-8">
        <text style="color: rgba(255, 255, 255, 0.4); font-size: 28rpx">该日期暂无物料</text>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view v-if="currentMode === 'show'" class="fixed bottom-20 right-4">
      <view class="w-12 h-12 bg-primary rounded-full flex items-center justify-center" @click="shareCalendar">
        <text class="fas fa-share" style="font-size: 20rpx; color: #fff"></text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'

interface Show {
  id: string
  watched: boolean
  title: string
  theater: string
  time: string
  poster: string
}

interface Material {
  id: string
  title: string
  location: string
  wantCount: number
  image: string
}

interface DayData {
  date: string
  day: number
  isCurrentMonth: boolean
  dateStr: string
  shows: Show[]
  materials: Material[]
}

// 状态管理
const statusBarHeight = ref(0)
const currentMode = ref<'show' | 'material'>('show')
const showCalendar = ref(false)
const selectedDates = ref([])
const selectedDay = ref<DayData | null>(null)
const currentDate = ref(new Date())
const showAmount = ref(true) // 控制是否显示金额
const calendarView = ref<'month' | 'year' | 'decade'>('month') // 日历视图模式

// 日历数据
const calendarData = ref([])

const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 计算属性
const calendarWeeks = computed(() => {
  // 生成日历数据的逻辑
  return generateCalendarData()
})



// 计算统计数据
const calculatedStats = computed(() => {
  const weeks = calendarWeeks.value
  let totalShows = 0
  let totalAmount = 0

  weeks.forEach(week => {
    week.days.forEach(day => {
      if (day.isCurrentMonth && day.shows && day.shows.length > 0) {
        totalShows += day.shows.length
        // 计算票房：每场演出基础票价350元，不管是否已观看都计算
        day.shows.forEach(() => {
          totalAmount += 350
        })
      }
    })
  })

  return {
    totalShows,
    totalAmount
  }
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const switchMode = (mode: 'show' | 'material') => {
  currentMode.value = mode
}

const addMaterial = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/add-material'
  })
}

const viewMaterial = (material: Material) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/material-detail?id=${material.id}`
  })
}

const shareCalendar = () => {
  // 分享功能实现
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

const generateCalendarData = () => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  const weeks = []
  let currentWeek = []

  // 计算需要显示的周数，确保包含完整的当月
  const totalDays = Math.ceil((lastDay.getDate() + firstDay.getDay()) / 7) * 7

  for (let i = 0; i < totalDays; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dayData: DayData = {
      date: date.toISOString().split('T')[0],
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === month,
      dateStr: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`,
      shows: getMockShowsForDate(date),
      materials: getMockMaterialsForDate(date)
    }

    currentWeek.push(dayData)

    if (currentWeek.length === 7) {
      weeks.push({ id: weeks.length, days: currentWeek })
      currentWeek = []
    }
  }

  return weeks
}

// 生成模拟看剧数据 - 便于后续替换为真实API
const getMockShowsForDate = (date: Date): Show[] => {
  const dayOfMonth = date.getDate()
  const shows: Show[] = []

  // 根据日期模拟不同的演出数据
  if (dayOfMonth % 5 === 0) {
    shows.push({
      id: `show_${dayOfMonth}_1`,
      watched: Math.random() > 0.5,
      title: '狮子王',
      theater: '上海大剧院',
      time: '19:30',
      poster: 'https://picsum.photos/300/400?random=' + dayOfMonth
    })
  }

  if (dayOfMonth % 7 === 0) {
    shows.push({
      id: `show_${dayOfMonth}_2`,
      watched: Math.random() > 0.3,
      title: '歌剧魅影',
      theater: '上海文化广场',
      time: '20:00',
      poster: 'https://picsum.photos/300/400?random=' + (dayOfMonth + 100)
    })
  }

  if (dayOfMonth % 3 === 0 && dayOfMonth % 6 !== 0) {
    shows.push({
      id: `show_${dayOfMonth}_3`,
      watched: Math.random() > 0.7,
      title: '芝加哥',
      theater: '北京天桥艺术中心',
      time: '14:00',
      poster: 'https://picsum.photos/300/400?random=' + (dayOfMonth + 200)
    })
  }

  return shows
}

// 生成模拟物料数据 - 便于后续替换为真实API
const getMockMaterialsForDate = (date: Date): Material[] => {
  const dayOfMonth = date.getDate()
  const materials: Material[] = []

  // 根据日期模拟不同的物料数据
  if (dayOfMonth % 4 === 0) {
    materials.push({
      id: `material_${dayOfMonth}_1`,
      title: '《狮子王》限量版海报',
      location: '上海大剧院门厅',
      wantCount: Math.floor(Math.random() * 100) + 20,
      image: 'https://picsum.photos/200/200?random=' + (dayOfMonth + 300)
    })
  }

  if (dayOfMonth % 6 === 0) {
    materials.push({
      id: `material_${dayOfMonth}_2`,
      title: '音乐剧纪念品套装',
      location: '剧院商店',
      wantCount: Math.floor(Math.random() * 50) + 10,
      image: 'https://picsum.photos/200/200?random=' + (dayOfMonth + 400)
    })
  }

  return materials
}

const dateFormatter = (day: any) => {
  return day
}

const onDateConfirm = (dates: any) => {
  selectedDates.value = dates
  showCalendar.value = false
}

const selectDate = (day: DayData) => {
  selectedDay.value = day
}

const getDayClass = (day: DayData) => {
  const classes = []

  if (day.isCurrentMonth) {
    classes.push('current-month')
  } else {
    classes.push('other-month')
  }

  const today = new Date().toISOString().split('T')[0]
  if (day.date === today) {
    classes.push('today')
  }

  return classes.join(' ')
}

const openSettings = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/material-manage'
  })
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

const toggleAmountDisplay = () => {
  showAmount.value = !showAmount.value
}

const switchView = (view: 'month' | 'year' | 'decade') => {
  calendarView.value = view
}

const getViewTitle = () => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1

  switch (calendarView.value) {
    case 'month':
      return `${year}年${month}月`
    case 'year':
      return `${year}年`
    case 'decade':
      const decadeStart = Math.floor(year / 10) * 10
      return `${decadeStart}-${decadeStart + 9}`
    default:
      return `${year}年${month}月`
  }
}

const selectMonth = (month: number) => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(month - 1)
  currentDate.value = newDate
  calendarView.value = 'month'
}

const selectYear = (year: number) => {
  const newDate = new Date(currentDate.value)
  newDate.setFullYear(year)
  currentDate.value = newDate
  calendarView.value = 'year'
}

const getDecadeYears = () => {
  const currentYear = currentDate.value.getFullYear()
  const decadeStart = Math.floor(currentYear / 10) * 10
  const years = []
  for (let i = 0; i < 10; i++) {
    years.push(decadeStart + i)
  }
  return years
}

const getMonthStats = (month: number) => {
  // 模拟月份统计数据，实际应该根据month参数查询真实数据
  console.log('Getting stats for month:', month)
  return {
    shows: Math.floor(Math.random() * 10) + 1,
    amount: (Math.floor(Math.random() * 5000) + 1000)
  }
}

const getYearStats = (year: number) => {
  // 模拟年份统计数据，实际应该根据year参数查询真实数据
  console.log('Getting stats for year:', year)
  return {
    shows: Math.floor(Math.random() * 50) + 10,
    amount: (Math.floor(Math.random() * 50000) + 10000)
  }
}

onLoad(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>

<style lang="scss" scoped>
/* 紧凑模式切换样式 */
.mode-switch-compact {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 4rpx;
  backdrop-filter: blur(10rpx);
}

.mode-option {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background: linear-gradient(135deg, #9c75d3 0%, #7b5cb0 100%);
    box-shadow: 0 2rpx 8rpx rgba(156, 117, 211, 0.3);
  }
}

.mode-text {
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;

  .mode-option.active & {
    color: #ffffff;
    font-weight: 600;
  }
}

.mode-divider {
  width: 1rpx;
  height: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 4rpx;
}

/* 视图切换样式 */
.view-switch {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12rpx;
  padding: 2rpx;
}

.view-option {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background: rgba(156, 117, 211, 0.8);
  }
}

.view-text {
  font-size: 20rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;

  .view-option.active & {
    color: #ffffff;
    font-weight: 600;
  }
}

/* 紧凑统计信息样式 */
.stats-compact {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12rpx;
  padding: 6rpx 12rpx;
  backdrop-filter: blur(10rpx);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60rpx;
}

.stats-number {
  font-size: 24rpx;
  font-weight: 600;
  color: #9c75d3;
  line-height: 1.2;
}

.stats-label {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 2rpx;
}

.stats-divider {
  width: 1rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 8rpx;
}

.amount-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:active {
    opacity: 0.7;
  }
}

/* 优化日历布局样式 */
.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1rpx; /* 使用细线间距 */
  margin-bottom: 1rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.calendar-day {
  position: relative;
  height: 120rpx; /* 增加高度 */
  background: rgba(255, 255, 255, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.1); /* 细线分隔 */
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  /* 当前月份的日期 */
  &.current-month {
    background: rgba(255, 255, 255, 0.08);
  }

  /* 今天的样式 */
  &.today {
    background: linear-gradient(135deg, #9c75d3 0%, #7b5cb0 100%);
    border-color: #9c75d3;
  }

  /* 非当前月份的日期 */
  &.other-month {
    background: rgba(255, 255, 255, 0.02);
    opacity: 0.4;
  }
}

/* 年视图样式 */
.year-view {
  .month-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12rpx;
    padding: 16rpx;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1rpx solid rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      transform: translateY(-2rpx);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .month-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
    display: block;
    margin-bottom: 8rpx;
  }

  .month-stats {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
  }

  .month-shows {
    font-size: 20rpx;
    color: #9C75D3;
    font-weight: 500;
  }

  .month-amount {
    font-size: 18rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}

/* 十年视图样式 */
.decade-view {
  .year-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12rpx;
    padding: 16rpx;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1rpx solid rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      transform: translateY(-2rpx);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .year-text {
    font-size: 26rpx;
    font-weight: 600;
    color: #ffffff;
    display: block;
    margin-bottom: 8rpx;
  }

  .year-stats {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
  }

  .year-shows {
    font-size: 18rpx;
    color: #9C75D3;
    font-weight: 500;
  }

  .year-amount {
    font-size: 16rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
