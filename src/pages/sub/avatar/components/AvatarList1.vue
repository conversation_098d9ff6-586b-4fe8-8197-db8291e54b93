<template>
  <mescroll-uni
    class="block h-0 w-screen grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="flex w-full flex-wrap items-start justify-start pl-2.5 pr-2.5 pt-[50rpx]">
      <view
        class="avatarBox mb-[78rpx] mr-[40rpx] flex h-[110rpx] w-[110rpx] items-center justify-center bg-fullAuto bg-no-repeat"
        :key="i.id"
        @tap="$push({ name: 'AvatarDetail', params: { id: i.id, isUpdate: i.upgradeStatus } })"
        v-for="i in list">
        <u-image :src="$picFormat(i.image)" height="88rpx" width="88rpx"></u-image>
      </view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $userDigitalAvatar } from '@/api/avatar'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $picFormat, $push, $topclick } from '@/utils/methods'

  const props = defineProps({
    keyword: { type: String, default: '' },
    orderBy: { type: String, default: '' },
    isAsc: { type: String, default: '' },
  })

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt4 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const upOpt = ref({ ...upOpt4.value, noMoreSize: 99999 })
  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 数字头像列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userDigitalAvatar({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      badgeType: 2,
      userId: userInfo.value.id,
      keyword: props.keyword || undefined,
      orderByColumn: props.orderBy ? props.orderBy + ' ' + props.isAsc + ',id' : undefined,
      // isAsc: props.isAsc,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleRefresh = () => {
    mescrollObj.value.resetUpScroll()
  }

  defineExpose({ handleRefresh })
</script>

<style lang="scss" scoped>
  .avatarBox {
    background-image: url($icon + 'background/avatarWall.webp');

    &:nth-child(5n) {
      margin-right: 0;
    }
  }
</style>
