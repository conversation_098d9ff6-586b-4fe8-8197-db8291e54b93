<template>
  <mescroll-uni
    class="block h-0 w-screen grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="m-auto flex w-[702rpx] flex-wrap items-start justify-start pt-[32rpx]">
      <view
        class="avatarItem relative mb-2.5 mr-[14rpx] min-h-[344rpx] w-[344rpx] overflow-hidden rounded-[20rpx] bg-w-20"
        :key="i.id"
        @tap="$push({ name: 'AvatarDetail', params: { id: i.id, isUpdate: i.upgradeStatus } })"
        v-for="i in list">
        <u-image class="bg-[#4D4C56]" :src="$picFormat(i.image)" height="344rpx" width="344rpx"></u-image>
        <!-- <view class="relative z-10 w-full pb-[22rpx] pl-[22rpx] pr-[22rpx] pt-2.5">
          <view class="mb-[4rpx] line-clamp-1 w-full font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">《{{ i.repertoireName || '-' }}》</view>
          <view class="mb-2.5 line-clamp-1 w-full break-all font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">商品编号：{{ i.sendId }}</view>
          <view class="flex w-full items-center justify-start">
            <view class="ml-[10rpx] line-clamp-1 font-Regular font-normal leading-[32rpx] text-[24prx] text-w-100">{{ i.issuerName || '-' }}</view>
          </view>
        </view>

        <image class="absolute bottom-[10rpx] right-0 h-[112rpx] w-[114rpx]" :src="$iconFormat('status/get.webp')" mode="scaleToFill" webp /> -->
      </view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $userDigitalAvatar } from '@/api/avatar'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $picFormat, $push, $topclick } from '@/utils/methods'

  const props = defineProps({
    keyword: { type: String, default: '' },
    orderBy: { type: String, default: '' },
    isAsc: { type: String, default: '' },
  })

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const upOpt = ref({ ...upOpt3.value, noMoreSize: 99999 })
  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 数字头像列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userDigitalAvatar({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      badgeType: 2,
      userId: userInfo.value.id,
      keyword: props.keyword || undefined,
      orderByColumn: props.orderBy ? props.orderBy + ' ' + props.isAsc + ',id' : undefined,
      // isAsc: props.isAsc,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleRefresh = () => {
    mescrollObj.value.resetUpScroll()
  }

  defineExpose({ handleRefresh })
</script>

<style lang="scss" scoped>
  .avatarItem {
    &:nth-child(2n) {
      margin-right: 0;
    }
  }
</style>
