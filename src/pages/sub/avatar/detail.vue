<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder />

    <view
      class="avatarBg m-auto mb-[112rpx] mt-[66rpx] flex h-[560rpx] w-[560rpx] items-center justify-center bg-fullAuto bg-no-repeat">
      <u-image
        :src="isUpdate ? detail.upgradeImageUrl : detail.imageUrl"
        height="480rpx"
        mode="aspectFill"
        v-if="detail.image"
        width="480rpx"></u-image>
    </view>

    <!-- 数字头像信息 -->
    <view class="relative m-auto mb-[60rpx] h-fit w-[706rpx]">
      <image class="block w-full" :src="$iconFormat('background/infoDailogT.png')" mode="widthFix" />

      <view
        class="relative z-10 mb-[-2rpx] mt-[-4rpx] h-fit w-full border-l-[4rpx] border-r-[4rpx] border-[#BC93FF] bg-[#100636] pb-[2rpx] pl-[38rpx] pr-[38rpx] pt-[2rpx]">
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >剧目名称</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            detail.repertoireName || '-'
          }}</text>
        </view>
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >发放时间</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            detail.startTime || '-'
          }}</text>
        </view>
        <view class="mb-[20rpx] flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >发放数量</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100" v-if="isUpdate">{{
            detail.digitalAvatarIssuedQuantity || '-'
          }}</text>
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100" v-else>无数量限制</text>
        </view>
        <!-- <view class="mb-[20rpx] flex items-start justify-start" v-if="isUpdate">
        <text class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">专属编号</text>
        <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{ detail.portfolioNo || '-' }}</text>
      </view> -->
        <view class="flex items-start justify-start">
          <text
            class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
            >获得时间</text
          >
          <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
            isUpdate ? detail.upgradeTime : detail.createTime || '-'
          }}</text>
        </view>
      </view>

      <image class="block w-full" :src="$iconFormat('background/infoDailogB.png')" mode="widthFix" />
    </view>

    <view class="flex items-center justify-center">
      <customBtn
        class="mr-3.5"
        type="3"
        @tap="$push({ name: 'AvatarSave', params: { id, isUpdate: isUpdate ? 1 : 0 } })"
        text="分享一下"
        width="260" />
      <customBtn
        :text="detail.adorn > 0 ? '取消展示' : '画廊展示'"
        :type="detail.adorn > 0 ? 4 : 1"
        @tap="handleShow"
        width="260" />
    </view>
  </view>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $detailsByDigitalAvatar, $updateAdornStatus } from '@/api/avatar'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { $back, $iconFormat, $picFormat, $push } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy

  const id = ref() // 数字头像id
  const isUpdate = ref(false) // 是否是升级头像
  const detail = ref<any>('')

  onLoad((params: any) => {
    id.value = params.id
    isUpdate.value = params.isUpdate === '0' ? false : true

    handleGetDetail()
  })

  /* 获取数字头像详情 */
  const handleGetDetail = () => {
    $detailsByDigitalAvatar(id.value, isUpdate.value ? 1 : 0).then((res: any) => {
      if (res.data.image) res.data.imageUrl = $picFormat(res.data.image)
      if (res.data.upgradeImage) res.data.upgradeImageUrl = $picFormat(res.data.upgradeImage)
      if (res.data.startTime) res.data.startTime = dayjs(res.data.startTime).format('YYYY/MM/DD')
      if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY/MM/DD HH:mm')
      if (res.data.upgradeTime) res.data.upgradeTime = dayjs(res.data.upgradeTime).format('YYYY/MM/DD HH:mm')

      detail.value = res.data
    })
  }

  /* 画廊展示 */
  const handleShow = () => {
    let data: any

    if (detail.value.adorn > 0) {
      data = {
        id: detail.value.adorn,
      }
    } else {
      data = {
        userReceivingRecordsId: Number(id.value),
        badgeType: 2,
        upgradeStatus: isUpdate.value ? 1 : 0,
        adornImage: isUpdate.value ? detail.value.upgradeImage : detail.value.image,
      }
    }

    $updateAdornStatus(data).then((res: any) => {
      handleGetDetail()
      uni.$emit('updateAvatarList')
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .avatarBg {
      background-image: url($icon + 'background/avatarWall1.webp');
    }

    .infoBox1 {
      background-image: url($icon + 'background/infoDailog2.webp');
    }

    .infoBox2 {
      background-image: url($icon + 'background/infoDailog3.webp');
    }
  }
</style>
