<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat pb-[100rpx]"
    :class="{ bgPic1: listType === 1, bgPic2: listType === 2 }">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="数字头像画廊"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      titleWidth="574rpx" />
    <!-- 头像墙 -->
    <view class="relative h-[91.7104vw] w-full shrink-0">
      <!-- 1 -->
      <view class="absolute left-[13.07vw] top-[25.327vw] h-[120rpx] w-[120rpx]">
        <u-image
          :src="$picFormat(list[0].adornImage)"
          @tap="
            $push({
              name: 'AvatarDetail',
              params: { id: list[0].userReceivingRecordsId, isUpdate: list[0].upgradeStatus },
            })
          "
          height="120rpx"
          v-if="list[0]"
          width="120rpx"></u-image>
      </view>

      <!-- 2 -->
      <view class="absolute left-[11.47vw] top-[46.655vw] h-[142rpx] w-[142rpx]">
        <u-image
          :src="$picFormat(list[1].adornImage)"
          @tap="
            $push({
              name: 'AvatarDetail',
              params: { id: list[1].userReceivingRecordsId, isUpdate: list[1].upgradeStatus },
            })
          "
          height="142rpx"
          shape="circle"
          v-if="list[1]"
          width="142rpx"></u-image>
      </view>

      <!-- 3 -->
      <view class="absolute left-[42.13vw] top-[26.66vw] h-[262rpx] w-[262rpx]">
        <u-image
          :src="$picFormat(list[2].adornImage)"
          @tap="
            $push({
              name: 'AvatarDetail',
              params: { id: list[2].userReceivingRecordsId, isUpdate: list[2].upgradeStatus },
            })
          "
          height="262rpx"
          v-if="list[2]"
          width="262rpx"></u-image>
      </view>

      <!-- 4 -->
      <view class="absolute left-[84.53vw] top-[60.7848vw] h-[66rpx] w-[66rpx]">
        <u-image
          :src="$picFormat(list[3].adornImage)"
          @tap="
            $push({
              name: 'AvatarDetail',
              params: { id: list[3].userReceivingRecordsId, isUpdate: list[3].upgradeStatus },
            })
          "
          height="66rpx"
          v-if="list[3]"
          width="66rpx"></u-image>
      </view>
    </view>

    <!-- 筛选 -->
    <view class="filterBar relative z-10 flex w-full items-center justify-start pl-3 pr-3">
      <!-- 搜索 -->
      <view
        class="searchWrap flex h-8 w-[410rpx] grow items-center justify-start rounded-full border-[2rpx] border-solid border-[#C5C5C5] pl-2.5 pr-2.5">
        <image class="mr-[6rpx] h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
        <u-input
          class="searchBox"
          @clear="handleSearch"
          @confirm="handleSearch"
          border="none"
          clearable
          placeholder="请输入关键字搜索  "
          v-model="keyword" />
      </view>
      <!-- 排序类型 -->
      <view
        class="relative ml-2.5 flex h-8 w-[188rpx] shrink-0 items-center justify-center rounded-full border-[2rpx] border-solid border-[#C5C5C5]"
        @tap="orderListController = !orderListController">
        <text class="w-[130rpx] text-center font-Regular text-[26rpx] font-normal leading-[32rpx] text-w-60">{{
          orderByTxt
        }}</text>
        <image class="mr-[-3rpx] block h-4 w-4 shrink-0" :src="$iconFormat('arrow/down3.svg')" mode="scaleToFill" />

        <u-transition :show="orderListController" mode="fade">
          <view
            class="absolute left-0 right-0 top-[80rpx] z-20 m-auto h-fit w-[160rpx] overflow-hidden rounded-[20rpx]">
            <view
              class="h-[60rpx] w-full bg-[#332F3B] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
              :key="index"
              @tap="handleSelOrderBy(item)"
              v-for="(item, index) in orderList"
              >{{ item.name }}</view
            >
          </view>
        </u-transition>
      </view>
      <!-- 列表样式切换 -->
      <view class="ml-2.5 flex h-8 w-8 shrink-0 items-center justify-center rounded-full">
        <image
          class="block h-8 w-8"
          :src="$iconFormat('listType/item.svg')"
          @tap="listType = 2"
          mode="scaleToFill"
          v-if="listType == 1" />
        <image
          class="block h-8 w-8"
          :src="$iconFormat('listType/row.svg')"
          @tap="listType = 1"
          mode="scaleToFill"
          v-else />
      </view>
    </view>

    <!-- 头像列表 -->
    <avatarList1 :isAsc="isAsc" :keyword="keyword" :orderBy="orderBy" ref="avatarLIst1Ref" v-if="listType === 1" />
    <avatarList2 :isAsc="isAsc" :keyword="keyword" :orderBy="orderBy" ref="avatarLIst2Ref" v-else-if="listType === 2" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import avatarList1 from './components/AvatarList1.vue'
  import avatarList2 from './components/AvatarList2.vue'
  import { $userAdornDigitalAvatar } from '@/api/avatar'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $picFormat, $push } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2 } = storeToRefs(useGlobalStore())

  const keyword = ref('') // 搜索关键字
  /* 排序字段列表 */
  const orderList = ref([
    { name: '按日期', string: 'createTime', isAsc: 'desc' },
    { name: '按剧目名称', string: 'repertoireName', isAsc: 'asc' },
    // { name: '按序列编号', string: 'collectionNo', isAsc: 'asc' },
  ])
  const orderListController = ref(false) // 排序字段列表弹窗控制器
  const orderBy = ref('createTime') // 排序字段
  const orderByTxt = ref('按日期') // 排序字段文案
  const isAsc = ref('desc') // 排序顺序
  const listType = ref(1) // 列表模式

  const list = ref<any>([])
  const avatarLIst1Ref = ref()
  const avatarLIst2Ref = ref()

  onMounted(() => {
    uni.$on('updateAvatarList', handleGetAdorn)

    handleGetAdorn()
  })

  /* 获取展示电子头像 */
  const handleGetAdorn = () => {
    $userAdornDigitalAvatar(2).then((res: any) => {
      // res.data.rows.map((i: any) => {
      //   if (i.upgradeStatus === 1) i.image = i.upgradeImage
      // })
      list.value = res.data
    })
  }

  /* 搜索 */
  const handleSearch = () => {
    avatarLIst1Ref.value.handleRefresh()
    avatarLIst2Ref.value.handleRefresh()
  }

  /* 切换排序字段 */
  const handleSelOrderBy = (orderItem: any) => {
    orderBy.value = orderItem.string
    orderByTxt.value = orderItem.name

    avatarLIst1Ref.value.handleRefresh()
    avatarLIst2Ref.value.handleRefresh()
  }
</script>

<style lang="scss" scoped>
  .bgPic1 {
    background-image: url($icon + 'background/avatar1.webp');
  }

  .bgPic2 {
    background-image: url($icon + 'background/avatar2.webp');
  }
</style>
