<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-[#1F1933] bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      :title="title"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="450rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <mescroll-uni
      class="block h-0 w-full grow bg-[#1F1933]"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="m-auto w-[702rpx] pb-[100rpx] pt-2.5">
        <template :key="i.id" v-for="i in detail.userMessageInfoList">
          <!-- 对方 -->
          <template v-if="i.userMerchantId">
            <view
              class="mb-2.5 w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-40"
              v-if="i.userMerchantId"
              >{{ dayjs(i.createTime).format('MM月DD日 HH:mm') }}</view
            >

            <view class="mb-[60rpx] flex w-full items-start justify-start">
              <u-avatar
                class="shrink-0"
                :defaultUrl="$iconFormat('avatar.jpg')"
                :src="$picFormat(detail.repertoireCoverPicture || detail.theaterCoverPicture)"
                @click="handleLinkDetail"></u-avatar>

              <view class="ml-2.5 grow">
                <view class="mb-[12rpx] w-full font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60">{{
                  title
                }}</view>

                <view
                  class="w-fit max-w-[530rpx] rounded-b-[40rpx] rounded-tr-[40rpx] bg-bgPurple p-2.5 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-80"
                  >{{ i.body }}</view
                >
              </view>
            </view>
          </template>

          <!-- 我 -->
          <template v-else-if="i.userId">
            <view class="mb-2.5 w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-40">{{
              dayjs(i.createTime).format('MM月DD日 HH:mm')
            }}</view>

            <view class="mb-[60rpx] flex w-full items-start justify-end">
              <view class="mr-2.5 flex grow flex-col items-end justify-start">
                <view
                  class="mb-[12rpx] w-full text-right font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
                  >{{ userInfo.name }}</view
                >

                <view
                  class="w-fit max-w-[530rpx] rounded-b-[40rpx] rounded-tl-[40rpx] bg-bgPurple p-2.5 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-80"
                  >{{ i.body }}</view
                >
              </view>

              <u-avatar class="shrink-0" :defaultUrl="$iconFormat('avatar.jpg')" :src="userInfo.avatarUrl"></u-avatar>
            </view>
          </template>

          <!-- 演都客服 -->
          <template v-else>
            <view class="mb-2.5 w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-40">{{
              dayjs(i.createTime).format('MM月DD日 HH:mm')
            }}</view>

            <view class="mb-[60rpx] flex w-full items-start justify-start">
              <u-avatar
                class="shrink-0"
                :defaultUrl="$iconFormat('avatar.jpg')"
                :src="$iconFormat('avatar.jpg')"
                @click="handleLinkDetail"></u-avatar>

              <view class="ml-2.5 grow">
                <view class="mb-[12rpx] w-full font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
                  >演都官方客服</view
                >

                <view
                  class="w-fit max-w-[530rpx] rounded-b-[40rpx] rounded-tr-[40rpx] bg-bgPurple p-2.5 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-80"
                  >{{ i.body }}</view
                >
              </view>
            </view>
          </template>
        </template>
      </view>
    </mescroll-uni>

    <!-- 回复输入框 -->
    <view class="w-full shrink-0 bg-deepbg">
      <view class="flex h-[116rpx] items-center justify-start pl-3 pr-3">
        <!-- 回复框 -->
        <u-input
          class="replyBox h-10 grow bg-w-10"
          :adjustPosition="false"
          @confirm="handleReplyComment"
          @focus="handleEmojiClose"
          border="none"
          cursorSpacing="30"
          placeholder="说点什么…"
          ref="replyRef"
          v-model="replyCon">
          <template #suffix>
            <image
              class="block h-6 w-6"
              :src="$iconFormat('icon/emjoy.svg')"
              @click="handleEmojiOpen"
              mode="scaleToFill" />
          </template>
        </u-input>
      </view>
      <emoji :keyboardH="keyboardH" :show="emojiController" @emoji="handleEmojiChange" />

      <view class="w-full" :style="bheight"></view>
    </view>
  </view>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userMessageAdd, $userMessageDetails } from '@/api/mass'
  import emoji from '@/components/Emoji.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $push, $toast, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const title = ref('') // 标题
  const id = ref()
  const detail = ref<any>('')

  const replyCon = ref('') // 回复内容
  const emojiController = ref(false) // 表情列表显示控制器

  /* 底部安全高度 */
  const bheight = computed(() => {
    return { height: uni.$u.sys().safeAreaInsets.bottom + 'rpx' }
  })
  const keyboardH = ref(0) // 键盘高度

  uni.onKeyboardHeightChange((res: any) => {
    keyboardH.value = res.height
  })

  onLoad((params: any) => {
    id.value = params.id - 0
  })

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userMessageDetails(id.value)
      .then((res: any) => {
        title.value = res.data.repertoireName || res.data.theaterName

        detail.value = res.data

        mescroll.endSuccess(1, false)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 表情列表打开 */
  const handleEmojiOpen = () => {
    emojiController.value = !emojiController.value
  }

  /* 表情列表关闭 */
  const handleEmojiClose = () => {
    emojiController.value = false
  }

  /* 选中表情 */
  const handleEmojiChange = (e: any) => {
    replyCon.value += e
  }

  /* 回复评论 */
  const handleReplyComment = () => {
    if (replyCon.value) {
      $userMessageAdd({
        theaterId: detail.value.theaterId || undefined,
        repertoireId: detail.value.repertoireId || undefined,
        userMessageId: id.value,
        userId: userInfo.value.id,
        body: replyCon.value,
      }).then((res: any) => {
        replyCon.value = ''

        mescrollObj.value.resetUpScroll()

        uni.$emit('updateMass')
      })
    } else {
      $toast(app, '回复内容不能为空')
    }
  }

  const handleLinkDetail = () => {
    if (detail.value.repertoireId) $push({ name: 'RepertoireDetail', params: { id: detail.value.repertoireId } })
    else if (detail.value.theaterId) $push({ name: 'TheaterDetail', params: { id: detail.value.theaterId } })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/history.webp');

    &:deep(.u-navbar) {
      .u-navbar__content__left {
        position: initial;
      }

      .u-navbar__content {
        justify-content: flex-start !important;
      }
    }

    .replyBox {
      @apply rounded-full pl-[38rpx] pr-2.5 #{!important};

      &:deep(.u-input__content) {
        .u-input__content__field-wrapper {
          margin-right: 20rpx;

          &__field {
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 28rpx !important;
            font-weight: 400;
            color: #fff !important;
          }

          .input-placeholder {
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 28rpx !important;
            font-weight: 400;
            color: rgb(255 255 255 / 40%) !important;
          }
        }
      }
    }
  }
</style>
