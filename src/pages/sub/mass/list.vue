<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="消息"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="m-auto w-[702rpx] pt-[18rpx]">
        <view
          class="mb-2.5 flex w-full items-start justify-start rounded-[20rpx] bg-w-10 p-2.5 last:mb-0"
          :key="i.id"
          @tap="$push({ name: 'MassDetail', params: { id: i.id } })"
          v-for="i in list">
          <u-image
            class="block"
            :src="$picFormat(i.repertoireCoverPicture || i.theaterCoverPicture)"
            bgColor="transparent"
            height="100rpx"
            mode="aspectFill"
            radius="20rpx"
            width="100rpx"></u-image>

          <view class="ml-2.5 grow">
            <view class="mb-[12rpx] flex items-center justify-start">
              <text class="mr-2.5 line-clamp-1 grow font-Medium text-[36rpx] font-medium leading-[50rpx] text-w-100">{{
                i.repertoireName || i.theaterName
              }}</text>
              <text class="shrink-0 font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60">{{
                i.updateTime
              }}</text>
            </view>
            <view class="flex items-start justify-start">
              <text class="mr-2.5 line-clamp-1 grow font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60">{{
                i.lastBody || '-'
              }}</text>

              <view class="flex w-[86] shrink-0 items-center justify-end" v-if="i.lookFlag == 0">
                <view
                  class="flex h-5 w-5 items-center justify-center rounded-full bg-[#EC2A2A] text-center font-Regular text-[24rpx] font-normal leading-[40rpx] text-w-100"
                  v-if="i.notLookCount && notifySetting.groupMessageNotify == 1"
                  >{{ i.notLookCount }}</view
                >

                <template v-if="i.notLookCount && notifySetting.groupMessageNotify == 0">
                  <view class="mr-[10rpx] h-[12rpx] w-[12rpx] rounded-full bg-[#EC2A2A]"></view>
                  <image class="block h-4 w-4" :src="$iconFormat('icon/msgOff.svg')" mode="scaleToFill" />
                </template>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/comment.svg')"
          height="322rpx"
          mode="data"
          text="哔嘟哔嘟~暂无消息"
          width="472rpx"></u-empty>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userMessageListByPage } from '@/api/mass'
  import { $userSetting } from '@/api/setting'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $picFormat, $push } from '@/utils/methods'
  import { $iconFormat, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const { titleStyle2, userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj
  const list = ref<any>([]) // 商品列表
  const notifySetting = ref<any>('') // 用户设置

  onLoad(() => {
    uni.$on('updateMass', () => {
      if (mescrollObj.value) mescrollObj.value.resetUpScroll()
    })
  })

  onShow(() => {
    if (mescrollObj.value) mescrollObj.value.resetUpScroll()
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userSetting().then((res: any) => {
      notifySetting.value = res.data
    })

    $userMessageListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      // orderByColumn: 'updateTime',
      // isAsc: 'desc',
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.updateTime) {
            let flag = dayjs().isAfter(i.updateTime, 'day')
            if (flag) i.updateTime = dayjs(i.updateTime).format('YY/M/D HH:mm')
            else i.updateTime = dayjs(i.updateTime).format('HH:mm')
          }
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据
        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }
</script>

<style lang="scss" scoped></style>
