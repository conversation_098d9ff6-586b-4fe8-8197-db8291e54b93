<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      placeholder
      titleWidth="574rpx">
      <template #left>
        <image class="block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/close4.svg')" mode="scaleToFill" />
      </template>
    </u-navbar>
    <view class="w-full border-b border-solid border-w-10"></view>

    <mescroll-uni
      class="block h-0 w-full grow overflow-hidden"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt1"
      @down="upCallback"
      @init="mescrollInit">
      <view class="w-full pt-[60rpx]">
        <image
          class="mb-[20rpx] ml-auto mr-auto block h-[160rpx] w-[160rpx] rounded-[16rpx]"
          :src="$picFormat(detail.repertoireCoverPicture)"
          mode="scaleToFill" />

        <view class="mb-[50rpx] w-full text-center font-Regular text-[36rpx] font-normal leading-[50rpx] text-[#FFFFFF]"
          >{{ detail.repertoireName || '-' }}票面升级包</view
        >

        <view class="mb-[74rpx] w-full text-center text-[60rpx] font-bold leading-[70rpx] text-[#FF7F1A]"
          >¥{{ detail.payPrice || '0.00' }}</view
        >

        <!-- 订单信息 -->
        <view class="mb-[30rpx] ml-auto mr-auto w-[702rpx] rounded-[16rpx] bg-[#251E3D] pb-[34rpx] pt-[28rpx]">
          <view class="mb-[20rpx] flex w-full items-center justify-start pl-[20rpx] pr-[20rpx]">
            <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">订单编号</text>
            <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
              detail.orderNo || '-'
            }}</text>
          </view>

          <view class="mb-[20rpx] flex w-full items-center justify-start pl-[20rpx] pr-[20rpx]">
            <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">订单时间</text>
            <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
              detail.createTime || '-'
            }}</text>
          </view>

          <view class="mb-[20rpx] flex w-full items-center justify-start pl-[20rpx] pr-[20rpx]">
            <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">订单状态</text>
            <text
              class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-[#E9C49D]"
              v-if="detail.payStatus === 0"
              >待支付</text
            >
            <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100" v-if="detail.payStatus === 1"
              >支付成功</text
            >
            <text
              class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-[#EC2A2A]"
              v-if="detail.payStatus === 2"
              >支付关闭</text
            >
          </view>

          <view class="mb-[20rpx] flex w-full items-center justify-start pl-[20rpx] pr-[20rpx]">
            <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">套餐内容</text>
            <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
              detail.portfolioName || '-'
            }}</text>
          </view>

          <view class="mb-[20rpx] flex w-full items-center justify-start pl-[20rpx] pr-[20rpx]">
            <text class="mr-[30rpx] font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">支付时间</text>
            <text class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
              detail.payTime || '-'
            }}</text>
          </view>

          <view class="mb-[20rpx] flex items-center justify-start">
            <view class="h-[20rpx] w-[10rpx] shrink-0 rounded-e-[10rpx] bg-[#0A0713]"></view>
            <view class="grow border-b-[1rpx] border-dashed border-w-10"></view>
            <view class="h-[20rpx] w-[10rpx] shrink-0 rounded-s-[10rpx] bg-[#0A0713]"></view>
          </view>

          <view class="flex items-center justify-start pl-[20rpx] pr-[20rpx]">
            <view class="col grow">
              <view
                class="mb-[40rpx] flex w-full items-center justify-start"
                :key="index"
                v-for="(i, index) in detail?.userReceivingRecordList">
                <image
                  class="mr-[20rpx] block h-[150rpx] w-[150rpx] shrink-0 rounded-[10rpx]"
                  :src="$picFormat(i.upgradeImage)"
                  mode="aspectFit" />

                <view class="mr-[20rpx] grow">
                  <view
                    class="mb-[20rpx] font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
                    v-if="i.badgeType === 1"
                    >{{ detail.repertoireName || '-' }}升级收藏票</view
                  >
                  <view
                    class="mb-[20rpx] font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
                    v-if="i.badgeType === 2"
                    >{{ detail.repertoireName || '-' }}升级数字头像</view
                  >

                  <view class="font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60">x1</view>
                </view>
              </view>
            </view>

            <template :key="'a' + index" v-for="(i, index) in detail?.userReceivingRecordList">
              <view class="ml-[20rpx] shrink-0" v-if="i.badgeType === 1">
                <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">￥</text>
                <text class="font-Regular text-[40rpx] font-normal leading-[34rpx] text-w-100">{{
                  detail.payPrice || '0.00'
                }}</text>
              </view>
            </template>
          </view>
        </view>

        <view class="m-auto flex w-[580rpx] items-center justify-between">
          <customBtn @tap="handleCancel" text="取消支付" v-if="detail.payStatus === 0" width="260" />
          <customBtn type="3" @tap="handlePay" text="立即支付" v-if="detail.payStatus === 0" width="260" />
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $cancelOrder, $userOrderDetail } from '@/api/order'
  import { $getPayInfo } from '@/api/pay'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $toast } from '@/utils/methods'

  const { userInfo, titleStyle2, downOpt1, upOpt1 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const app: any = getCurrentInstance()?.proxy
  const mescrollObj = ref() // 滚动obj

  const id = ref() // 订单id
  const detail = ref<any>([]) // 订单详情

  onLoad((params: any) => {
    id.value = params.id
  })

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userOrderDetail(id.value)
      .then((res: any) => {
        detail.value = res.data
        mescroll.endSuccess(1, false)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 立即支付 */
  const handlePay = () => {
    $getPayInfo(detail.value.userReceivingRecordList[0].id).then((res: any) => {
      uni.requestPayment({
        provider: 'wxpay',
        orderInfo: '',
        timeStamp: res.data.timeStamp,
        nonceStr: res.data.nonceStr,
        package: res.data.packageStr,
        signType: 'RSA',
        paySign: res.data.sign,
        success: (res: any) => {},
        fail: (err: any) => {},
      })
    })
  }

  /* 取消支付 */
  const handleCancel = () => {
    $cancelOrder(id.value).then((res: any) => {
      $toast(app, '已取消')

      mescrollObj.value.resetUpScroll()
    })
  }
</script>

<style lang="scss" scoped></style>
