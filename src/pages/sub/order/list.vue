<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="我的订单"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <!-- 搜索 -->
    <view
      class="searchWrap m-auto mb-[18rpx] mt-[18rpx] flex h-8 w-[702rpx] items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
      <image class="mr-[6rpx] h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @clear="handleSearch"
        @confirm="handleSearch"
        border="none"
        clearable
        placeholder="请输入剧目名称搜索"
        v-model="keyword" />
      <text
        class="ml-[6rpx] shrink-0 font-Regular text-[32rpx] font-normal leading-[44rpx] text-[#9C75D3]"
        @click="handleSearch"
        >搜索</text
      >
    </view>

    <mescroll-uni
      class="block h-0 w-full grow overflow-hidden"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <!-- 订单列表 -->
      <view class="w-full pl-[24rpx] pr-[24rpx]">
        <view
          class="mb-[10rpx] flex w-full items-center justify-start rounded-[20rpx] bg-[#251E3D] pb-2 pl-2 pr-[20rpx] pt-2"
          :key="index"
          @click="$push({ name: 'OrderDetail', params: { id: i.id } })"
          v-for="(i, index) in list">
          <image
            class="block h-[96rpx] w-[96rpx] shrink-0 rounded-[16rpx]"
            :src="$picFormat(i.repertoireCoverPicture)"
            mode="scaleToFill" />

          <view class="ml-[16rpx] grow">
            <view class="font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
              >{{ i.repertoireName || '-' }}电子票升级</view
            >
            <view class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
              i.createTime || '-'
            }}</view>
          </view>

          <view
            class="ml-[36rpx] mr-[36rpx] shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#FF9723]"
            v-if="i.payStatus === 0"
            >待支付</view
          >
          <view
            class="ml-[36rpx] mr-[36rpx] shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#1C893E]"
            v-else-if="i.payStatus === 1"
            >已支付</view
          >
          <view
            class="ml-[36rpx] mr-[36rpx] shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#EC2A2A]"
            v-else-if="i.payStatus === 2"
            >已取消</view
          >

          <view class="shrink-0 text-[36rpx] font-bold leading-[42rpx] text-[#FF7F1A]">¥{{ i.payPrice }}</view>
        </view>

        <!-- 空状态 -->
        <view class="emptyWrap mtPatch2 w-full pt-[200rpx]" v-if="!list || !list.length">
          <u-empty
            :icon="$iconFormat('empty/collection.webp')"
            :text="`哔嘟哔嘟~暂无订单~`"
            height="385rpx"
            mode="data"
            width="487rpx"></u-empty>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userOrderList } from '@/api/order'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'

  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const keyword = ref('') // 搜索关键字

  const list = ref<any>([]) // 勋章列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $userOrderList({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      keyword: keyword.value || undefined,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleSearch = () => {
    mescrollObj.value.resetUpScroll()
  }
</script>

<style lang="scss" scoped></style>
