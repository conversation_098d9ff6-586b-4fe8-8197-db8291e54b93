<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="搜索"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <!-- 搜索 -->
    <view
      class="searchWrap m-auto mt-[10rpx] flex h-8 w-[702rpx] shrink-0 items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
      <image class="mr-1 h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @confirm="handleSearch"
        border="none"
        clearable
        placeholder="请输入关键字搜索"
        v-model="keyword" />
      <view class="ml-2.5 shrink-0 font-Regular text-base font-normal leading-[44rpx] text-purple" @tap="handleSearch"
        >搜索</view
      >
    </view>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $push } from '@/utils/methods'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy
  const { titleStyle2 } = storeToRefs(useGlobalStore())

  const tabList = ref([{ name: '剧场' }, { name: '剧目' }]) // tab
  const listType = ref(0) // 列表类型
  /* tab未激活样式 */
  const inactiveStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '28rpx',
    fontFamily: 'PingFangSC-Regular, PingFang SC',
    fontWeight: 400,
    color: 'rgb(255 255 255 / 60%)',
    lineHeight: '40rpx',
  })
  /* tab激活样式 */
  const activeStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '40rpx',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontWeight: 500,
    color: '#ffffff',
    lineHeight: '56rpx',
  })

  const keyword = ref('') // 搜索关键字
  const historyList = ref(uni.getStorageSync('historyList') || []) // 搜索历史

  const repertoireListRef = ref() // 剧目列表ref
  const theaterListRef = ref() // 剧场列表ref

  onLoad((params: any) => {
    keyword.value = decodeURIComponent(params.keyword)
  })

  /* tab栏切换 */
  const handleTabChange = (e: any) => {
    listType.value = e.index
  }

  /* 搜索 */
  const handleSearch = () => {
    if (!keyword.value) return

    if (!historyList.value.includes(keyword.value)) {
      if (historyList.value.length >= 15) historyList.value = _.initial(historyList.value)

      historyList.value.unshift(keyword.value)
      uni.setStorageSync('historyList', historyList.value)

      repertoireListRef.value.mescrollObj.resetUpScroll()
      theaterListRef.value.mescrollObj.resetUpScroll()
    }
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .tabBar {
      padding: 0 134rpx;

      &:deep(.u-tabs__wrapper) {
        .u-tabs__wrapper__nav {
          &__item {
            position: relative;
            z-index: 2;
            width: 120rpx;
            height: 102rpx !important;
            padding: 0;
          }

          &__line {
            bottom: 24rpx !important;
            z-index: 1;
          }
        }
      }
    }
  }
</style>
