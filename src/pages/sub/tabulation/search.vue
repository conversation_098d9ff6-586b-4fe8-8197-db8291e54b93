<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="搜索"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <!-- 搜索 -->
    <view
      class="searchWrap m-auto mt-[10rpx] flex h-8 w-[702rpx] shrink-0 items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
      <image class="mr-1 h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @confirm="handleSearch"
        @focus="searchMode = 0"
        border="none"
        clearable
        placeholder="输入剧目/剧场名搜索"
        v-model="keyword" />
      <view class="ml-2.5 shrink-0 font-Regular text-base font-normal leading-[44rpx] text-purple" @tap="handleSearch"
        >搜索</view
      >
    </view>

    <!-- 搜索历史 -->
    <view class="h-0 w-full grow pb-[100rpx] pt-5" v-if="searchMode === 0">
      <view class="w-full pl-3 pr-3">
        <template :key="index" v-for="(item, index) in historyList">
          <view
            class="mb-5 flex w-full items-start justify-start last:mb-0"
            @tap="handleSelKeyword(item)"
            v-if="(!showMoreHistory && index < 7) || showMoreHistory">
            <image class="mr-1 block h-4 w-4 shrink-0" :src="$iconFormat('icon/history.svg')" mode="scaleToFill" />
            <text class="grow break-all font-Regular text-[30rpx] font-normal leading-[32rpx] text-w-60">{{
              item
            }}</text>
            <image
              class="ml-1 block h-4 w-4 shrink-0"
              :src="$iconFormat('icon/close1.svg')"
              @tap.stop="handleDelHistory(item)"
              mode="scaleToFill" />
          </view>
        </template>
      </view>

      <view
        class="mt-[50rpx] w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
        @tap="showMoreHistory = true"
        v-if="historyList.length && !showMoreHistory && historyList.length > 7"
        >更多历史记录</view
      >
      <view
        class="mt-[50rpx] w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
        @tap="handleClearHistory"
        v-else-if="historyList.length && (showMoreHistory || historyList.length <= 7)"
        >清除全部搜索记录</view
      >
    </view>

    <!-- tab切换 -->
    <u-tabs
      class="tabBar w-full border-b border-w-20"
      :activeStyle="activeStyle"
      :inactiveStyle="inactiveStyle"
      :list="tabList"
      :scrollable="false"
      @change="handleTabChange"
      lineColor="linear-gradient(90deg, #844FD2 0%, #4230C2 100%)"
      lineHeight="10rpx"
      lineWidth="80rpx"
      v-if="searchMode === 1" />

    <!-- 剧目 -->
    <repertoireList :keyword="keyword" ref="repertoireListRef" v-if="searchMode === 1 && listType === 0" />

    <!-- 剧场 -->
    <theaterList :keyword="keyword" ref="theaterListRef" v-if="searchMode === 1 && listType === 1" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import repertoireList from '@/components/List/RepertoireList.vue'
  import theaterList from '@/components/List/TheaterList.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat } from '@/utils/methods'
  import _ from 'lodash'

  const app: any = getCurrentInstance()?.proxy
  const { titleStyle2 } = storeToRefs(useGlobalStore())

  const keyword = ref('') // 搜索关键字
  const searchMode = ref(0) // 是否搜索了

  const showMoreHistory = ref(false) // 显示更多历史记录
  const historyList = ref(uni.getStorageSync('historyList') || []) // 搜索历史

  const tabList = ref([{ name: '剧目' }, { name: '剧场' }]) // tab
  const listType = ref(0) // 列表类型
  /* tab未激活样式 */
  const inactiveStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '28rpx',
    fontFamily: 'PingFangSC-Regular, PingFang SC',
    fontWeight: 400,
    color: 'rgb(255 255 255 / 60%)',
    lineHeight: '40rpx',
  })
  /* tab激活样式 */
  const activeStyle = ref({
    display: 'block',
    width: 'fit-content',
    fontSize: '40rpx',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontWeight: 500,
    color: '#ffffff',
    lineHeight: '56rpx',
  })

  const repertoireListRef = ref() // 剧目列表ref
  const theaterListRef = ref() // 剧场列表ref

  /* 点击历史/热门搜索 */
  const handleSelKeyword = (item: string) => {
    keyword.value = item

    handleSearch()
  }

  /* 删除历史记录 */
  const handleDelHistory = (item: string) => {
    historyList.value = _.without(historyList.value, item)
    uni.setStorageSync('historyList', historyList.value)
  }

  /* 清空历史记录 */
  const handleClearHistory = () => {
    historyList.value = []
    uni.setStorageSync('historyList', historyList.value)
  }

  /* 搜索 */
  const handleSearch = () => {
    if (!keyword.value) return

    if (!historyList.value.includes(keyword.value)) {
      if (historyList.value.length >= 15) historyList.value = _.initial(historyList.value)

      historyList.value.unshift(keyword.value)
      uni.setStorageSync('historyList', historyList.value)
    }

    searchMode.value = 1

    if (repertoireListRef.value) repertoireListRef.value.mescrollObj.resetUpScroll()
    if (theaterListRef.value) theaterListRef.value.mescrollObj.resetUpScroll()
  }

  /* tab栏切换 */
  const handleTabChange = (e: any) => {
    listType.value = e.index
  }
</script>

<style lang="scss" scoped></style>
