<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="我的勋章"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <!-- 搜索 -->
    <view
      class="searchWrap m-auto mb-[18rpx] mt-[18rpx] flex h-8 w-[702rpx] items-center justify-start rounded-full bg-w-10 pl-2.5 pr-2.5">
      <image class="mr-[6rpx] h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
      <u-input
        class="searchBox"
        @clear="handleSearch"
        @confirm="handleSearch"
        border="none"
        clearable
        placeholder="请输入关键字搜索"
        v-model="keyword" />
    </view>

    <mescroll-uni
      class="block h-0 w-full grow overflow-hidden rounded-t-[30rpx] bg-gradient-to-b from-w-f to-w-t"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <!-- 勋章列表 -->
      <view class="w-full pl-[28rpx] pr-[28rpx] pt-5">
        <view class="mb-5 w-full last:mb-0" :key="i.id" v-for="i in list">
          <!-- 所属剧场 -->
          <view class="mb-2.5 flex items-center justify-start">
            <view class="dotBg h-3 w-3 bg-fullAuto bg-no-repeat"></view>
            <view class="ml-1 font-Regular text-[28rpx] font-normal leading-[32rpx] text-w-100">{{
              i.theaterName || i.repertoireName || '-'
            }}</view>
          </view>

          <view
            class="mb-2.5 flex w-full flex-col items-start justify-start pl-[32rpx] pr-[32rpx]"
            :key="son.id"
            @tap="handleSelMedal(son)"
            v-for="son in i.userReceivingRecordsList">
            <!-- 勋章 -->
            <view class="flex items-center justify-start">
              <medal :color="son.rankMedalColor" :level="son.rankMedalLevel" :text="son.rankMedalName" />

              <!-- 是否佩戴 -->
              <view
                class="ml-[10rpx] h-4 w-fit shrink-0 rounded-full bg-purpleBg pl-[10rpx] pr-[10rpx] text-center font-Regular text-[22rpx] font-normal leading-[32rpx] text-[#9C75D3]"
                v-if="userInfo.rankMedalInfoId === son.relationId"
                >佩戴中</view
              >
            </view>

            <!-- 观看次数 -->
            <view class="mb-[10rpx] mt-[12rpx] font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">
              <text>累计观看</text>
              <text class="font-Medium text-[28rpx] font-medium text-[#838BFF]">{{ son.expenseNumber }}</text>
              <text>场次</text>
            </view>
            <view class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60"
              >获得时间：{{ son.createTime || '未知' }}</view
            >
          </view>
        </view>

        <!-- 空状态 -->
        <view class="emptyWrap mtPatch2 w-full pt-[200rpx]" v-if="!list || !list.length">
          <u-empty
            :icon="$iconFormat('empty/collection.webp')"
            :text="`哔嘟哔嘟~暂无勋章~`"
            height="385rpx"
            mode="data"
            width="487rpx"></u-empty>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <!-- 勋章详情 -->
  <u-popup :show="popupContaoller" @close="handleClose" bgColor="transparent" round="0">
    <view class="medalDetail relative m-auto h-[714rpx] w-full bg-fullAuto bg-no-repeat pt-[30rpx]">
      <image
        class="absolute right-0 top-0 block h-6 w-6"
        :src="$iconFormat('icon/close2.svg')"
        @tap="handleClose"
        mode="scaleToFill" />

      <view class="flex w-full flex-col items-center justify-start pt-[124rpx]" v-if="selMedal">
        <medal :color="selMedal.rankMedalColor" :level="selMedal.rankMedalLevel" :text="selMedal.rankMedalName" />

        <view class="mb-[18rpx] mt-[30rpx] text-center font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100"
          >《{{ selMedal.theaterName || selMedal.repertoireName }}》</view
        >
        <view class="text-cenetr mb-[30rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
          >累计观看{{ selMedal.expenseNumber }}次可以获得</view
        >
        <view class="mb-[76rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
          >获得时间：{{ selMedal.createTime }}</view
        >

        <view class="flex items-center justify-center">
          <!-- <customBtn type="3" text="分享一下" width="260" /> -->
          <customBtn
            @tap="handleSave(true)"
            text="佩戴勋章"
            v-if="userInfo.rankMedalInfoId !== selMedal.relationId"
            width="260" />
          <customBtn type="4" @tap="handleSave(false)" text="取消佩戴" v-else width="260" />
        </view>
      </view>
    </view>
  </u-popup>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userUpdate } from '@/api/account'
  import { $collectionListByRankMedal, $rankMedalDetails } from '@/api/medal'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import medal from '@/components/Medal.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $topclick } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const keyword = ref('') // 搜索关键字

  const list = ref<any>([]) // 勋章列表

  const popupContaoller = ref(false) // 弹窗控制器
  const selMedal = ref<any>('') // 选中的徽章

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $collectionListByRankMedal({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      badgeType: 4,
      userId: userInfo.value.id,
      keyword: keyword.value || undefined,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleSearch = () => {
    mescrollObj.value.resetUpScroll()
  }

  /* 选中勋章 */
  const handleSelMedal = (item: any) => {
    $rankMedalDetails(item.id).then((res: any) => {
      selMedal.value = res.data
      popupContaoller.value = true
    })
  }

  /* 关闭弹窗 */
  const handleClose = () => {
    selMedal.value = ''
    popupContaoller.value = false
  }

  /* 佩戴徽章 */
  const handleSave = (flag: boolean) => {
    uni.showLoading()

    $userUpdate({
      id: userInfo.value.id,
      rankMedalInfoId: flag ? selMedal.value.relationId : 0,
    }).then((res: any) => {
      uni.hideLoading()

      globalStore.handleRefreshUserInfo()

      handleClose()
    })
  }
</script>

<style lang="scss" scoped>
  .medalDetail {
    background-image: url($icon + 'background/medal.webp');
  }

  .dotBg {
    background-image: url($icon + 'icon/dot1.svg');
  }
</style>
