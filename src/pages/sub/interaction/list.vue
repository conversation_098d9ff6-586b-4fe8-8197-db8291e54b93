<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="互动消息"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <!-- 列表分类 -->
    <u-tabs
      class="tabBar mt-2.5 w-full shrink-0"
      :activeStyle="activeStyle1"
      :current="listType1"
      :inactiveStyle="inactiveStyle1"
      :list="tabList1"
      :scrollable="false"
      @change="handleTabChange1"
      lineColor="linear-gradient(90deg, #844FD2 0%, #4230C2 100%)"
      lineHeight="10rpx"
      lineWidth="80rpx" />

    <!-- 状态分类 -->
    <u-tabs
      class="tabBar mt-2.5 w-full shrink-0"
      :activeStyle="activeStyle2"
      :current="listType2"
      :inactiveStyle="inactiveStyle2"
      :list="tabList2"
      :scrollable="false"
      @change="handleTabChange2"
      lineColor="transparent"
      lineHeight="0"
      lineWidth="0" />

    <comment1 v-if="listType1 == 0 && listType2 == 0" />
    <comment2 v-if="listType1 == 0 && listType2 == 1" />
    <kukoList1 v-if="listType1 == 1 && listType2 == 0" />
    <kukoList2 v-if="listType1 == 1 && listType2 == 1" />
    <issueList1 v-if="listType1 == 2 && listType2 == 0" />
    <issueList2 v-if="listType1 == 2 && listType2 == 1" />
  </view>
</template>

<script lang="ts" setup>
  import issueList1 from './components/IssueList1.vue'
  import issueList2 from './components/IssueList2.vue'
  import kukoList1 from './components/KudoList1.vue'
  import kukoList2 from './components/KudoList2.vue'
  import comment1 from './components/comment1.vue'
  import comment2 from './components/comment2.vue'
  import { $receivingRecordsCount } from '@/api/count'
  import { useGlobalStore } from '@/stores/global'
  import { $back } from '@/utils/methods'

  const { titleStyle2 } = storeToRefs(useGlobalStore())

  const tabList1 = ref([
    { name: '评论', badge: { value: 0 } },
    { name: '点赞', badge: { value: 0 } },
    { name: '提问', badge: { value: 0 } },
  ])
  const listType1 = ref(0) // 列表分类
  /* tab未激活样式1 */
  const inactiveStyle1 = ref({
    display: 'block',
    fontFamily: 'PingFangSC-Regular, PingFang SC',
    fontSize: '32rpx',
    fontWeight: 400,
    lineHeight: '44rpx',
    textAlign: 'center',
    width: '50%',
    color: 'rgb(255 255 255 / 40%)',
  })
  /* tab激活样式1 */
  const activeStyle1 = ref({
    display: 'block',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '32rpx',
    fontWeight: 500,
    lineHeight: '44rpx',
    textAlign: 'center',
    width: '50%',
    color: '#9C75D3',
  })

  const tabList2 = computed(() => {
    switch (listType1.value) {
      case 0:
        return [{ name: '评论我的' }, { name: '我评论的' }]
      case 1:
        return [{ name: '点赞我的' }, { name: '我点赞的' }]
      case 2:
        return [{ name: '邀请我的' }, { name: '我的提问' }]
      default:
        return ''
    }
  })
  const listType2 = ref(0) // 状态分类
  /* tab未激活样式2 */
  const inactiveStyle2 = ref({
    color: 'rgb(255 255 255 / 40%)',
    display: 'block',
    fontFamily: 'PingFangSC-Regular, PingFang SC',
    fontSize: '32rpx',
    fontWeight: 400,
    lineHeight: '64rpx',
    textAlign: 'center',
    width: '180rpx',
    height: '64rpx',
    background: 'rgb(255 255 255 / 10%)',
    borderRadius: '32rpx',
  })
  /* tab激活样式2 */
  const activeStyle2 = ref({
    color: '#9C75D3',
    display: 'block',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '32rpx',
    fontWeight: 500,
    lineHeight: '64rpx',
    textAlign: 'center',
    width: '180rpx',
    height: '64rpx',
    background: 'rgb(156 117 211 / 40%)',
    borderRadius: '32rpx',
  })

  onLoad(async () => {
    tabList1.value[0].badge.value = (await $receivingRecordsCount(1)).data
    tabList1.value[1].badge.value = (await $receivingRecordsCount(2)).data
    tabList1.value[2].badge.value = (await $receivingRecordsCount(3)).data

    uni.$on('updateInteractionCount', async () => {
      tabList1.value[0].badge.value = (await $receivingRecordsCount(1)).data
      tabList1.value[1].badge.value = (await $receivingRecordsCount(2)).data
      tabList1.value[2].badge.value = (await $receivingRecordsCount(3)).data
    })
  })

  /* tab栏切换1 */
  const handleTabChange1 = (e: any) => {
    listType1.value = e.index
    listType2.value = 0
  }

  /* tab栏切换2 */
  const handleTabChange2 = (e: any) => {
    listType2.value = e.index
  }
</script>

<style lang="scss" scoped>
  .tabBar {
    &:deep(.u-tabs__wrapper__nav__item) {
      position: relative;

      .u-badge {
        position: absolute;
        top: 0;
        bottom: 0;
        left: calc(50% + 40rpx);
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32rpx;
        height: 32rpx;
        padding: 0 6rpx;
        margin: auto;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 26rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: #fff;
        background: #ec2a2a;
      }
    }
  }
</style>
