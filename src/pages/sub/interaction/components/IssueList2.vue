<template>
  <mescroll-uni
    class="block h-0 w-full grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt3"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="m-auto w-[702rpx] overflow-hidden pt-[30rpx]">
      <view
        class="mb-2.5 flex w-full items-start justify-start"
        :key="i.id"
        @tap="handleCheckDetail(i)"
        v-for="i in list">
        <u-avatar
          class="shrink-0"
          :defaultUrl="$iconFormat('avatar.jpg')"
          :src="$picFormat(i.replyUserAvatar)"
          size="64rpx"></u-avatar>

        <view class="ml-2.5 grow">
          <view class="mb-[12rpx] w-full font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60">{{
            $hideName(i.replyUserName)
          }}</view>

          <view class="mb-2.5 flex grow items-start justify-start">
            <view class="mb-2 mr-[28rpx] w-full grow font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
              >回答了你：{{ i.replyIssueContent || '-' }}</view
            >

            <u-image
              class="shrink-0"
              :src="$picFormat(i.repertoireCoverPicture)"
              height="80rpx"
              mode="aspectFill"
              radius="10rpx"
              width="80rpx"></u-image>
          </view>

          <view class="flex w-full items-center justify-start">
            <text class="mr-5 shrink-0 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-40">问大家</text>
            <text class="mr-2.5 grow font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-40">{{
              dayjs(i.createTime).format('YYYY/MM/DD')
            }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/comment.svg')"
          height="322rpx"
          mode="data"
          text="哔嘟哔嘟~暂无我的提问"
          width="472rpx"></u-empty>
      </view>
    </view>
  </mescroll-uni>

  <askDetail :popupContaoller="popupContaoller2" :selAsk="selAsk" @close="handleCloseDetailPopup" v-if="selAsk" />
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $listByPageByIssue } from '@/api/interaction'
  import askDetail from '@/components/Ask/Detasil.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $hideName, $iconFormat, $picFormat, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 商品列表

  /* 回答按钮样式 */
  const btnStyle1 = ref({
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '26rpx',
    fontWeight: 500,
    padding: 0,
  })

  const popupContaoller2 = ref(false)
  const selAsk = ref<any>('')

  onLoad(() => {
    uni.$on('updateAsk', () => {
      mescrollObj.value.resetUpScroll()
    })
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    uni.$emit('updateInteractionCount')

    $listByPageByIssue({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 查看问答详情 */
  const handleCheckDetail = (item: any) => {
    let obj = { ...item }
    obj.id = obj.relevanceId
    selAsk.value = obj
    popupContaoller2.value = true
  }

  /* 关闭详情 */
  const handleCloseDetailPopup = () => {
    selAsk.value = {}
    popupContaoller2.value = false
  }
</script>

<style lang="scss" scoped></style>
