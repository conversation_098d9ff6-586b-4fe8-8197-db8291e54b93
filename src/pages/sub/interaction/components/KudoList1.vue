<template>
  <mescroll-uni
    class="block h-0 w-full grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt3"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="m-auto w-[702rpx] overflow-hidden pt-[30rpx]">
      <view class="mb-2.5 flex w-full items-start justify-start" :key="i.id" v-for="i in list">
        <u-avatar
          class="shrink-0"
          :defaultUrl="$iconFormat('avatar.jpg')"
          :src="$picFormat(i.userAvatar)"
          size="64rpx"></u-avatar>

        <view class="ml-2.5 flex grow items-start justify-start">
          <view class="mr-2.5 grow">
            <view class="mb-[12rpx] w-full font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60">{{
              $hideName(i.userName)
            }}</view>
            <view class="mb-2 w-full font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
              >点赞了你的评价</view
            >

            <view class="mb-2.5 flex items-start justify-start">
              <view class="mr-2.5 h-[60rpx] w-[2rpx] shrink-0 bg-w-20"></view>
              <view class="line-clamp-2 grow font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
                i.commentContent || '-'
              }}</view>
            </view>

            <view class="w-full font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-40">{{
              dayjs(i.createTime).format('YYYY/MM/DD')
            }}</view>
          </view>
          <u-image
            :src="$picFormat(i.repertoireCoverPicture)"
            height="128rpx"
            mode="aspectFill"
            radius="10rpx"
            width="128rpx"></u-image>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/comment.svg')"
          height="322rpx"
          mode="data"
          text="哔嘟哔嘟~暂无点赞我的"
          width="472rpx"></u-empty>
      </view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $listByPageByKudos } from '@/api/interaction'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $hideName, $iconFormat, $picFormat, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 商品列表

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    uni.$emit('updateInteractionCount')

    $listByPageByKudos({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      replyUserId: userInfo.value.id,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }
</script>

<style lang="scss" scoped></style>
