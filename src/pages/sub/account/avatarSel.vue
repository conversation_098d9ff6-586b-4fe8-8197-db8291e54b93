<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="选择数字头像"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <view class="flex h-[164rpx] w-full items-start justify-center pt-[60rpx]">
      <text class="font-Medium text-[36rpx] font-medium leading-[50rpx] text-w-100">{{ userInfo.name }}</text>
      <text class="ml-[1em] font-Medium text-[28rpx] font-medium leading-[50rpx] text-w-100">的数字头像</text>
    </view>

    <mescroll-uni
      class="block h-0 w-full grow rounded-t-[30rpx] bg-[#201A34]"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="w-full pb-[100rpx] pl-3 pr-3 pt-5" v-if="list && list.length">
        <view class="mb-[20rpx] w-full" :key="index" v-for="(i, index) in list">
          <view class="mb-2.5 flex w-full items-center justify-start">
            <image class="block h-4 w-4" :src="$iconFormat('icon/dot1.svg')" mode="scaleToFill" />
            <view class="ml-[4rpx] font-Medium text-[30rpx] font-medium leading-[32rpx] text-w-100">{{
              i.repertoireName
            }}</view>
          </view>

          <view class="flex flex-wrap items-start justify-start pl-[50rpx] pr-[50rpx]">
            <view
              class="avatarItem mb-[30rpx] mr-[30rpx] flex h-[128rpx] w-[128rpx] items-center justify-center bg-fullAuto bg-no-repeat"
              :key="j.id"
              @tap="handleSelAvatar(j)"
              v-for="j in i.userReceivingRecordsList">
              <u-image
                :src="$picFormat(j.image)"
                bgColor="#7C6E5D"
                height="100rpx"
                mode="aspectFill"
                width="100rpx"></u-image>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap mtPatch w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/ticket.webp')"
          height="256rpx"
          mode="data"
          text="哔嘟哔嘟~您暂时还没有数字头像~"
          width="386rpx"></u-empty>
      </view>
    </mescroll-uni>
  </view>

  <!--  数字头像详情 -->
  <u-popup :show="popupContaoller" @close="handleClose" bgColor="transparent" round="0">
    <view class="avatarDetail relative m-auto h-[1080rpx] w-full bg-fullAuto bg-no-repeat pt-[108rpx]">
      <image
        class="absolute right-0 top-0 block h-6 w-6"
        :src="$iconFormat('icon/close2.svg')"
        @tap="handleClose"
        mode="scaleToFill" />

      <view class="flex w-full flex-col items-center justify-start" v-if="selAvatar">
        <view
          class="avatarItem mb-[48rpx] flex h-[380rpx] w-[380rpx] items-center justify-center bg-fullAuto bg-no-repeat">
          <u-image
            :src="$picFormat(selAvatar.image)"
            bgColor="#7C6E5D"
            height="288rpx"
            mode="aspectFill"
            width="288rpx"></u-image>
        </view>

        <view class="mb-[18rpx] mt-[30rpx] text-center font-Regular text-[32rpx] font-normal leading-[44rpx] text-w-100"
          >《{{ selAvatar.repertoireName }}》</view
        >
        <!-- <view class="text-cenetr mb-[10rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">专属编号：{{ selAvatar.sendId }}</view> -->
        <view
          class="mb-[76rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
          v-if="selAvatar.isUpdate"
          >升级时间：{{ selAvatar.upgradeTime }}</view
        >
        <view class="mb-[76rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60" v-else
          >获得时间：{{ selAvatar.createTime }}</view
        >

        <customBtn @tap="handleSave(true)" text="佩戴头像" v-if="userInfo.avatar !== selAvatar.image" />
        <customBtn type="4" @tap="handleSave(false)" text="取消佩戴" v-else />
      </view>
    </view>
  </u-popup>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userUpdate } from '@/api/account'
  import { $collectionListByDigitalAvatar, $detailsByDigitalAvatar } from '@/api/avatar'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $topclick } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj
  const list = ref<any>([]) // 数字头像列表

  const popupContaoller = ref(false) // 弹窗控制器
  const selAvatar = ref<any>('') // 选中的徽章

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $collectionListByDigitalAvatar({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 选中头像 */
  const handleSelAvatar = (item: any) => {
    $detailsByDigitalAvatar(item.id).then((res: any) => {
      if (item.image === res.data.upgradeImage) res.data.isUpdate = true
      else res.data.isUpdate = false
      res.data.image = item.image
      selAvatar.value = res.data
      popupContaoller.value = true
    })
  }

  /* 关闭弹窗 */
  const handleClose = () => {
    selAvatar.value = ''
    popupContaoller.value = false
  }

  /* 更换头像 */
  const handleSave = (flag: boolean) => {
    uni.showLoading()

    $userUpdate({
      id: userInfo.value.id,
      avatar: flag ? selAvatar.value.image : '',
    }).then((res: any) => {
      uni.hideLoading()

      globalStore.handleRefreshUserInfo()

      handleClose()

      uni.$emit('updateAbatar')

      $back()
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/avatarBg.webp');

    .avatarItem {
      background-image: url($icon + 'background/avatarWall2.webp');

      &:nth-child(4n) {
        margin-right: 0;
      }
    }
  }

  .avatarDetail {
    background-image: url($icon + 'background/avatarDetail.webp');
  }
</style>
