<template>
  <!-- 导航栏 -->
  <u-navbar class="navbar" title="登录" :autoBack="true" @leftClick="$back" fixed placeholder />

  <!-- logo和标题 -->
  <image
    class="m-auto mb-2.5 mt-[140rpx] block h-[160rpx] w-[160rpx] rounded-full bg-purple"
    :src="$picFormat(wechatInfo.wechatLogo)"
    mode="aspectFill"
    webp />
  <view class="mb-[160rpx] text-center font-Medium text-lg font-medium leading-[50rpx] text-gray">{{
    wechatInfo.wechatName
  }}</view>

  <!-- 协议 -->
  <view class="m-auto mb-5 flex w-[560rpx] items-start justify-start" @tap="agreeFlag = !agreeFlag">
    <image
      class="mt-[7rpx] block h-4 w-4 shrink-0"
      :src="$iconFormat('icon/radioOff.svg')"
      mode="scaleToFill"
      v-if="!agreeFlag" />
    <image class="mt-[7rpx] block h-4 w-4 shrink-0" :src="$iconFormat('icon/radioOn.svg')" mode="scaleToFill" v-else />

    <view class="ml-[18rpx]">
      <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-gray">您已阅读并同意</text>
      <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-blue" @tap.stop="handleLinkAgreement(1)"
        >《用户协议》</text
      >
      <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-blue" @tap.stop="handleLinkAgreement(2)"
        >《隐私协议》</text
      >
      <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-blue" @tap.stop="handleLinkAgreement(4)"
        >《充值及虚拟产品协议》</text
      >
    </view>
  </view>

  <!-- 登录按钮 -->
  <customBtn class="m-auto" @handleAgree="handleWechatLogin" agree text="手机号快捷登录" v-if="canUse" />
  <customBtn class="m-auto" @tap="handleWechatLogin" text="手机号快捷登录" v-else />

  <!-- 微信授权弹窗 -->
  <u-modal class="authModel" :show="authModalController" width="600rpx">
    <view class="flex flex-col items-center justify-start">
      <u-icon name="checkmark-circle-fill" color="#07C160" size="100rpx"></u-icon>
      <view class="mt-[28rpx] w-full text-center font-Medium text-[34rpx] font-medium leading-[48rpx] text-black"
        >用户信息获取成功</view
      >
      <view
        class="' mt-[16rpx] w-full text-center font-Regular text-[34rpx] font-normal leading-[48rpx] text-black opacity-50"
        >授权获取你的手机号</view
      >
    </view>

    <template #confirmButton>
      <u-line color="rgb(0 0 0 / 10%)"></u-line>
      <u-button class="modelBtn" @tap="authModalController = false" color="#000000" plain text="拒绝"></u-button>
      <u-line color="rgb(0 0 0 / 10%)" direction="col"></u-line>
      <u-button
        class="modelBtn"
        @getphonenumber="handleAuthPhone"
        color="#9C75D3"
        openType="getPhoneNumber"
        plain
        text="允许"></u-button>
    </template>
  </u-modal>

  <u-popup
    :closeOnClickOverlay="false"
    :show="nameEditController"
    @close="nameEditController = false"
    bgColor="#302A45"
    round="40rpx">
    <view
      class="mb-[120rpx] w-full pt-[30rpx] text-center font-Medium text-[36rpx] font-medium leading-[50rpx] text-w-100"
      >请设置昵称</view
    >

    <u-form class="nameForm" :model="formData" :rules="rules" ref="formRef">
      <u-form-item class="formItem" prop="userName">
        <view class="h-[100rpx] w-[640rpx] rounded-full bg-w-10">
          <u-input
            class="nameInput"
            :cursorSpacing="10"
            border="none"
            clearable
            inputAlign="center"
            maxlength="15"
            placeholder="2-15个字符 支持中英文"
            shape="circle"
            v-model="formData.userName"></u-input>
        </view>
      </u-form-item>
    </u-form>

    <view class="m-auto mb-[90rpx] h-fit w-fit">
      <customBtn @tap="handleSaveUserInfo" text="完成" />
    </view>
  </u-popup>

  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $loginByWechat, $userUpdate } from '@/api/account'
  import { $getWechatSetting } from '@/api/base'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $push, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()

  const wechatInfo = ref<any>('') // 微信小程序信息
  const agreeFlag = ref<boolean>(false) // 是否同意协议
  const authModalController = ref<boolean>(false) // 微信授权弹窗控制器
  const wxCode = ref<string>() // login code

  const nameEditController = ref(false) // 昵称修改弹窗控制器
  const rules = ref({
    userName: {
      type: 'string',
      required: true,
      message: '请输入2-15位昵称',
      trigger: ['blur', 'change'],
      min: 2,
      max: 15,
    },
  })
  const formRef = ref()
  const formData = ref({
    userName: '',
  })

  const canUse = ref(false)

  onLoad(() => {
    getWechatSetting()

    try {
      wx.getPrivacySetting({
        success: (res: any) => {
          canUse.value = true
        },
        fail: () => {
          canUse.value = false
        },
      })
    } catch (error) {
      canUse.value = false
    }
  })

  /* 获取小程序基本信息 */
  const getWechatSetting = () => {
    $getWechatSetting().then((res: any) => {
      wechatInfo.value = res.data
    })
  }

  /* 打开协议弹窗 */
  const handleLinkAgreement = (type: number) => {
    $push({ name: 'Agreement', params: { type } })
  }

  /* 微信登录 */
  const handleWechatLogin = () => {
    if (agreeFlag.value) {
      uni.login({
        provider: 'weixin',
        success: (loginRes: any) => {
          wxCode.value = loginRes.code

          authModalController.value = true
        },
      })
    } else {
      $toast(app, '请先阅读并同意《用户协议》和《隐私政策》')
    }
  }

  /* 手机号授权 */
  const handleAuthPhone = (e: any) => {
    $loading()

    switch (e.detail.errMsg) {
      case 'getPhoneNumber:ok':
        // uni.hideLoading()
        // $push({ name: 'Register' })
        // code: e.detail.code, encryptedData: e.detail.encryptedData, iv: e.detail.iv
        $loginByWechat({ code: wxCode.value, phoneCode: e.detail.code }).then(async (res: any) => {
          authModalController.value = false

          uni.setStorageSync('token', res.token)
          globalStore.token = res.token

          await globalStore.handleRefreshUserInfo()

          uni.hideLoading()

          if (globalStore.userInfo.fistLogin === '0') nameEditController.value = true
          else $back()
        })
        break
      case 'getPhoneNumber:fail user deny':
        authModalController.value = false
        uni.hideLoading()
        $toast(app, '已拒绝手机号授权')
        break
      default:
        uni.hideLoading()
        $toast(app, '手机号授权失败，请重试')
        break
    }
  }

  /* 保存 */
  const handleSaveUserInfo = () => {
    formRef.value.validate().then((res: any) => {
      $loading()

      $userUpdate({
        id: globalStore.userInfo.id,
        name: formData.value.userName,
        fistLogin: '1',
      }).then((res: any) => {
        uni.hideLoading()

        globalStore.handleRefreshUserInfo()

        $back()
      })
    })
  }
</script>

<style>
  page {
    background: #fff;
  }
</style>

<style lang="scss" scoped>
  .authModel {
    &:deep(.u-modal) {
      .u-modal__button-group--confirm-button {
        @apply w-full  p-0;

        & > view {
          @apply flex w-full flex-row flex-wrap items-center justify-between;

          .modelBtn {
            @apply h-[104rpx] border-0 #{!important};

            width: calc((100% - 1px) / 2) !important;

            .u-button__text {
              @apply font-Medium text-[34rpx] font-medium #{!important};
            }
          }
        }
      }
    }
  }

  .nameForm {
    width: 640rpx;
    height: 100rpx;
    margin: 0 auto 132rpx;

    .formItem {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 0 !important;
      margin: 0 !important;

      &:deep(.u-form-item__body) {
        width: 100%;
        height: 100%;
        padding: 0;
      }

      &:deep(.u-form-item__body__right__message) {
        position: absolute;
        top: calc(100% + 20rpx);
        left: 0;
        width: 100%;
        margin: 0 !important;
        text-align: center;
      }
    }

    .nameInput {
      width: 100%;
      height: 100%;
      padding: 0 20rpx !important;

      &:deep(.u-input__content__field-wrapper__field) {
        font-family: PingFangSC-Regular, 'PingFang SC' !important;
        font-size: 30rpx !important;
        font-weight: 400 !important;
        color: #fff !important;
      }

      &:deep(.input-placeholder) {
        color: rgb(255 255 255 / 40%) !important;
      }
    }
  }
</style>
