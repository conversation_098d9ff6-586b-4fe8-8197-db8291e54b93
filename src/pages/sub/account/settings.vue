<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" title="提醒小助手" :titleStyle="titleStyle1" @leftClick="$back" bgColor="#1e1733" leftIconColor="#FFFFFF" placeholder />

    <scroll-view class="flex-1 w-full" scrollY>
      <view class="px-6 py-4">
        <!-- 功能说明 -->
        <view class="mb-6 rounded-[24rpx] bg-w-10 backdrop-blur-[5rpx] p-5">
          <view class="flex items-center mb-3">
            <text class="fas fa-clock mr-3" style="font-size: 36rpx; color: #fff"></text>
            <view class="font-Medium text-[30rpx] font-medium text-w-100">提醒管理</view>
          </view>
          <view class="font-Regular text-[26rpx] text-w-80 leading-[36rpx]">设置您关心的重要提醒事项，主页上方显示这些提醒</view>
        </view>

        <!-- 倒计时项目列表 -->
        <view class="space-y-4">
          <view class="rounded-[24rpx] bg-w-10 backdrop-blur-[5rpx] overflow-hidden" v-for="(type, index) in countdownTypes" :key="type.key">
            <!-- 主要设置区域 -->
            <view class="p-5">
              <!-- 标题和开关 -->
              <view class="flex items-center justify-between mb-4">
                <view class="flex items-center flex-1">
                  <view class="w-[48rpx] h-[48rpx] rounded-[12rpx] bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center mr-4">
                    <text class="fas" :class="type.iconClass" style="font-size: 28rpx; color: #fff"></text>
                  </view>
                  <view class="flex-1">
                    <view class="font-Medium text-[28rpx] font-medium text-w-100 mb-1">{{ type.title }}</view>
                    <view class="font-Regular text-[24rpx] text-w-70">{{ type.description }}</view>
                  </view>
                </view>
                <u-switch v-model="countdownSettings[type.key].enabled" @change="handleSwitchChange(type.key)" activeColor="#7C3AED" inactiveColor="rgba(255,255,255,0.2)" size="28" />
              </view>

              <!-- 详细配置区域 -->
              <view v-if="countdownSettings[type.key].enabled" class="bg-w-5 rounded-[16rpx] p-4">
                <!-- 发薪日设置 -->
                <view v-if="type.type === 'salary'">
                  <view class="flex items-center justify-center">
                    <text class="font-Regular text-[26rpx] text-w-90 mr-3">每月</text>
                    <view class="w-[120rpx] h-[60rpx] bg-w-10 rounded-[12rpx] flex items-center justify-center">
                      <u-input v-model="countdownSettings[type.key].dayOfMonth" type="number" placeholder="25" class="text-center font-Medium text-[28rpx]" :border="false" @change="saveSettings" />
                    </view>
                    <text class="font-Regular text-[26rpx] text-w-90 ml-3">号发薪</text>
                  </view>
                </view>

                <!-- 日期时间设置 -->
                <view v-if="type.type === 'datetime'" class="text-center">
                  <u-datetime-picker v-model="countdownSettings[type.key].datetime" mode="datetime" @confirm="e => confirmDateTime(e, type.key)">
                    <view class="w-full h-[60rpx] bg-w-10 rounded-[12rpx] flex items-center justify-center">
                      <text class="font-Medium text-[26rpx] text-w-100">
                        {{ countdownSettings[type.key].datetime ? formatDateTime(countdownSettings[type.key].datetime) : '点击选择时间' }}
                      </text>
                    </view>
                  </u-datetime-picker>
                </view>

                <!-- 看剧基金设置 -->
                <view v-if="type.type === 'fund'" class="space-y-3">
                  <view class="flex items-center">
                    <text class="font-Regular text-[26rpx] text-w-90 w-[140rpx]">当前金额</text>
                    <view class="flex-1 h-[60rpx] bg-w-10 rounded-[12rpx] flex items-center px-3">
                      <text class="font-Regular text-[24rpx] text-w-70 mr-2">¥</text>
                      <u-input v-model="countdownSettings[type.key].current" type="number" placeholder="0" class="flex-1 font-Medium text-[26rpx]" :border="false" @change="saveSettings" />
                    </view>
                  </view>
                  <view class="flex items-center">
                    <text class="font-Regular text-[26rpx] text-w-90 w-[140rpx]">目标金额</text>
                    <view class="flex-1 h-[60rpx] bg-w-10 rounded-[12rpx] flex items-center px-3">
                      <text class="font-Regular text-[24rpx] text-w-70 mr-2">¥</text>
                      <u-input v-model="countdownSettings[type.key].target" type="number" placeholder="2000" class="flex-1 font-Medium text-[26rpx]" :border="false" @change="saveSettings" />
                    </view>
                  </view>
                  <!-- 进度显示 -->
                  <view class="mt-3">
                    <view class="flex items-center justify-between mb-2">
                      <text class="font-Regular text-[22rpx] text-w-70">完成进度</text>
                      <text class="font-Medium text-[22rpx] text-w-90">{{ fundProgress }}%</text>
                    </view>
                    <view class="w-full h-[8rpx] bg-w-20 rounded-[4rpx] overflow-hidden">
                      <view class="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-[4rpx]" :style="{ width: fundProgress + '%' }"></view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 使用说明 -->
        <view class="mt-8 mb-4 rounded-[20rpx] bg-w-5 p-4">
          <view class="flex items-center mb-3">
            <text class="fas fa-info-circle mr-2" style="font-size: 24rpx; color: #7c3aed"></text>
            <text class="font-Medium text-[24rpx] font-medium text-w-90">使用说明</text>
          </view>
          <view class="space-y-2">
            <view class="font-Regular text-[22rpx] text-w-70 leading-[32rpx]">• 最紧急的倒计时将在主页大卡片中显示</view>
            <view class="font-Regular text-[22rpx] text-w-70 leading-[32rpx]">• 其他启用的倒计时在小网格中显示</view>
            <view class="font-Regular text-[22rpx] text-w-70 leading-[32rpx]">• 倒计时按距离天数自动排序优先级</view>
          </view>
        </view>

        <!-- 底部操作提示 -->
        <view class="mt-4 mb-4 text-center">
          <view class="font-Regular text-[24rpx] text-w-60 leading-[34rpx]">设置完成后，返回主页查看倒计时显示</view>
        </view>
      </view>
    </scroll-view>

    <!-- toast提示 -->
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script lang="ts" setup>
import { useGlobalStore } from '@/stores/global'
import { $back } from '@/utils/methods'

const { titleStyle1 } = storeToRefs(useGlobalStore())
const uToast = ref()

onMounted(() => {
  uni.getSystemInfo({
    success: res => {
      console.log('系统信息:', res)
    }
  })
})

// 倒计时类型配置
const countdownTypes = ref([
  {
    key: 'salary',
    title: '发薪日倒计时',
    description: '设置每月发薪日期，提醒您工资到账时间',
    iconClass: 'fa-dollar-sign',
    type: 'salary'
  },
  {
    key: 'showTime',
    title: '演出倒计时',
    description: '设置即将观看的演出时间',
    iconClass: 'fa-clock',
    type: 'datetime'
  },
  {
    key: 'ticketTime',
    title: '开票提醒',
    description: '设置关注演出的开票时间，不错过抢票',
    iconClass: 'fa-ticket-alt',
    type: 'datetime'
  },
  {
    key: 'nextShow',
    title: '下场演出',
    description: '设置计划观看的下一场演出时间',
    iconClass: 'fa-calendar-alt',
    type: 'datetime'
  },
  {
    key: 'fund',
    title: '看剧基金',
    description: '设置看剧基金目标，管理您的观剧预算',
    iconClass: 'fa-piggy-bank',
    type: 'fund'
  }
])

// 倒计时设置
const countdownSettings = ref({
  salary: { enabled: false, dayOfMonth: 25 },
  showTime: { enabled: false, datetime: '' },
  ticketTime: { enabled: false, datetime: '' },
  nextShow: { enabled: false, datetime: '' },
  fund: { enabled: false, current: 0, target: 2000 }
})

// 基金进度计算
const fundProgress = computed(() => {
  const current = Number(countdownSettings.value.fund.current) || 0
  const target = Number(countdownSettings.value.fund.target) || 2000
  return Math.min(Math.round((current / target) * 100), 100)
})

onLoad(() => {
  loadSettings()
})

/* 加载设置 */
const loadSettings = () => {
  const settings = uni.getStorageSync('countdownSettings')
  if (settings) {
    countdownSettings.value = { ...countdownSettings.value, ...settings }
  }
}

/* 开关变化处理 */
const handleSwitchChange = (key: string) => {
  saveSettings()
  uni.showToast({
    title: countdownSettings.value[key].enabled ? '已开启' : '已关闭',
    icon: 'none',
    duration: 1500
  })
}

/* 保存设置 */
const saveSettings = () => {
  uni.setStorageSync('countdownSettings', countdownSettings.value)
}

/* 确认选择时间 */
const confirmDateTime = (e: any, key: string) => {
  countdownSettings.value[key].datetime = e.value
  saveSettings()
}

/* 格式化日期时间 */
const formatDateTime = (datetime: string) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  return (
    date
      .toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
      .replace(/\//g, '月')
      .replace(' ', ' ') + '分'
  )
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.pageWrap {
  &:deep(.u-navbar__content) {
    .u-navbar__content__left {
      padding: 0 0 0 24rpx;
    }
  }

  &:deep(.u-input__content__field-wrapper__field) {
    text-align: center;
  }
}
</style>
