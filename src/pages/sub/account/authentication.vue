<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="安全验证"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#1e1733"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="w-full border-b border-solid border-w-10"></view>

    <view
      class="m-auto mb-[76rpx] mt-[180rpx] w-[548rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
      >为了保证您的账号安全，请验证身份。验证成功后进行下一步操作</view
    >

    <view class="mb-[60rpx] w-full text-center font-Regular text-[36rpx] font-normal leading-[50rpx] text-w-100">{{
      $hidePhone(userInfo.phone)
    }}</view>

    <u-form class="formWrap pl-3 pr-3" :model="formData" :rules="$authenticationRules" ref="form">
      <u-form-item class="codeItem" borderBottom prop="code" ref="item1">
        <u-input
          class="smallPatch"
          @change="handleCheckCode"
          border="none"
          inputAlign="center"
          maxlength="6"
          placeholder="请输入验证码"
          v-model="formData.code"></u-input>
      </u-form-item>
    </u-form>

    <customBtn class="m-auto mb-0 mt-0" :text="btnTxt" :type="isSend ? 2 : 1" @tap="getCode" />

    <view class="mt-[60rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
      >未收到验证码？</view
    >
  </view>

  <u-code :seconds="seconds" @change="codeChange" @end="end" ref="uCode"></u-code>

  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $sendUpdatePhoneCode, $sendUpdatePwdCode, $verifyPhoneCode, $verifyPwdCode } from '@/api/account'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $hidePhone, $push, $toast } from '@/utils/methods'
  import { $authenticationRules } from '@/utils/rules'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()
  const { userInfo, titleStyle1 } = storeToRefs(useGlobalStore())

  const pageType = ref('') // 页面类型

  const seconds = ref(60) // 验证码倒计时
  const isSend = ref(false) // 是否发送验证码
  const btnTxt = ref('发送验证码') // 按钮文案

  const formData = reactive({
    code: '',
  })

  onLoad((params: any) => {
    pageType.value = params.type
  })

  /*  倒计时按钮文案变化 */
  const codeChange = (text: string) => {
    btnTxt.value = text
  }

  /* 获取验证码 */
  const getCode = () => {
    if (app.$refs.uCode.canGetCode) {
      // 模拟向后端请求验证码
      uni.showLoading({ title: '正在获取验证码' })

      if (pageType.value == 'EditPwd') {
        $sendUpdatePwdCode().then((res: any) => {
          uni.hideLoading()
          // 这里此提示会被this.start()方法中的提示覆盖
          $toast(app, '验证码已发送')
          isSend.value = true
          // 通知验证码组件内部开始倒计时
          app.$refs.uCode.start()
        })
      } else if (pageType.value == 'EditPhone') {
        $sendUpdatePhoneCode().then((res: any) => {
          uni.hideLoading()
          // 这里此提示会被this.start()方法中的提示覆盖
          $toast(app, '验证码已发送')
          isSend.value = true
          // 通知验证码组件内部开始倒计时
          app.$refs.uCode.start()
        })
      }
    }
  }

  /* 倒计时结束 */
  const end = () => {
    isSend.value = false
    uni.$u.toast('倒计时结束')
  }

  /* 验证验证码 */
  const handleCheckCode = (val: string) => {
    if (val.length == 6) {
      if (pageType.value == 'EditPwd') {
        $verifyPwdCode(val).then((res: any) => {
          $push({ name: pageType.value, params: { smsCode: val } })
        })
      } else if (pageType.value == 'EditPhone') {
        $verifyPhoneCode(val).then((res: any) => {
          $push({ name: pageType.value, params: { smsCode: val } })
        })
      }
    }
  }
</script>

<style lang="scss" scoped></style>
