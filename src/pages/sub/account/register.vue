<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="注册"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />

    <image
      class="mb-[32rpx] block h-[564rpx] w-full"
      :src="$iconFormat('background/register.png')"
      mode="scaleToFill" />

    <u-form class="m-auto w-[532rpx]" :labelWidth="0" :model="formData" :rules="rules" ref="formRef">
      <u-form-item class="formItem rounded-full" prop="realName">
        <u-input border="none" clearable placeholder="请输入您的真实姓名" v-model="formData.realName"></u-input>
      </u-form-item>
      <u-form-item class="formItem rounded-full" maxlength="18" prop="IDCard">
        <u-input border="none" clearable placeholder="请输入您的身份证号" v-model="formData.IDCard"></u-input>
      </u-form-item>
      <u-form-item class="formItem rounded-full" prop="phone">
        <u-input
          border="none"
          clearable
          maxlength="11"
          placeholder="请输入您的手机号"
          v-model="formData.phone"></u-input>
      </u-form-item>
      <u-form-item class="formItem rounded-full" prop="code">
        <u-input border="none" clearable maxlength="6" placeholder="请输入验证码" v-model="formData.code"></u-input>

        <view class="sendTxt ml-[20rpx]" :class="{ isSend }" @tap="getCode">{{ btnTxt }}</view>
      </u-form-item>

      <view class="m-auto mt-[72rpx] h-fit w-fit">
        <customBtn text="注册" />
      </view>
      <view
        class="mt-[40rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#fefeff]"
        @click="$replaceAll({ name: 'Login' })"
        >已有账号?去登录</view
      >
    </u-form>
  </view>

  <u-code :seconds="seconds" @change="codeChange" @end="end" ref="uCode"></u-code>

  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $replaceAll, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()
  const { titleStyle1 } = storeToRefs(useGlobalStore())

  const rules = ref({
    realName: {
      type: 'string',
      required: true,
      message: '真实姓名不能为空',
      trigger: ['blur', 'change'],
    },
    IDCard: [
      { type: 'string', required: true, message: '身份证不能为空', trigger: ['blur', 'change'] },
      {
        validator: (rule: any, value: any, callback: any) => {
          return uni.$u.test.idCard(value)
        },
        message: '身份证不正确',
        trigger: ['change', 'blur'],
      },
    ],
    phone: [
      {
        type: 'string',
        required: true,
        message: '手机号码不能为空',
        trigger: ['blur', 'change'],
        min: 11,
        max: 11,
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          return uni.$u.test.mobile(value)
        },
        message: '手机号码不正确',
        trigger: ['change', 'blur'],
      },
    ],
    code: {
      type: 'string',
      required: true,
      message: '验证码不能为空',
      trigger: ['blur', 'change'],
      min: 6,
      max: 6,
    },
  })
  const formRef = ref()
  const formData = ref({
    realName: '',
    IDCard: '',
    phone: '',
    code: '',
  })

  const seconds = ref(60) // 验证码倒计时
  const isSend = ref(false) // 是否发送验证码
  const btnTxt = ref('发送验证码') // 按钮文案

  /*  倒计时按钮文案变化 */
  const codeChange = (text: string) => {
    btnTxt.value = text
  }

  /* 获取验证码 */
  const getCode = () => {
    if (app.$refs.uCode.canGetCode) {
      // 模拟向后端请求验证码
      uni.showLoading({ title: '正在获取验证码' })

      setTimeout(() => {
        uni.hideLoading()

        $toast(app, '验证码已发送')
        isSend.value = true
        app.$refs.uCode.start()
      }, 1500)
    }
  }

  /* 倒计时结束 */
  const end = () => {
    isSend.value = false
    uni.$u.toast('倒计时结束')
  }
</script>

<style lang="scss" scoped>
  .formItem {
    position: relative;
    width: 100%;
    margin-bottom: 60rpx;

    :deep(.u-form-item__body) {
      height: 80rpx;
      padding: 0 30rpx;
      background: rgb(255 255 255 / 10%);
      backdrop-filter: blur(5px);
      border-radius: 80rpx;

      .u-form-item__body__right {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &__content__slot {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          .sendTxt {
            font-family: PingFangSC-Medium, 'PingFang SC';
            font-size: 30rpx !important;
            font-weight: 500;
            line-height: 42rpx !important;
            color: #fefeff !important;

            &.isSend {
              color: rgb(255 255 255 / 60%) !important;
            }
          }
        }

        .u-input {
          &__content {
            &__field-wrapper {
              &__field {
                height: 42rpx;
                font-family: PingFangSC-Medium, 'PingFang SC';
                font-size: 30rpx !important;
                font-weight: 500;
                line-height: 42rpx !important;
                color: #fff !important;
              }

              .input-placeholder {
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 30rpx;
                font-weight: 400;
                line-height: 42rpx;
                color: #fefeff !important;
              }
            }
          }
        }
      }
    }

    :deep(.u-form-item__body__right__message) {
      position: absolute;
      top: 100%;
      left: 0;
      padding: 0 30rpx;
      margin-top: 10rpx;
    }
  }
</style>
