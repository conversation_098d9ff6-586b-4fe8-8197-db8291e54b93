<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <image
      class="absolute left-0 top-0 block h-[400] w-full"
      :src="$iconFormat('background/history.webp')"
      mode="scaleToFill"
      webp />a
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="观演历史"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <!-- tab 切换 -->
    <view class="relative z-10 mt-5 flex w-full items-center justify-center">
      <view
        class="mr-2.5 flex h-[88rpx] w-[280rpx] items-center justify-center rounded-t-[30rpx] bg-[#463D6F]"
        :class="tabIndex == 0 ? 'bg-[#6C4FA6]' : ''"
        @tap="handlSwitchTab(0)">
        <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">待评价</text>
        <text class="ml-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">3</text>
      </view>
      <view
        class="flex h-[88rpx] w-[280rpx] items-center justify-center rounded-t-[30rpx] bg-[#463D6F]"
        :class="tabIndex == 1 ? 'bg-[#6C4FA6]' : ''"
        @tap="handlSwitchTab(1)">
        <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">已评价</text>
        <text class="ml-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">10</text>
      </view>
    </view>

    <mescroll-uni
      class="block h-0 w-screen grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="w-full bg-[#1F1933]"></view>
    </mescroll-uni>
  </view>
</template>

<script lang="ts" setup>
  import { $getJson } from '@/api/example'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $topclick } from '@/utils/methods'

  const { titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const tabIndex = ref(0) // tab index

  const mescrollObj = ref() // 滚动obj
  const list = ref([]) // 商品列表

  /* tab 切换 */
  const handlSwitchTab = (index: number) => {
    tabIndex.value = index
  }

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $getJson({ pageNum: mescroll.num, pageSize: mescroll.size })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }
</script>

<style lang="scss" scoped></style>
