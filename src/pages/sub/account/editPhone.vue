<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="修改手机号"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="mb-5 h-[1rpx] w-full shrink-0 bg-w-10"></view>

    <u-form class="formWrap w-full pl-3 pr-3" :model="formData" :rules="$authenticationRules" ref="formRef">
      <!-- 手机号 -->
      <u-form-item class="formItem borderPatch" prop="phone">
        <u-input
          class="smallPatch"
          border="none"
          clearable
          maxlength="11"
          placeholder="请输入新手机号"
          v-model="formData.phone"></u-input>

        <u-button class="getCodeBtn ml-2" :text="btnTxt" @tap="getCode" color="#9C75D3" plain shape="circle"></u-button>
      </u-form-item>

      <!-- 验证码 -->
      <u-form-item class="formItem borderPatch" prop="code">
        <u-input
          class="smallPatch"
          border="none"
          clearable
          placeholder="请输入验证码"
          v-model="formData.code"></u-input>
      </u-form-item>
    </u-form>

    <customBtn class="m-auto mt-0" @tap="handleSave" text="保存" />
  </view>

  <u-code :seconds="seconds" @change="codeChange" @end="end" ref="uCode"></u-code>

  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $updatePhone } from '@/api/account'
  import { $sendUpdateNewPhoneCode } from '@/api/account'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $replaceAll, $toast } from '@/utils/methods'
  import { $authenticationRules } from '@/utils/rules'

  const app: any = getCurrentInstance()?.proxy
  const { titleStyle1 } = storeToRefs(useGlobalStore())
  const globalStore = useGlobalStore()

  const seconds = ref(60) // 验证码倒计时
  const isSend = ref(false) // 是否发送验证码
  const btnTxt = ref('发送验证码') // 按钮文案

  const formRef = ref()

  /* 表单 */
  const formData = reactive({
    phone: '',
    code: '',
  })

  /*  倒计时按钮文案变化 */
  const codeChange = (text: string) => {
    btnTxt.value = text
  }

  /* 获取验证码 */
  const getCode = () => {
    if (app.$refs.uCode.canGetCode) {
      // 模拟向后端请求验证码
      uni.showLoading({ title: '正在获取验证码' })

      formRef.value.validateField('phone').then((res: any) => {
        $sendUpdateNewPhoneCode(formData.phone).then((res: any) => {
          uni.hideLoading()
          // 这里此提示会被this.start()方法中的提示覆盖
          $toast(app, '验证码已发送')
          isSend.value = true
          // 通知验证码组件内部开始倒计时
          app.$refs.uCode.start()
        })
      })
    }
  }

  /* 倒计时结束 */
  const end = () => {
    isSend.value = false
    uni.$u.toast('倒计时结束')
  }

  /* 保存修改密码 */
  const handleSave = () => {
    formRef.value.validate().then((res: any) => {
      $updatePhone({
        phone: formData.phone,
        code: formData.code,
      }).then((res: any) => {
        $toast(app, '修改成功')

        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('scanResult')
        globalStore.token = ''
        globalStore.userInfo = ''
        globalStore.scanResult = ''
        $replaceAll({ name: 'Login' })
      })
    })
  }
</script>

<style lang="scss" scoped>
  .formWrap {
    margin-bottom: 118rpx;

    &:deep(.u-form-item__body__right__message) {
      padding-bottom: 20rpx;
      margin-left: 0 !important;
    }
  }
</style>
