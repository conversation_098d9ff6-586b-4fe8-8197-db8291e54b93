<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" title="设置" :titleStyle="titleStyle1" @leftClick="$back" bgColor="#1e1733" leftIconColor="#FFFFFF" placeholder />

    <!-- 通知管理 -->
    <view class="mb-2.5 mt-2.5 w-full pl-[48rpx] font-Medium text-[26rpx] font-medium leading-[36rpx] text-w-60">通知管理</view>

    <view class="m-auto mb-5 w-[700rpx] rounded-[30rpx] bg-w-10 backdrop-blur-[5rpx]">
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify1.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">评论通知</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.commentNotify"></u-switch>
      </view>
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify2.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">点赞通知</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.wellNotify"></u-switch>
      </view>
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify3.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">提问通知</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.issueNotify"></u-switch>
      </view>
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify4.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">关注的账号更新通知</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.likeNotify"></u-switch>
      </view>
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify5.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">关注的账号群发通知</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.groupMessageNotify"></u-switch>
      </view>
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify6.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">关注的账号接待内容通知</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.welcomeNotify"></u-switch>
      </view>
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="$iconFormat(`notify/notify7.svg`)" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">消息提示</text>
        <u-switch
          class="shrink-0"
          :activeValue="1"
          :inactiveValue="0"
          @change="handleSwitch"
          activeColor="#4CD964"
          inactiveColor="rgb(120 120 128 / 16%)"
          size="27"
          space="2"
          v-model="notifySetting.messageNotify"></u-switch>
      </view>
    </view>

    <!-- 账号与安全 -->
    <view class="mb-2.5 mt-2.5 w-full pl-[48rpx] font-Medium text-[26rpx] font-medium leading-[36rpx] text-w-60">账号与安全</view>

    <view class="m-auto mb-5 w-[700rpx] rounded-[30rpx] bg-w-10 backdrop-blur-[5rpx]">
      <view class="flex h-[100rpx] w-full items-center justify-start pl-2.5 pr-2.5" :key="index" @tap="$push({ name: item.linkName, params: item.params })" v-for="(item, index) in accountSafe">
        <image class="mr-2.5 block h-4 w-4 shrink-0" :src="item.icon" mode="scaleToFill" />
        <text class="grow font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-100">{{ item.name }}</text>
        <text class="mr-2.5 shrink-0 font-Regular text-[30rpx] font-normal leading-[100rpx] text-w-60" v-if="index == 1">{{ $hidePhone(userInfo.phone) }}</text>
        <image class="block h-4 w-4 shrink-0" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
      </view>
    </view>

    <!-- 退出登录 -->
    <customBtn class="m-auto" @tap="handleLogout" text="退出登录" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
import { $userSetting, $userSettingUpdate } from '@/api/setting'
import customBtn from '@/components/Btn/CustomBtn.vue'
import network from '@/components/Network.vue'
import { useGlobalStore } from '@/stores/global'
import { $back, $hidePhone, $iconFormat, $push, $replaceAll } from '@/utils/methods'

const globalStore = useGlobalStore()
const { userInfo, titleStyle1 } = storeToRefs(useGlobalStore())

/* 通知列表 */
const notifySetting = ref<any>({
  commentNotify: 0,
  wellNotify: 0,
  issueNotify: 0,
  groupMessageNotify: 0,
  messageNotify: 0,
  welcomeNotify: 0,
  likeNotify: 0
})

/* 账号安全 */
const accountSafe = ref([
  {
    name: '账号密码',
    icon: $iconFormat('icon/pwd.svg'),
    linkName: 'Authentication',
    params: { type: 'EditPwd' }
  },
  {
    name: '手机号',
    icon: $iconFormat('icon/phone.svg'),
    linkName: 'Authentication',
    params: { type: 'EditPhone' }
  },
  { name: '注销账号', icon: $iconFormat('icon/destroy.svg'), linkName: 'Writeoff' }
])

/* 按钮自定义样式 */
const customStyle = ref({
  width: '560rpx',
  height: '116rpx',
  margin: 'auto',
  background: `url(${$iconFormat('btn/logout.webp')}) no-repeat`,
  backgroundSize: 'cover'
})

onLoad(() => {
  handleGetSetting()
})

const handleGetSetting = () => {
  $userSetting().then((res: any) => {
    notifySetting.value = res.data
  })
}

/* 通知开关 */
const handleSwitch = (e: any) => {
  $userSettingUpdate({
    id: notifySetting.value.id,
    userId: notifySetting.value.userId,
    commentNotify: notifySetting.value.commentNotify,
    wellNotify: notifySetting.value.wellNotify,
    issueNotify: notifySetting.value.issueNotify,
    groupMessageNotify: notifySetting.value.groupMessageNotify,
    messageNotify: notifySetting.value.messageNotify,
    welcomeNotify: notifySetting.value.welcomeNotify,
    likeNotify: notifySetting.value.likeNotify
  }).then((res: any) => {
    handleGetSetting()
  })
}

/* 退出登录 */
const handleLogout = () => {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('scanResult')
  globalStore.token = ''
  globalStore.userInfo = ''
  globalStore.scanResult = ''
  $replaceAll({ name: 'Login' })
}
</script>
