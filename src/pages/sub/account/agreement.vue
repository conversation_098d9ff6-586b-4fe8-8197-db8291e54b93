<template>
  <!-- 导航栏 -->
  <u-navbar class="navbar" :autoBack="true" :title="title" @leftClick="$back" fixed placeholder />

  <!-- 间隔 -->
  <view class="line h-[10rpx] w-full bg-[#F4F4F4]"></view>

  <!-- 协议内容 -->
  <view class="agreementContent w-full p-[24rpx] pb-[100rpx]">
    <u-parse :content="agreementDetail.body" :tagStyle="tagStyle"></u-parse>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $agreementByType } from '@/api/base'
  import network from '@/components/Network.vue'
  import { $back } from '@/utils/methods'

  const title = ref<string>('') // 导航标题
  const agreementDetail = ref<any>('') // 协议内容
  /* 富文本样式 */
  const tagStyle = reactive({
    p: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: #2F3032;line-height: 40rpx;',
    span: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: #2F3032;line-height: 40rpx;',
    div: 'font-size: 28rpx;font-family: PingFangSC-Regular, PingFang SC;font-weight: 400;color: #2F3032;line-height: 40rpx;',
  })

  onLoad((params) => {
    switch (params?.type) {
      case '1':
        title.value = '《用户协议》'
        break
      case '2':
        title.value = '《隐私协议》'
        break
      case '3':
        title.value = '《用户注销协议》'
        break
      case '4':
        title.value = '《虚拟商品交易协议》'
        break
    }

    $agreementByType(params?.type - 0).then((res: any) => {
      agreementDetail.value = res.data
    })
  })
</script>

<style>
  page {
    background: #fff;
  }
</style>
