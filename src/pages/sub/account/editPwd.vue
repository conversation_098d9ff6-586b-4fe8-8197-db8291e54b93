<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="设置密码"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="h-[1rpx] w-full shrink-0 bg-w-10"></view>

    <!-- 设置密码/修改密码 -->
    <view
      class="m-auto mb-[12rpx] mt-[38rpx] w-[702rpx] font-Medium text-[26rpx] font-medium leading-[36rpx] text-w-60"
      >{{ type == 0 ? '设置密码' : '修改密码' }}</view
    >

    <u-form class="formWrap w-full pl-3 pr-3" :model="formData" :rules="$pwdRules" ref="formRef">
      <!-- 新密码 -->
      <u-form-item class="formItem borderPatch" prop="oldPwd" v-if="type == 1">
        <u-input
          class="smallPatch"
          border="none"
          clearable
          maxlength="16"
          password
          placeholder="请输入旧密码"
          v-model="formData.oldPwd"></u-input>
      </u-form-item>

      <!-- 新密码 -->
      <u-form-item class="formItem borderPatch" prop="newPwd">
        <u-input
          class="smallPatch"
          border="none"
          clearable
          maxlength="16"
          password
          placeholder="请输入账号密码"
          v-model="formData.newPwd"></u-input>
      </u-form-item>

      <!-- 确认密码 -->
      <u-form-item class="formItem borderPatch" prop="confrimPwd">
        <u-input
          class="smallPatch"
          border="none"
          clearable
          password
          placeholder="请再次确认账号密码"
          v-model="formData.confrimPwd"></u-input>
      </u-form-item>
    </u-form>

    <customBtn class="m-auto mt-0" @tap="handleSave" text="保存" />
  </view>

  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $updatePwdByPhone } from '@/api/account'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $replaceAll, $toast } from '@/utils/methods'
  import { $pwdRules } from '@/utils/rules'

  const app: any = getCurrentInstance()?.proxy
  const globalStore = useGlobalStore()
  const { titleStyle1 } = storeToRefs(useGlobalStore())

  const type = ref(0) // 修改密码类型
  const formRef = ref()
  /* 表单 */
  const formData = reactive({
    oldPwd: '',
    newPwd: '',
    confrimPwd: '',
    code: '',
  })

  onLoad((params: any) => {
    formData.code = params.smsCode
  })

  watch(
    () => formData.newPwd,
    (val: any) => {
      $pwdRules.confrimPwd.validator = (rule: any, value: any, callback: any) => {
        return val === formData.confrimPwd
      }
    }
  )

  /* 保存修改密码 */
  const handleSave = () => {
    formRef.value.validate().then((res: any) => {
      $updatePwdByPhone({
        newPassword: formData.newPwd,
        code: formData.code,
      }).then((res: any) => {
        $toast(app, '修改成功')

        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('scanResult')
        globalStore.token = ''
        globalStore.userInfo = ''
        globalStore.scanResult = ''
        $replaceAll({ name: 'Login' })
      })
    })
  }
</script>

<style lang="scss" scoped>
  .formWrap {
    margin-bottom: 118rpx;

    &:deep(.u-form-item__body__right__message) {
      padding-bottom: 20rpx;
      margin-left: 0 !important;
    }
  }
</style>
