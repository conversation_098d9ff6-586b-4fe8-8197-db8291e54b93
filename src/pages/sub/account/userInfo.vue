<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="#1e1733" leftIconColor="#FFFFFF" placeholder />

    <u-form
      class="formWrap mt-1.5 w-full pl-3 pr-3"
      :labelWidth="0"
      :model="formData"
      :rules="$editInfoRules"
      ref="formRef">
      <!-- 头像 -->
      <u-form-item class="formItem noPadding mbPatch" prop="avatar">
        <view class="relative flex h-[144rpx] w-[144rpx]" @tap="handleSelAvatar">
          <u-avatar :defaultUrl="$iconFormat('avatar.jpg')" :src="$picFormat(formData.avatar)" size="144rpx"></u-avatar>
          <view class="absolute left-0 top-0 z-10 h-full w-full rounded-full bg-b-40"></view>
          <image
            class="absolute bottom-0 left-0 right-0 top-0 z-20 m-auto block h-6 w-6"
            :src="$iconFormat('icon/camera.svg')"
            mode="scaleToFill" />
        </view>

        <view
          class="ml-[60rpx] h-6 w-[180rpx] rounded-full bg-w-10 text-center font-Regular text-[26rpx] font-normal leading-6 text-w-60"
          @tap="$push({ name: 'AvatarSel' })"
          >使用数字头像</view
        >
      </u-form-item>
      <!-- 用户名 -->
      <u-form-item class="formItem borderPatch" label="用户名" prop="name">
        <u-input
          border="none"
          clearable
          maxlength="15"
          placeholder="请输入您的用户名"
          v-model="formData.name"></u-input>
      </u-form-item>
      <!-- 性别 -->
      <u-form-item class="formItem borderPatch" label="性&nbsp;&nbsp;&nbsp;&nbsp;别" labelWidth="4em" prop="sex">
        <view class="flex items-center justify-start" @tap="formData.sex = 0">
          <image
            class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
            :src="$iconFormat('icon/radioOn.svg')"
            mode="scaleToFill"
            v-if="formData.sex == 0" />
          <image
            class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
            :src="$iconFormat('icon/radioOff.svg')"
            mode="scaleToFill"
            v-else />

          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">男</text>
        </view>
        <view class="ml-5 flex items-center justify-start" @tap="formData.sex = 1">
          <image
            class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
            :src="$iconFormat('icon/radioOn.svg')"
            mode="scaleToFill"
            v-if="formData.sex == 1" />
          <image
            class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
            :src="$iconFormat('icon/radioOff.svg')"
            mode="scaleToFill"
            v-else />

          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">女</text>
        </view>
        <view class="ml-5 flex items-center justify-start" @tap="formData.sex = 2">
          <image
            class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
            :src="$iconFormat('icon/radioOn.svg')"
            mode="scaleToFill"
            v-if="formData.sex == 2" />
          <image
            class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
            :src="$iconFormat('icon/radioOff.svg')"
            mode="scaleToFill"
            v-else />

          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">不公开</text>
        </view>
      </u-form-item>
      <!-- 个性签名 -->
      <u-form-item class="formItem flexpatch borderPatch" label="个性签名" prop="personalizedSignature">
        <u-textarea
          class="signatureBox"
          autoHeight
          border="none"
          placeholder="请简单介绍一下您自己"
          v-model="formData.personalizedSignature"></u-textarea>
      </u-form-item>

      <customBtn class="saveBtn m-auto mt-[120rpx]" @tap="handleSaveUserInfo" text="保存" />
    </u-form>

    <ksp-cropper
      :height="200"
      :maxHeight="1024"
      :maxWidth="1024"
      :url="selAvatar"
      :width="200"
      @cancel="handleCancel"
      @ok="handleCropAvartar"
      mode="fixed"></ksp-cropper>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $userUpdate } from '@/api/account'
  import { $upload } from '@/api/base'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $push } from '@/utils/methods'
  import { $editInfoRules } from '@/utils/rules'

  const globalStore = useGlobalStore()
  const { userInfo } = storeToRefs(useGlobalStore())

  const formRef = ref()
  const formData = reactive({
    avatar: userInfo.value.avatar,
    name: userInfo.value.name,
    sex: userInfo.value.sex,
    personalizedSignature: userInfo.value.personalizedSignature,
  })
  const selAvatar = ref('') // 选中要裁剪的头像

  onLoad(async () => {
    init()

    uni.$on('updateAbatar', async () => {
      await globalStore.handleRefreshUserInfo()

      formData.avatar = userInfo.value.avatar
    })
  })

  const init = async () => {
    await globalStore.handleRefreshUserInfo()

    formData.avatar = userInfo.value.avatar
    formData.name = userInfo.value.name
    formData.sex = userInfo.value.sex
    formData.personalizedSignature = userInfo.value.personalizedSignature
  }

  /* 选择要裁剪的头像 */
  const handleSelAvatar = () => {
    uni.chooseMedia({
      count: 1,
      mediaType: ['image'],
      success: (res) => {
        selAvatar.value = res.tempFiles[0].tempFilePath
      },
    })
  }

  /* 裁剪头像 */
  const handleCropAvartar = (e: any) => {
    selAvatar.value = ''
    $upload({
      name: 'file',
      filePath: e.path,
    }).then((res: any) => {
      uni.hideLoading()
      formData.avatar = res.fileName
    })
  }

  /* 取消裁剪头像 */
  const handleCancel = () => {
    selAvatar.value = ''
  }

  /* 保存 */
  const handleSaveUserInfo = () => {
    formRef.value.validate().then((res: any) => {
      $loading()

      $userUpdate({
        id: userInfo.value.id,
        avatar: formData.avatar,
        name: formData.name,
        sex: formData.sex,
        personalizedSignature: formData.personalizedSignature,
      }).then((res: any) => {
        uni.hideLoading()

        globalStore.handleRefreshUserInfo()

        $back()
      })
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    .formWrap {
      .signatureBox {
        &:deep(.u-textarea__field) {
          resize: none;
        }
      }

      .saveBtn {
        margin-top: 120rpx;
      }
    }
  }
</style>
