<template>
  <view class="pageWrap min-h-screen w-screen bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="注销账号"
      :titleStyle="titleStyle1"
      @leftClick="handleLogout"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="h-[1rpx] w-full shrink-0 bg-w-10"></view>

    <template v-if="step == 0">
      <image
        class="m-auto mb-2.5 mt-[78rpx] block h-[160rpx] w-[160rpx]"
        :src="$iconFormat('icon/warn.svg')"
        mode="scaleToFill" />
      <view class="mb-2.5 w-full text-center font-Regular text-[36rpx] font-normal leading-[50rpx] text-w-100"
        >注销账号</view
      >
      <view class="m-auto mb-5 w-[600rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        >注销账号，将清除所有您在平台留存的信息且无法找回，具体包括：</view
      >
      <view class="m-auto mb-[120rpx] w-[702rpx] rounded-[20rpx] bg-w-10 p-2.5">
        <view class="w-full font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60"
          >· 昵称/头像等个人资料</view
        >
        <view class="w-full font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60"
          >· 已领取的数字商品，包括数字头像、纪念徽章、等级勋章、电子票</view
        >
        <view class="w-full font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60"
          >· 所有剧场/剧目的评价信息及评论信息</view
        >
        <view class="w-full font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60">· 已观看剧目表演信息</view>
        <view class="w-full font-Regular text-[28rpx] font-normal leading-[60rpx] text-w-60"
          >· 以及所有使用本平台的相关记录信息</view
        >
      </view>

      <view class="mb-2.5 w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        >点击【下一步】及代表您已经同意<text
          class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-blue"
          @tap.stop="handleLinkAgreement(3)"
          >《用户注销协议》</text
        ></view
      >

      <!-- <customBtn class="m-auto mt-0" @tap="handleNext" text="下一步" /> -->
      <customBtn class="m-auto mt-0" @tap="sureModalController = true" text="确定注销" />
    </template>

    <template v-else-if="step == 1">
      <view
        class="m-auto mb-[76rpx] mt-[180rpx] w-[548rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
        >为了保证您的账号安全，请验证身份。验证成功后进行下一步操作</view
      >

      <view class="mb-[60rpx] w-full text-center font-Regular text-[36rpx] font-normal leading-[50rpx] text-w-100">{{
        $hidePhone(userInfo.phone)
      }}</view>

      <u-form class="formWrap pl-3 pr-3" :model="formData" :rules="$authenticationRules" ref="form">
        <u-form-item class="codeItem" borderBottom prop="code" ref="item1">
          <u-input
            class="smallPatch"
            border="none"
            inputAlign="center"
            maxlength="6"
            placeholder="请输入验证码"
            v-model="formData.code"></u-input>

          <view
            class="absolute bottom-0 right-[36rpx] top-0 z-10 m-auto h-fit text-[26rpx] leading-[36rpx] text-w-60"
            @tap.stop="getCode"
            >{{ btnTxt }}</view
          >
        </u-form-item>
      </u-form>

      <customBtn class="m-auto mb-0 mt-0" @tap="handleCheckCode" text="确定注销" />

      <!-- <view class="mt-[60rpx] w-full text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">未收到验证码？</view> -->
    </template>

    <template v-if="step == 2">
      <view class="mb-5 mt-[176rpx] w-full text-center font-Medium text-[30rpx] font-medium leading-[42rpx] text-w-100"
        >注销账号操作成功</view
      >
      <view
        class="m-auto mb-[300rpx] w-[560rpx] text-center font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        >您可以在7天内返回平台取消注销，平台依然保留您的使用信息</view
      >

      <customBtn class="m-auto mt-0" @tap="handleLogout" text="返回" />
    </template>
  </view>

  <u-code :seconds="seconds" @change="codeChange" @end="end" ref="uCode"></u-code>

  <u-modal
    class="modalWrap"
    :show="sureModalController"
    @cancel="sureModalController = false"
    @confirm="handleCheckCode"
    content="你确定要注销账号吗？"
    showCancelButton
    width="400rpx"></u-modal>
  <u-toast ref="uToast"></u-toast>
  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $logout, $sendLogoutCode } from '@/api/account'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $hidePhone, $iconFormat, $push, $replaceAll, $toast } from '@/utils/methods'
  import { $authenticationRules } from '@/utils/rules'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle1 } = storeToRefs(useGlobalStore())
  const globalStore = useGlobalStore()

  const step = ref(0) // 页面层级
  const sureModalController = ref(false)

  const seconds = ref(60) // 验证码倒计时
  const isSend = ref(false) // 是否发送验证码
  const btnTxt = ref('发送验证码') // 按钮文案
  const formData = reactive({
    code: '',
  })

  /* 打开协议弹窗 */
  const handleLinkAgreement = (type: number) => {
    $push({ name: 'Agreement', params: { type } })
  }

  /* 下一步 */
  const handleNext = () => {
    step.value++
  }

  /*  倒计时按钮文案变化 */
  const codeChange = (text: string) => {
    btnTxt.value = text
  }

  /* 获取验证码 */
  const getCode = () => {
    if (app.$refs.uCode.canGetCode) {
      // 模拟向后端请求验证码
      uni.showLoading({ title: '正在获取验证码' })

      $sendLogoutCode().then((res: any) => {
        uni.hideLoading()
        // 这里此提示会被this.start()方法中的提示覆盖
        $toast(app, '验证码已发送')
        isSend.value = true
        // 通知验证码组件内部开始倒计时
        app.$refs.uCode.start()
      })
    }
  }

  /* 倒计时结束 */
  const end = () => {
    isSend.value = false
    uni.$u.toast('倒计时结束')
  }

  /* 验证验证码 */
  const handleCheckCode = () => {
    $logout('').then((res: any) => {
      step.value = 2
      sureModalController.value = false
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('scanResult')
      globalStore.token = ''
      globalStore.userInfo = ''
      globalStore.scanResult = ''
    })
    // if (formData.code.length == 6) {
    //   $logout(formData.code).then((res: any) => {
    //     step.value++
    //   })
    // }
  }

  /* 退出登录 */
  const handleLogout = () => {
    $replaceAll({ name: 'Login' })
  }
</script>

<style lang="scss" scoped>
  .modalWrap {
    &:deep(.u-modal__button-group__wrapper) {
      height: 70rpx;
    }
  }
</style>
