<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-deepbg">
    <image
      class="absolute left-0 top-0 block h-[400] w-full"
      :src="$iconFormat('background/history.webp')"
      mode="scaleToFill"
      webp />
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="观演历史"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />

    <!-- tab 切换 -->
    <view class="relative z-10 flex w-full items-center justify-center pt-5">
      <view
        class="mr-2.5 flex h-[88rpx] w-[280rpx] items-center justify-center rounded-t-[30rpx] bg-[#463D6F]"
        :class="tabIndex == 0 ? 'bg-[#6C4FA6]' : ''"
        @tap="handlSwitchTab(0)">
        <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">待评价</text>
        <text class="ml-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{ waitNum }}</text>
      </view>
      <view
        class="flex h-[88rpx] w-[280rpx] items-center justify-center rounded-t-[30rpx] bg-[#463D6F]"
        :class="tabIndex == 1 ? 'bg-[#6C4FA6]' : ''"
        @tap="handlSwitchTab(1)">
        <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">已评价</text>
        <text class="ml-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{ doneNum }}</text>
      </view>
    </view>

    <view class="wall relative z-10 h-0 w-full grow overflow-hidden rounded-t-[30rpx] bg-deepbg">
      <historyWaitList v-if="tabIndex === 0" />
      <historyDoneList v-if="tabIndex === 1" />
    </view>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import historyDoneList from './components/HistoryDoneList.vue'
  import historyWaitList from './components/HistoryWaitList.vue'
  import { $commentListByUserCount } from '@/api/comment'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat } from '@/utils/methods'

  const { userInfo, titleStyle2 } = storeToRefs(useGlobalStore())

  const tabIndex = ref(0) // tab index

  const waitNum = ref(0) // 待评价剧目数量
  const doneNum = ref(0) // 已评价剧目数量

  onLoad(() => {
    uni.$on('updateHistory', handleInitNum)

    handleInitNum()
  })

  /* tab 切换 */
  const handlSwitchTab = (index: number) => {
    tabIndex.value = index
  }

  /* 获取评论数量统计 */
  const handleInitNum = () => {
    /* 待评价数量 */
    $commentListByUserCount({ userId: userInfo.value.id, commentFlag: 0 }).then((res: any) => {
      waitNum.value = res.data
    })
    /* 已评价数量 */
    $commentListByUserCount({ userId: userInfo.value.id, commentFlag: 1 }).then((res: any) => {
      doneNum.value = res.data
    })
  }
</script>

<style lang="scss" scoped></style>
