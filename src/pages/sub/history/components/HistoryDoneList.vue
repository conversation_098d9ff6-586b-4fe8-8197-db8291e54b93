<template>
  <mescroll-uni
    class="block h-full w-full"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt3"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="w-full overflow-hidden rounded-t-[30rpx]" v-if="list && list.length">
      <view
        class="mb-[10rpx] w-full bg-[#1F1933] pb-3 pl-3 pr-3 pt-2.5 first:pt-[40rpx] last:mb-0"
        :key="i.id"
        v-for="i in list">
        <!-- 剧目信息 -->
        <view
          class="mb-2.5 flex w-full items-center justify-start rounded-[20rpx] bg-deepbg2 p-1 pr-[12rpx]"
          @tap="$push({ name: 'RepertoireDetail', params: { id: i.repertoireId, bottom: 1 } })">
          <u-image
            class="shrink-0"
            :src="$picFormat(i.repertoireCoverPicture)"
            bgColor="transparent"
            height="80rpx"
            mode="aspectFill"
            radius="10rpx"
            width="80rpx"></u-image>

          <view class="ml-2.5 grow">
            <view class="mb-[4rpx] font-Medium text-[24rpx] font-medium leading-[34rpx] text-w-100">{{
              i.repertoireName
            }}</view>
            <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
              >演出时间:{{ i.startTime || '-' }} | 演出场地:{{ i.theaterName }}</view
            >
          </view>
        </view>

        <template v-if="i.deleted === 1 && i.status === 1">
          <template v-if="!i.isOpen">
            <!-- 剧目点赞 -->
            <view class="mb-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1" v-if="[0, 1].includes(i.repertoireKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="i.repertoireKudos === 1">
                  <image
                    class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                    :src="$iconFormat('icon/thumbsUp2.svg')"
                    mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                    >赞了 {{ i.repertoireName || '-' }}</view
                  >
                </template>
                <template v-else-if="i.repertoireKudos === 0">
                  <image
                    class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                    :src="$iconFormat('icon/thumbsUp4.svg')"
                    mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                    >踩了 {{ i.repertoireName || '-' }}</view
                  >
                </template>
              </view>
            </view>
            <view
              class="relative mb-2 line-clamp-2 w-fit break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
              >{{ i.text }}
            </view>
            <view
              class="mb-2.5 text-right font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-30"
              @tap="i.isOpen = true"
              >展开</view
            >
          </template>

          <template v-else>
            <!-- 剧目点赞 -->
            <view class="mb-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1" v-if="[0, 1].includes(i.repertoireKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="i.repertoireKudos === 1">
                  <image
                    class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                    :src="$iconFormat('icon/thumbsUp2.svg')"
                    mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                    >赞了 {{ i.repertoireName || '-' }}</view
                  >
                </template>
                <template v-else-if="i.repertoireKudos === 0">
                  <image
                    class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                    :src="$iconFormat('icon/thumbsUp4.svg')"
                    mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                    >踩了 {{ i.repertoireName || '-' }}</view
                  >
                </template>
              </view>
            </view>
            <u-parse
              class="break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
              :content="i.content"></u-parse>

            <view class="m-auto mb-3 mt-3 h-[2rpx] w-full bg-w-10"></view>

            <!-- 剧场点赞 -->
            <view
              class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1"
              v-if="[0, 1].includes(i.theaterKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="i.theaterKudos === 1">
                  <image
                    class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                    :src="$iconFormat('icon/thumbsUp2.svg')"
                    mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                    >赞了《{{ i.theaterName || '-' }}》</view
                  >
                </template>
                <template v-else-if="i.theaterKudos === 0">
                  <image
                    class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0"
                    :src="$iconFormat('icon/thumbsUp4.svg')"
                    mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40"
                    >踩了《{{ i.theaterName || '-' }}》</view
                  >
                </template>
              </view>
            </view>
            <u-parse
              class="break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
              :content="i.theaterContent"></u-parse>

            <view
              class="mb-2.5 text-right font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-30"
              @tap="i.isOpen = false"
              >收起</view
            >
          </template>
        </template>

        <view
          class="mb-[10rpx] font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
          v-if="i.deleted === 0 && i.status === 1"
          >该用户评论已被删除。</view
        >
        <view
          class="mb-[10rpx] font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
          v-if="i.deleted === 1 && i.status === 0"
          >该用户评论涉嫌违规，已被举报。</view
        >
        <view
          class="mb-[10rpx] font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100"
          v-if="i.deleted === 0 && i.status === 0"
          >该用户评论已被举报，已被删除。</view
        >

        <view class="mb-[10rpx] mt-[10rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
          >{{ i.createTime || '-年-月-日' }}{{ i.visible == 1 ? '·评价仅自己可见' : '' }}</view
        >

        <view class="mb-2 h-[1rpx] w-full bg-w-10"></view>

        <view class="flex items-center justify-start">
          <!-- 回复人数 -->
          <view
            class="flex grow items-center justify-center"
            @tap.stop.prevent="
              $push({
                name: 'CommentDetail',
                params: {
                  id: i.commentId,
                  commentType: 3,
                  theaterId: i.theaterId,
                  repertoireId: i.repertoireId,
                  repertoireInfoDetailId: i.repertoireInfoDetailId,
                },
              })
            ">
            <image class="mr-[12rpx] block h-4 w-[36rpx]" :src="$iconFormat('icon/reply.svg')" mode="scaleToFill" />
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ i.replyCount || 0 }}</text>
          </view>

          <!-- 赞人数 -->
          <view
            class="flex grow items-center justify-center"
            @tap.stop.prevent="handleKudos(i.commentId, i.kudosStatus == 1 ? undefined : 1)">
            <image
              class="mr-[12rpx] block h-4 w-[36rpx]"
              :src="$iconFormat('icon/thumbsUp2.svg')"
              mode="scaleToFill"
              v-if="i.kudosStatus == 1" />
            <image
              class="mr-[12rpx] block h-4 w-[36rpx]"
              :src="$iconFormat('icon/thumbsUp1.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ i.likeCount || 0 }}</text>
          </view>

          <!-- 踩人数 -->
          <view
            class="flex grow items-center justify-center"
            @tap.stop.prevent="handleKudos(i.commentId, i.kudosStatus == 0 ? undefined : 0)">
            <image
              class="mr-[12rpx] block h-4 w-[36rpx]"
              :src="$iconFormat('icon/thumbsUp4.svg')"
              mode="scaleToFill"
              v-if="i.kudosStatus == 0" />
            <image
              class="mr-[12rpx] block h-4 w-[36rpx]"
              :src="$iconFormat('icon/thumbsUp3.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{
              i.dislikeCount || 0
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
      <u-empty
        :icon="$iconFormat('empty/comment.svg')"
        height="322rpx"
        mode="data"
        text="暂无可评价剧目，去扫描演出票吧"
        width="472rpx"></u-empty>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $commentInfoSave } from '@/api/base'
  import { $commentListByUser } from '@/api/comment'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $checkKudos, $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'
  import _ from 'lodash'
  import dayjs from 'dayjs'

  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 商品列表

  onLoad(() => {
    uni.$on('updateHistory', () => {
      mescrollObj.value.resetUpScroll()
    })

    uni.$on('updateHistoryComment', () => {})
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $commentListByUser({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
      commentFlag: 1,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          i.isOpen = false
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日')
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY年MM月DD日')
          if (i.content) i.text = i.content.replace(/<[^>]+>/g, '')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 赞踩 */
  const handleKudos = (commentId: number, type: any) => {
    uni.$u.throttle(() => {
      $commentInfoSave({ userId: userInfo.value.id, commentId, type }).then((res: any) => {
        let arr: any = _.cloneDeep(list.value)

        arr.map((i: any) => {
          if (i.commentId === commentId) {
            $checkKudos(i, type)
          }
        })

        list.value = _.cloneDeep(arr)
      })
    }, 500)
  }
</script>

<style lang="scss" scoped></style>
