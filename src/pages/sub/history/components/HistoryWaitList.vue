<template>
  <mescroll-uni
    class="block h-full w-full"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt3"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="w-full overflow-hidden rounded-t-[30rpx]" v-if="list && list.length">
      <view
        class="mb-[10rpx] flex w-full items-start justify-start bg-[#1F1933] pb-2.5 pl-3 pr-3 pt-2.5 first:pt-[40rpx] last:mb-0"
        :key="i.id"
        @tap="$push({ name: 'RepertoireDetail', params: { id: i.repertoireId } })"
        v-for="i in list">
        <!-- 海报 -->
        <u-image
          class="shrink-0"
          :src="$picFormat(i.repertoireCoverPicture)"
          bgColor="transparent"
          height="160rpx"
          mode="aspectFill"
          radius="10rpx"
          width="160rpx"></u-image>

        <view class="ml-2.5 flex grow items-center justify-start">
          <!-- 信息 -->
          <view class="grow">
            <view class="mb-[10rpx] font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-100">{{
              i.repertoireName
            }}</view>

            <view class="font-Regular text-[22rpx] font-normal leading-[36rpx] text-w-60"
              >演出时间:{{ i.startTime }}</view
            >
            <view class="font-Regular text-[22rpx] font-normal leading-[36rpx] text-w-60"
              >演出场地:{{ i.theaterName }}</view
            >
            <!-- <view class="font-Regular text-[22rpx] font-normal leading-[36rpx] text-w-60">演出场次:{{ '-' }}</view> -->
          </view>

          <!-- 评价按钮 -->
          <u-button
            class="followBtn shrink-0"
            type="primary"
            :customStyle="{ width: '188rpx', height: '62rpx', ...btnStyle }"
            :hairline="false"
            @tap.stop.prevent="handleComment(i)"
            color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
            shape="circle"
            >评价</u-button
          >
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="emptyWrap2 w-full pt-[200rpx]" v-if="!list || !list.length">
      <u-empty
        :icon="$iconFormat('empty/repertoire.svg')"
        height="426rpx"
        mode="data"
        text="暂无可评价剧目，去扫描演出票吧"
        width="516rpx"></u-empty>
    </view>
  </mescroll-uni>

  <!-- <comment :detail="selItem" :popupContaoller="popupContaoller" @close="handleCloseCommentPopup" v-if="selItem" /> -->
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $commentListByUser } from '@/api/comment'
  // import comment from '@/components/Comment.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $picFormat, $push, $replace, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  /* 按钮样式 */
  const btnStyle = ref({
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '28rpx',
    fontWeight: 500,
    padding: 0,
  })

  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 商品列表

  const popupContaoller = ref(false) // 评价弹窗控制器
  const selItem = ref<any>('') // 剧场详情
  const skip = ref(false) // 是否跳转到剧目评论页

  onLoad((params: any) => {
    if (params.skip) skip.value = true

    /* 发布评价成功 */
    uni.$on('updateHistoryComment', () => {
      uni.$emit('updateHistory')
      mescrollObj.value.resetUpScroll()
      if (skip.value)
        $replace({
          name: 'RepertoireDetail',
          params: { id: selItem.value.repertoireId, bottom: 1 },
        })
    })
  })

  /* 加载数据 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    $commentListByUser({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      userId: userInfo.value.id,
      commentFlag: 0,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日')
          // if (i.startTime) i.startTime = dayjs(i.startTime).format('YYYY年MM月DD日 HH:mm')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 发表评价 */
  const handleComment = (item: any) => {
    // selItem.value = item
    // popupContaoller.value = true

    $push({ name: 'HistoryComment', params: item })
  }

  /* 发布评价成功 */
  // const handleCloseCommentPopup = (flag?: boolean) => {
  //   popupContaoller.value = false

  //   uni.$emit('updateHistory')

  //   if (flag) {
  //     mescrollObj.value.resetUpScroll()
  //     if (skip.value) $replace({ name: 'RepertoireDetail', params: { id: selItem.value.repertoireId, bottom: 1 } })
  //   }
  // }
</script>

<style lang="scss" scoped></style>
