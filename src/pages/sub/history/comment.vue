<template>
  <!-- 导航栏 -->
  <u-navbar
    class="w-full shrink-0"
    title="发表评价"
    :titleStyle="titleStyle2"
    @leftClick="$back"
    bgColor="transparent"
    leftIconColor="#FFFFFF"
    placeholder
    titleWidth="574rpx" />
  <view class="w-full border-b border-solid border-w-10"></view>

  <view class="m-auto w-[702rpx] pt-[30rpx]">
    <!-- 剧目信息 -->
    <view
      class="flex w-full items-center justify-start rounded-[20rpx] bg-w-10 p-1 pr-[12rpx]"
      @tap="$push({ name: 'RepertoireDetail', params: { id: detail.repertoireId } })">
      <u-image
        class="shrink-0"
        :src="$picFormat(detail?.repertoireCoverPicture)"
        bgColor="transparent"
        height="80rpx"
        mode="aspectFill"
        radius="10rpx"
        width="80rpx"></u-image>

      <view class="ml-2.5 grow">
        <view class="mb-[4rpx] font-Medium text-[24rpx] font-medium leading-[34rpx] text-w-100">{{
          detail?.repertoireName || '-'
        }}</view>
        <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
          >演出时间:{{ detail?.startTime || '-' }} | 演出场地:{{ detail?.theaterName || '-' }}</view
        >
      </view>
    </view>

    <!-- 评价 -->
    <u-form class="formWrap w-full" :model="formData" :rules="$commentRules" labelWidth="0" ref="formRef">
      <view class="mb-2.5 w-full overflow-hidden rounded-[20rpx] bg-deepbg3">
        <!-- 剧目评价： -->
        <u-form-item class="formItem" prop="content">
          <view class="shrink-0 self-start pt-2.5 font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-80"
            >剧目评价：</view
          >

          <view class="grow pb-[50rpx] pr-2.5 pt-2.5">
            <editor
              class="commentBox2"
              @input="handleEditorChange1"
              @touchend.stop.prevent
              placeholder="你对本剧目的评价是？分享表演，演员，演出氛围等体验和更多友友们安利吧~"></editor>
          </view>

          <view class="textCount">{{ textCount1 }}</view>
        </u-form-item>

        <view class="m-auto h-[2rpx] w-[662rpx] bg-w-10"></view>

        <!-- 剧场评价： -->
        <u-form-item class="formItem" prop="theaterContent">
          <view class="shrink-0 self-start pt-2.5 font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-80"
            >剧场评价：</view
          >

          <view class="grow pb-[50rpx] pr-2.5 pt-2.5">
            <editor
              class="commentBox2"
              @input="handleEditorChange2"
              @touchend.stop.prevent
              placeholder="你对本剧场的评价是？分享地理位置，环境，服务等体验可以帮到友友们哦~"></editor>
          </view>

          <view class="textCount">{{ textCount2 }}</view>
        </u-form-item>
      </view>

      <!-- 剧目赞踩 -->
      <u-form-item class="formItem mbPatch1" prop="repertoireFlag">
        <view class="flex h-[60rpx] w-full items-center justify-end bg-[#07011D] pl-[10rpx] pr-[18rpx]">
          <view class="mr-2.5 flex grow items-center justify-start">
            <u-image
              class="shrink-0"
              :src="$picFormat(detail?.repertoireCoverPicture)"
              bgColor="transparent"
              height="48rpx"
              mode="aspectFill"
              radius="8rpx"
              width="48rpx"></u-image>
            <view class="ml-2 line-clamp-1 font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-100">{{
              detail?.repertoireName || '-'
            }}</view>
          </view>

          <!-- 踩 -->
          <view class="mr-[42rpx] flex shrink-0 items-center justify-end" @tap="formData.repertoireFlag = 0">
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp3.svg')"
              mode="scaleToFill"
              v-if="formData.repertoireFlag !== 0" />
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp4.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">踩</text>
          </view>

          <!-- 赞 -->
          <view class="flex shrink-0 items-center justify-end" @tap="formData.repertoireFlag = 1">
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp1.svg')"
              mode="scaleToFill"
              v-if="formData.repertoireFlag !== 1" />
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp2.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">赞</text>
          </view>
        </view>
      </u-form-item>

      <!-- 剧场赞踩 -->
      <u-form-item class="formItem borderPatch pb-2" prop="theaterFlag">
        <view class="flex h-[60rpx] w-full items-center justify-end bg-[#07011D] pl-[10rpx] pr-[18rpx]">
          <view class="mr-2.5 flex grow items-center justify-start">
            <u-image
              class="shrink-0"
              :src="$picFormat(detail?.theaterCoverPicture)"
              bgColor="transparent"
              height="48rpx"
              mode="aspectFill"
              radius="8rpx"
              width="48rpx"></u-image>
            <view class="ml-2 line-clamp-1 font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-100">{{
              detail?.theaterName || '-'
            }}</view>
          </view>

          <!-- 踩 -->
          <view class="mr-[42rpx] flex shrink-0 items-center justify-end" @tap="formData.theaterFlag = 0">
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp3.svg')"
              mode="scaleToFill"
              v-if="formData.theaterFlag !== 0" />
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp4.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">踩</text>
          </view>

          <!-- 赞 -->
          <view class="flex shrink-0 items-center justify-end" @tap="formData.theaterFlag = 1">
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp1.svg')"
              mode="scaleToFill"
              v-if="formData.theaterFlag !== 1" />
            <image
              class="mr-1 block h-[30rpx] w-[30rpx]"
              :src="$iconFormat('icon/thumbsUp2.svg')"
              mode="scaleToFill"
              v-else />
            <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">赞</text>
          </view>
        </view>
      </u-form-item>
    </u-form>

    <!-- 评价仅自己可见 -->
    <view class="mb-5 mt-2 flex" @tap="formData.visible = !formData.visible">
      <image
        class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
        :src="$iconFormat('icon/radioOff.svg')"
        mode="scaleToFill"
        v-if="!formData.visible" />
      <image
        class="mr-[14rpx] block h-[34rpx] w-[34rpx]"
        :src="$iconFormat('icon/radioOn.svg')"
        mode="scaleToFill"
        v-else />

      <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">评价仅自己可见</text>
    </view>

    <customBtn class="m-auto mt-0" @tap="handleSave" text="保存" />
  </view>

  <u-toast ref="uToast" />
</template>

<script lang="ts" setup>
  import { $commentAdd } from '@/api/comment'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $push } from '@/utils/methods'
  import { $commentRules } from '@/utils/rules'

  const { titleStyle2, userInfo } = storeToRefs(useGlobalStore())

  const app: any = getCurrentInstance()?.proxy

  const formRef = ref()

  const detail = ref()
  const cursorSpacing = ref(uni.upx2px(220))
  const textCount1 = ref(0)
  const editorCtx1 = ref()
  const textCount2 = ref(0)
  const editorCtx2 = ref()

  /* 表单 */
  const formData = reactive<any>({
    grade: 0, // 评分
    content: undefined, // 剧目评价
    repertoireFlag: undefined, // 剧目赞踩
    theaterContent: undefined, // 剧场评价
    theaterFlag: undefined, // 剧场赞踩
    visible: false, // 评价仅自己可见
  })

  onLoad((params: any) => {
    params.theaterCoverPicture = decodeURIComponent(params.theaterCoverPicture)
    params.theaterName = decodeURIComponent(params.theaterName)
    params.repertoireCoverPicture = decodeURIComponent(params.repertoireCoverPicture)
    params.repertoireName = decodeURIComponent(params.repertoireName)
    params.startTime = decodeURIComponent(params.startTime)
    params.endTime = decodeURIComponent(params.endTime)

    detail.value = params
  })

  const handleEditorChange1 = (e: any) => {
    textCount1.value = e.detail.text?.replace(/\n/g, '').length || 0

    formData.content = textCount1.value ? e.detail.html : ''
  }

  const handleEditorChange2 = (e: any) => {
    textCount2.value = e.detail.text?.replace(/\n/g, '').length || 0
    formData.theaterContent = textCount2.value ? e.detail.html : ''
  }

  /* 提交评论 */
  const handleSave = () => {
    // formRef.value.validate().then((res: any) => {})
    $loading('加载中')
    $commentAdd({
      userReceivingRecordsId: detail.value.id,
      theaterId: detail.value.theaterId,
      repertoireId: detail.value.repertoireId,
      repertoireInfoDetailId: detail.value.repertoireInfoDetailId,
      repertoireFlag: formData.repertoireFlag,
      theaterFlag: formData.theaterFlag,
      visible: formData.visible ? 1 : 0,
      comment: {
        theaterId: detail.value.theaterId,
        repertoireId: detail.value.repertoireId,
        repertoireInfoDetailId: detail.value.repertoireInfoDetailId,
        userId: userInfo.value.id,
        grade: formData.grade,
        content: formData.content || '此用户没有填写评价',
        theaterContent: formData.theaterContent || '此用户没有填写评价',
        visible: formData.visible ? 1 : 0,
      },
    }).then((res: any) => {
      formData.grade = 0
      formData.content = undefined
      formData.repertoireFlag = undefined
      formData.theaterContent = undefined
      formData.theaterFlag = undefined
      formData.visible = false

      uni.hideLoading()

      $back()

      uni.$emit('updateHistoryComment')
    })
  }

  const hideKeyboard = () => {
    uni.hideKeyboard()
  }
</script>

<style lang="scss" scoped>
  .formWrap {
    .formItem {
      margin-bottom: 0;

      &.mbPatch1 {
        margin-bottom: 10rpx;
      }

      &.mbPatch2 {
        margin-bottom: 20rpx;
      }

      &.pbPatch1 {
        padding-bottom: 16rpx;
      }

      &.pPatch {
        &:deep(.u-form-item__body) {
          .u-form-item__body__right {
            .u-form-item__body__right__content__slot {
              box-sizing: border-box;
              padding: 20rpx 20rpx 50rpx;
            }
          }
        }
      }

      &:deep(.u-form-item__body) {
        padding: 0 !important;

        .u-form-item__body__right {
          width: 100%;
        }
      }

      &:deep(.u-form-item__body__right__message) {
        padding-right: 20rpx;
        padding-bottom: 20rpx;
        text-align: right;
      }

      .commentBox2 {
        width: 100%;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 26rpx !important;
        font-weight: 400;
        line-height: 36rpx !important;
        color: #fff !important;

        &:deep(.ql-editor.ql-blank) {
          &::before {
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 26rpx;
            font-style: normal !important;
            font-weight: 400;
            line-height: 36rpx !important;
            color: rgb(255 255 255 / 40%) !important;
          }
        }
      }

      .textCount {
        position: absolute;
        right: 20rpx;
        bottom: 14rpx;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 24rpx;
        font-weight: 400;
        line-height: 34rpx;
        color: rgb(255 255 255 / 30%);
        background: transparent !important;
      }
    }
  }
</style>
