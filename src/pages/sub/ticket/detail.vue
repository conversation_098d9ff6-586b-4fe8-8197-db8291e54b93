<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-deepbg bg-fullAuto bg-no-repeat pb-[50rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="z-50 w-full shrink-0"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder />

    <scroll-view class="h-0 w-full grow" scrollY>
      <!-- 普通版 -->
      <template v-if="!showType">
        <!-- 电子票普通样式预览 -->
        <view class="relative mb-[40rpx] mt-[20rpx] min-h-[720rpx] w-full">
          <u-image
            class="m-auto block w-fit"
            :src="ticketInfo.commonImage"
            height="auto"
            mode="widthFix"
            width="560rpx"></u-image>
        </view>

        <!-- 电子票信息（普通） -->
        <view class="relative m-auto mb-[46rpx] mt-[34rpx] h-fit w-[706rpx]">
          <image class="block w-full" :src="$iconFormat('background/infoDailogT.png')" mode="widthFix" />
          <view
            class="relative z-10 mb-[-2rpx] mt-[-4rpx] h-fit w-full border-l-[4rpx] border-r-[4rpx] border-[#BC93FF] bg-[#100636] pb-[2rpx] pl-[38rpx] pr-[38rpx] pt-[2rpx]">
            <view class="mb-[10rpx] flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >剧目名称</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.repertoireName || '-'
              }}</text>
            </view>
            <view class="mb-[10rpx] flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >剧场名称</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.theaterName || '-'
              }}</text>
            </view>
            <view class="mb-[10rpx] flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >演出时间</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.time || '-'
              }}</text>
            </view>
            <view class="mb-[10rpx] flex flex-col items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >演职信息</text
              >
              <text
                class="mb-[12rpx] grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100 last:mb-0"
                :key="index"
                v-for="(i, index) in actorList"
                >{{ i.actorInformation || '-' }}</text
              >
            </view>
            <view class="flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >获得时间</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.createTime || '-'
              }}</text>
            </view>
          </view>
          <image class="block w-full" :src="$iconFormat('background/infoDailogB.png')" mode="widthFix" />
        </view>
      </template>

      <!-- 升级版 -->
      <template v-if="showType">
        <!-- 电子票升级样式预览 -->
        <veiw class="relative z-20 w-full">
          <view
            class="relative mt-[20rpx] h-[1000rpx] w-full overflow-hidden"
            @click.stop.prevent
            @touchstart.stop.prevent>
            <model
              class="absolute bottom-0 left-0 right-0 top-0 m-auto"
              type="ticket"
              :url="[ticketInfo.upgradeImage, ticketInfo.coverReverse]"
              height="1150rpx"
              ref="modelRef"
              v-if="ticketInfo.upgradeImage && ticketInfo.coverReverse" />
          </view>

          <view class="relative z-20 m-auto mb-[46rpx] mt-[-16rpx] flex h-8 w-[622rpx] items-center justify-between">
            <image
              class="block h-8 w-8"
              :src="$iconFormat('icon/rotateR.svg')"
              @longtap.stop.prevent="rotateModel(1)"
              @touchend="rotateStop"
              mode="scaleToFill" />
            <image
              class="block h-8 w-8"
              :src="$iconFormat('icon/rotateL.svg')"
              @longtap.stop.prevent="rotateModel(2)"
              @touchend="rotateStop"
              mode="scaleToFill" />
          </view>
        </veiw>

        <view
          class="relative z-20 mt-[-100rpx] w-full text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100"
          >按住画面左右翻动可查看票的背面</view
        >

        <!-- 电子票信息（升级） -->
        <view class="relative m-auto mb-[46rpx] mt-[34rpx] h-fit w-[706rpx]">
          <image class="block w-full" :src="$iconFormat('background/infoDailogT.png')" mode="widthFix" />
          <view
            class="relative z-10 mb-[-2rpx] mt-[-4rpx] h-fit w-full border-l-[4rpx] border-r-[4rpx] border-[#BC93FF] bg-[#100636] pb-[2rpx] pl-[38rpx] pr-[38rpx] pt-[2rpx]">
            <view class="mb-[20rpx] flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >剧目名称</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.repertoireName || '-'
              }}</text>
            </view>
            <view class="mb-[20rpx] flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >剧场名称</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.theaterName || '-'
              }}</text>
            </view>
            <view class="mb-[20rpx] flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >演出时间</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.time || '-'
              }}</text>
            </view>
            <view class="mb-[10rpx] flex flex-col items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >演职信息</text
              >
              <text
                class="mb-[12rpx] grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100 last:mb-0"
                :key="index"
                v-for="(i, index) in actorList"
                >{{ i.actorInformation || '-' }}</text
              >
            </view>
            <view class="flex items-start justify-start">
              <text
                class="mr-[30rpx] w-[4em] shrink-0 text-right font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-60"
                >获得时间</text
              >
              <text class="grow font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100">{{
                ticketInfo.upgradeTime || '-'
              }}</text>
            </view>
          </view>
          <image class="block w-full" :src="$iconFormat('background/infoDailogB.png')" mode="widthFix" />
        </view>
      </template>
    </scroll-view>

    <image
      class="fixed bottom-0 right-[24rpx] top-0 z-20 mb-auto mt-auto block h-[150rpx] w-[84rpx]"
      :src="$iconFormat('share.webp')"
      @tap="$push({ name: 'TicketSave', params: { id: ticketInfo.id, isUpdate: upgradeStatus } })"
      mode="scaleToFill" />

    <view class="m-auto mt-2.5 flex w-[582rpx] shrink-0 items-center justify-between">
      <customBtn
        class="m-auto"
        :text="actorList && actorList.length ? '编辑信息' : '追加信息'"
        @tap="$push({ name: 'AddActor', params: { id } })"
        width="260" />
      <customBtn
        class="m-auto"
        type="4"
        @tap="showType = false"
        text="查看普通票根"
        v-if="upgradeStatus === 1 && showType"
        width="260" />
      <customBtn
        class="m-auto"
        type="4"
        @tap="showType = true"
        text="查看升级票根"
        v-if="upgradeStatus === 1 && !showType"
        width="260" />
      <customBtn
        class="m-auto"
        type="3"
        @tap="$push({ name: 'Upgrade', params: { portfolioNo: ticketInfo.portfolioNo } })"
        text="升级收藏"
        v-else-if="upgradeStatus === 0 && ticketInfo.coverFront"
        width="260" />
    </view>
  </view>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $collectionDetails } from '@/api/scan'
  import { $userReceivingRecordsText } from '@/api/ticket'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import model from '@/components/Model.vue'
  import network from '@/components/Network.vue'
  import { $back, $iconFormat, $picFormat, $push } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy

  const id = ref() // 电子票id
  const portfolioId = ref() // 电子票组合id
  const upgradeStatus = ref(0) // 是否升级
  const ticketInfo = ref<any>('') // 电子票信息
  const showType = ref(false) // 显示类型 false：普通版 true：升级版

  const actorList = ref<any>([])

  onLoad(async (params: any) => {
    id.value = params.id
    portfolioId.value = params.portfolioId

    let detail: any = ''

    /* 获取电子票详情 */
    await $collectionDetails(id.value).then((res: any) => {
      if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY-MM-DD HH:mm')
      if (res.data.time) res.data.time = dayjs(res.data.time).format('YYYY-MM-DD HH:mm')
      if (res.data.upgradeTime) res.data.upgradeTime = dayjs(res.data.upgradeTime).format('YYYY-MM-DD HH:mm')

      if (res.data.image) res.data.image = $picFormat(res.data.image)
      if (res.data.upgradeImage) res.data.upgradeImage = $picFormat(res.data.upgradeImage)
      if (res.data.upgradeCoverReverse) res.data.upgradeCoverReverse = $picFormat(res.data.upgradeCoverReverse)

      detail = res.data

      upgradeStatus.value = detail.upgradeStatus
      showType.value = detail.upgradeStatus === 1 ? true : false

      ticketInfo.value = {
        id: detail.id,
        repertoireName: detail.repertoireName,
        theaterName: detail.theaterName,
        time: detail.time,
        createTime: detail.createTime,
        upgradeTime: detail.upgradeTime,
        commonImage: detail.image,
        upgradeImage: detail.upgradeImage,
        coverReverse: detail.upgradeCoverReverse,
        portfolioNo: detail.portfolioNo,
        portfolioId: detail.portfolioId,
        coverFront: detail.coverFront,
      }
    })

    /* 获取演职列表 */
    await $userReceivingRecordsText({ userReceivingRecordsId: id.value - 0 }).then((res: any) => {
      actorList.value = res.data
    })
  })

  /* 旋转模型 */
  const rotateModel = (type: number) => {
    app.$refs.modelRef.rotateModel(type)
  }
  /* 停止旋转模型 */
  const rotateStop = () => {
    app.$refs.modelRef.rotateStop()
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/ticketBg.webp');

    .infoBox1 {
      background-image: url($icon + 'background/infoDailog3.webp');
    }

    .infoBox2 {
      background-image: url($icon + 'background/infoDailog2.webp');
    }
  }
</style>
