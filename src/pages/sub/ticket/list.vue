<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="电子票夹"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      placeholder
      titleWidth="574rpx" />
    <view class="w-full border-b border-solid border-w-10"></view>

    <!-- 筛选栏 -->
    <view class="filterBar relative z-20 mb-2.5 mt-2.5 flex w-full items-center justify-start pl-3 pr-3">
      <!-- 搜索 -->
      <view class="searchWrap flex h-8 grow items-center justify-start rounded-full bg-w-10 pl-[10rpx] pr-[10rpx]">
        <image class="h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
        <u-input
          class="searchBox"
          @clear="handleSearch"
          @confirm="handleSearch"
          border="none"
          clearable
          placeholder="请输入剧目名称、演职人员查询"
          v-model="keyword" />
      </view>
      <!-- 排序类型 -->
      <view
        class="relative ml-2.5 flex h-8 w-[188rpx] shrink-0 items-center justify-center rounded-full bg-w-10"
        @tap="orderListController = !orderListController">
        <text class="text-center font-Regular text-[26rpx] font-normal leading-[32rpx] text-w-60">{{
          orderByTxt
        }}</text>
        <image class="mr-[-3rpx] block h-4 w-4 shrink-0" :src="$iconFormat('icon/sort.svg')" mode="scaleToFill" />

        <u-transition :show="orderListController" mode="fade">
          <view
            class="absolute left-0 right-0 top-[80rpx] z-20 m-auto h-fit w-[160rpx] overflow-hidden rounded-[20rpx]">
            <view
              class="h-[60rpx] w-full bg-[#342B54] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
              :key="index"
              @tap="handleSelOrderBy(item)"
              v-for="(item, index) in orderList"
              >{{ item.name }}</view
            >
          </view>
        </u-transition>
      </view>
      <!-- 列表样式切换 -->
      <view class="ml-2.5 flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-w-10">
        <image
          class="block h-8 w-8"
          :src="$iconFormat('listType/list.svg')"
          @tap="listType = 2"
          mode="scaleToFill"
          v-if="listType == 1" />
        <image
          class="block h-8 w-8"
          :src="$iconFormat('listType/banner.svg')"
          @tap="listType = 1"
          mode="scaleToFill"
          v-else />
      </view>
    </view>
    <!-- 演出时间 -->
    <view
      class="mb-[28rpx] mt-[12rpx] box-border flex w-full items-center justify-start pl-3 pr-3"
      v-if="['time', 'createTime'].includes(orderBy)">
      <view class="flex w-[280rpx] flex-wrap items-center justify-start" @click="hanldeShowDate(1)">
        <view class="grow font-Regular text-[24rpx] font-normal leading-[40rpx] text-w-60">{{
          startDate || '请选择日期'
        }}</view>
        <image class="block h-5 w-5 shrink-0" :src="$iconFormat('icon/date.svg')" mode="scaleToFill" />
        <u-line class="shrink-0" color="rgba(255,255,255,0.4)" margin="10rpx 0 0" />
      </view>

      <text class="mb-[10rpx] ml-[18rpx] mr-[22rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60"
        >至</text
      >

      <view class="flex w-[280rpx] flex-wrap items-center justify-start" @click="hanldeShowDate(2)">
        <view class="grow font-Regular text-[24rpx] font-normal leading-[40rpx] text-w-60">{{
          endDate || '请选择日期'
        }}</view>
        <image class="block h-5 w-5 shrink-0" :src="$iconFormat('icon/date.svg')" mode="scaleToFill" />
        <u-line class="shrink-0" color="rgba(255,255,255,0.4)" margin="10rpx 0 0" />
      </view>

      <view
        class="ml-2.5 shrink-0 font-Regular text-[28rpx] font-normal leading-[40rpx] text-[#9C75D3]"
        @click="handleResetDate"
        >重置</view
      >
    </view>
    <!-- 电子票分组 -->
    <view class="relative mb-2 w-full">
      <scroll-view class="w-full" scrollX>
        <view class="box-border flex h-[60rpx] w-fit items-center justify-start pl-3 pr-[190rpx]">
          <view
            class="mr-[10rpx] box-border h-[60rpx] w-fit min-w-[120rpx] whitespace-nowrap rounded-full pl-2.5 pr-2.5 text-center font-Medium text-[26rpx] font-medium leading-[60rpx] last:mr-0"
            :class="ticketGroupId === i.id ? 'bg-[#9C75D3] text-w-100' : 'bg-w-20 text-w-60'"
            :key="index"
            @click="handleSwitchGroupId(i.id)"
            v-for="(i, index) in ticketGroupList"
            >{{ i.name }}</view
          >
        </view>
      </scroll-view>

      <view
        class="managerBtn absolute right-0 top-0 flex items-center justify-center"
        @click="$push({ name: 'TicketGroup' })">
        <image class="mr-1 block h-5 w-5" :src="$iconFormat('icon/setting.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[28rpx] font-normal leading-[50rpx] text-w-100">管理</text>
      </view>
    </view>
    <!-- 金额统计 -->
    <view class="box-border flex w-full items-center justify-between pl-3 pr-3">
      <view class="font-Medium text-[24rpx] font-medium leading-[34rpx] text-w-100" v-if="showMoney">
        <text>共</text>
        <text class="text-[#FF9500]">{{ ticketCount || 0 }}</text>
        <text>场，</text>
        <text class="text-[#FF9500]">{{ tickerTotalMoney || 0 }}</text>
        <text>元</text>
      </view>
      <view v-else></view>

      <view class="flex items-center justify-end">
        <text class="mr-2 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">显示/隐藏金额</text>
        <u-switch :size="20" activeColor="#9C75D3" v-model="showMoney"></u-switch>
      </view>
    </view>

    <mescroll-uni
      class="block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt3"
      @down="downCallback"
      @init="mescrollInit"
      @topclick="$topclick"
      @up="upCallback">
      <view class="w-full pl-3 pr-3 pt-2.5" v-if="list && list.length">
        <ticketList1 :list="list" v-if="listType == 1" />
        <ticketList2 :list="list" v-else-if="listType == 2" />
      </view>

      <!-- 空状态 -->
      <view class="emptyWrap mtPatch w-full pt-[200rpx]" v-if="!list || !list.length">
        <u-empty
          :icon="$iconFormat('empty/ticket.webp')"
          height="256rpx"
          mode="data"
          text="哔嘟哔嘟~您暂时还没有领取过电子票~"
          width="386rpx"></u-empty>
      </view>
    </mescroll-uni>
  </view>

  <!-- 开始时间 -->
  <u-datetime-picker
    :maxDate="maxDate1"
    :minDate="minDate1"
    :show="startDateController"
    @cancel="startDateController = false"
    @confirm="hadnleSelStartDate"
    mode="date"
    v-model="currectDate1" />
  <!-- 结束时间 -->
  <u-datetime-picker
    :minDate="minDate2"
    :show="endDateController"
    @cancel="endDateController = false"
    @confirm="hadnleSelEndDate"
    mode="date"
    v-model="currectDate2" />
  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import ticketList1 from './components/TicketList1.vue'
  import ticketList2 from './components/TicketList2.vue'
  import { $collectionListByPage } from '@/api/scan'
  import { $sumPrice, $ticketGroupPull } from '@/api/ticket'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const mescrollObj = ref() // 滚动obj

  const keyword = ref('') // 搜索关键字
  /* 排序字段列表 */
  const orderList = ref([
    { name: '按演出日期', string: 'time' },
    { name: '按剧目名称', string: 'repertoireName' },
    { name: '按剧场名称', string: 'theaterName' },
    { name: '按价格', string: 'price' },
    { name: '按城市', string: 'cityName' },
    { name: '按领取时间', string: 'createTime' },
  ])
  const orderListController = ref(false) // 排序字段列表弹窗控制器
  const orderBy = ref('createTime') // 排序字段
  const orderByTxt = ref('按领取时间') // 排序字段文案
  const listType = ref(1) // 列表模式

  const startDateController = ref(false) // 开始时间控制器
  const endDateController = ref(false) // 结束时间控制器
  const currectDate1 = ref(dayjs().valueOf()) // 当前选择时间
  const currectDate2 = ref(dayjs().valueOf()) // 当前选择时间
  const startDate = ref('') // 开始时间
  const endDate = ref('') // 结束时间

  const minDate1 = computed(() => dayjs('2000-01-01').valueOf())
  const maxDate1 = computed(() => (endDate.value ? dayjs(endDate.value).valueOf() : dayjs().add(3, 'Year').valueOf()))
  const minDate2 = computed(() => (startDate.value ? dayjs(startDate.value).valueOf() : dayjs('2000-01-01').valueOf()))

  const ticketGroupList = ref<any>([]) // 电子票分组
  const ticketGroupId = ref(null) // 电子票分组id
  const showMoney = ref(true) // 显示/隐藏金额

  const list = ref<any>() // 电子票列表
  const ticketCount = ref(0) // 电子票数量
  const tickerTotalMoney = ref(0) // 电子票价

  /* 数据加载 */
  const upCallback = async (mescroll: any) => {
    mescrollObj.value = mescroll

    if (mescroll.num === 1) {
      await handleGetTicketGroupPull()

      $sumPrice({
        badgeType: 1,
        userId: userInfo.value.id,
        keyword: keyword.value || undefined,
        ticketGroupId: ticketGroupId.value,
        time: {
          beginTime: orderBy.value === 'time' ? startDate.value || undefined : undefined,
          endTime: orderBy.value === 'time' ? endDate.value || undefined : undefined,
        },
        getTime: {
          beginTime: orderBy.value === 'createTime' ? startDate.value || undefined : undefined,
          endTime: orderBy.value === 'createTime' ? endDate.value || undefined : undefined,
        },
      }).then((res: any) => {
        tickerTotalMoney.value = res.data || 0
      })
    }

    $collectionListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      badgeType: 1,
      userId: userInfo.value.id,
      keyword: keyword.value || undefined,
      ticketGroupId: ticketGroupId.value,
      time: {
        beginTime: orderBy.value === 'time' ? startDate.value || undefined : undefined,
        endTime: orderBy.value === 'time' ? endDate.value || undefined : undefined,
      },
      getTime: {
        beginTime: orderBy.value === 'createTime' ? startDate.value || undefined : undefined,
        endTime: orderBy.value === 'createTime' ? endDate.value || undefined : undefined,
      },
      orderByColumn:
        orderBy.value + ' ' + (orderBy.value == 'time' || orderBy.value == 'createTime' ? 'desc' : 'asc') + ',id',
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.time) {
            i.isLook = dayjs().isAfter(dayjs(i.time))
            i.time = dayjs(i.time).format('YYYY-MM-DD HH:mm')
          }
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY-MM-DD HH:mm')

          if (i.price) i.price = parseInt(i.price)
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据
        ticketCount.value = res.data.total
        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleSearch = () => {
    mescrollObj.value.resetUpScroll()
  }

  const handleGetTicketGroupPull = async () => {
    await $ticketGroupPull().then((res: any) => {
      ticketGroupList.value = res.data
      if (!ticketGroupId.value) ticketGroupId.value = res.data[0].id
    })
  }

  /* 切换排序字段 */
  const handleSelOrderBy = (orderItem: any) => {
    orderBy.value = orderItem.string
    orderByTxt.value = orderItem.name

    startDate.value = ''
    endDate.value = ''

    mescrollObj.value.resetUpScroll()
  }

  /* 显示时间选择器 */
  const hanldeShowDate = (type: number) => {
    switch (type) {
      case 1:
        startDateController.value = true
        break
      case 2:
        endDateController.value = true
        break
    }
  }

  /* 选择开始时间 */
  const hadnleSelStartDate = (e: any) => {
    startDate.value = dayjs(e.value).format('YYYY-MM-DD')
    startDateController.value = false

    nextTick(() => {
      if (startDate.value && endDate.value) mescrollObj.value.resetUpScroll()
    })
  }

  /* 选择结束时间 */
  const hadnleSelEndDate = (e: any) => {
    endDate.value = dayjs(e.value).format('YYYY-MM-DD')
    endDateController.value = false

    nextTick(() => {
      if (startDate.value && endDate.value) mescrollObj.value.resetUpScroll()
    })
  }

  /* 重置时间 */
  const handleResetDate = () => {
    currectDate1.value = dayjs().valueOf()
    currectDate2.value = dayjs().valueOf()
    startDate.value = ''
    endDate.value = ''
    mescrollObj.value.resetUpScroll()
  }

  /* 切换电子票分组 */
  const handleSwitchGroupId = (id: any) => {
    ticketGroupId.value = id
    mescrollObj.value.resetUpScroll()
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/ticketPacks.webp');

    .searchWrap {
      &:deep(.u-input__content) {
        .u-input__content__field-wrapper {
          &__field,
          .input-placeholder {
            font-size: 24rpx !important;
          }
        }
      }
    }
  }

  .managerBtn {
    width: 166rpx;
    height: 60rpx;
    background: linear-gradient(90deg, rgb(7 1 29 / 0%) 0%, #07011d 35rpx);
  }
</style>
