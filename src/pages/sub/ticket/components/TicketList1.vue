<template>
  <view
    class="mb-[10rpx] w-full overflow-hidden rounded-[20rpx] bg-[#302B40] pl-[16rpx] pr-[16rpx] pt-[20rpx]"
    :class="{ isUpgrade: i.upgradeStatus === 1 }"
    :key="index"
    @tap="$push({ name: 'TicketDetail', params: { id: i.id, portfolioId: i.portfolioId } })"
    v-for="(i, index) in list as any">
    <view class="relative flex items-start justify-start">
      <!-- 海报 -->
      <view class="picWarp relative mr-2.5 h-[172rpx] w-[172rpx]">
        <u-image
          class="shrink-0 overflow-hidden"
          :src="$picFormat(isSel ? i.coverPicture : i.image)"
          bgColor="transparent"
          height="172rpx"
          mode="aspectFill"
          radius="16rpx"
          width="172rpx"></u-image>

        <!-- 地区 -->
        <view
          class="absolute left-[10rpx] top-[10rpx] w-fit rounded-full bg-w-10 pl-[10rpx] pr-[10rpx] font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-100"
          >{{ i.cityName || '-' }}</view
        >
      </view>

      <!-- 基本信息 -->
      <view class="grow">
        <!-- 剧目名称 -->
        <text
          class="mb-[10rpx] mr-2.5 line-clamp-2 grow font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100"
          >{{ i.repertoireName || '-' }}</text
        >

        <view class="relative z-10 w-full">
          <!-- 演出场地 -->
          <view class="mb-1 font-Regular text-xs font-normal leading-4 text-w-60">演出场地：{{ i.theaterName }}</view>
          <!-- 演出时间 -->
          <view class="mb-1 font-Regular text-xs font-normal leading-4 text-w-60">演出时间：{{ i.time }}</view>
          <!-- 领取时间 -->
          <view class="font-Regular text-xs font-normal leading-4 text-w-60">领取时间：{{ i.createTime }}</view>
        </view>
      </view>

      <!-- 观影状态 -->
      <image
        class="absolute bottom-[-38rpx] right-[-46rpx] block h-[216rpx] w-[216rpx]"
        :src="$iconFormat('status/down.webp')"
        mode="scaleToFill"
        v-if="i.isLook" />
      <image
        class="absolute bottom-[-38rpx] right-[-46rpx] block h-[216rpx] w-[216rpx]"
        :src="$iconFormat('status/none.webp')"
        mode="scaleToFill"
        v-else />
    </view>

    <view
      class="mt-[14rpx] flex min-h-[80rpx] w-full items-center justify-start border-t-[1rpx] border-w-20 pr-[20rpx]">
      <!-- 票价 -->
      <view class="shrink-0 grow">
        <text class="mr-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-[#FF7F1A]">票价:</text>
        <text class="font-Medium text-[24rpx] font-bold leading-[30rpx] text-[#FF7F1A]">¥</text>
        <text class="font-Medium text-[40rpx] font-bold leading-[30rpx] text-[#FF7F1A]">{{ i.price || 0 }}</text>
      </view>

      <template v-if="!isSel">
        <u-button
          class="paybtn"
          type="primary"
          :customStyle="{ width: '140rpx', height: '50rpx', whiteSpace: 'noWrap', ...btnStyle1 }"
          :hairline="false"
          @tap.stop.prevent="handleEditActor(i)"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          shape="circle"
          >追加信息</u-button
        >

        <u-button
          class="paybtn ml-2.5"
          type="primary"
          :customStyle="{ width: '140rpx', height: '50rpx', whiteSpace: 'noWrap', ...btnStyle1 }"
          :hairline="false"
          color="linear-gradient(270deg, #F4CC8B 0%, #F7AB77 100%)"
          shape="circle"
          v-if="i.upgradeStatus === 1"
          >已升级</u-button
        >

        <u-button
          class="paybtn ml-2.5"
          type="primary"
          :customStyle="{ width: '140rpx', height: '50rpx', whiteSpace: 'noWrap', ...btnStyle1 }"
          :hairline="false"
          @tap.stop.prevent="handlePay(i)"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          shape="circle"
          v-else-if="i.coverFront"
          >升级收藏</u-button
        >
      </template>
      <template v-else>
        <u-button
          class="paybtn"
          type="primary"
          :customStyle="{ width: '140rpx', height: '50rpx', whiteSpace: 'noWrap', ...btnStyle1 }"
          :hairline="false"
          @tap.stop.prevent="handleSel(i)"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          shape="circle"
          >选择添加</u-button
        >
      </template>
    </view>
  </view>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $back, $iconFormat, $picFormat, $push } from '@/utils/methods'

  const props = defineProps({
    list: { type: Array, required: true }, // 列表数据
    isSel: { type: Boolean, default: false }, // 是否是选择
    selType: { type: String, default: '1' }, // 选择类型
  })

  /* 按钮样式 */
  const btnStyle1 = ref({
    border: '2rpx solid #ffffff',
    color: '#282656',
    fontFamily: 'PingFangSC-Medium, PingFang SC',
    fontSize: '26rpx',
    fontWeight: 500,
    padding: 0,
  })

  /* 升级电子票 */
  const handlePay = (i: any) => {
    $push({ name: 'Upgrade', params: { portfolioNo: i.portfolioNo } })
  }

  /* 编辑演员信息 */
  const handleEditActor = (i: any) => {
    $push({ name: 'AddActor', params: { id: i.id } })
  }

  /* 选择场次 */
  const handleSel = (i: any) => {
    uni.$emit('selTicket' + props.selType, i)
    $back()
  }
</script>

<style lang="scss" scoped>
  .paybtn {
    margin-right: -10rpx;
  }

  .isUpgrade {
    background: #685a4d;
  }
</style>
