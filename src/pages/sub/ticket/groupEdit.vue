<template>
  <view
    class="pageWrap flex min-h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="添加票根"
      :titleStyle="titleStyle1"
      @leftClick="handleCloseAddTicket"
      bgColor="#282142"
      leftIconColor="#FFFFFF"
      placeholder
      v-if="addTicket">
      <template #left>
        <image class="block h-5 w-5" :src="$iconFormat('icon/close4.svg')" mode="scaleToFill" />
      </template>
    </u-navbar>
    <u-navbar
      class="w-full shrink-0"
      :title="formData.id ? '修改分组' : '创建分组'"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#282142"
      leftIconColor="#FFFFFF"
      placeholder
      v-else />
    <view class="w-full border-b border-solid border-w-10"></view>

    <!-- 添加分组 -->
    <template v-if="!addTicket">
      <view class="box-border flex w-full items-center justify-start pb-[44rpx] pl-3 pr-3 pt-[44rpx]">
        <image class="mr-1 block h-4 w-4" :src="$iconFormat('icon/group.svg')" mode="scaleToFill" />
        <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">分组名称</text>
      </view>

      <view class="box-border w-full pl-3 pr-3">
        <u-input class="nameBox" border="none" clearable placeholder="请输入分组名称" v-model="formData.name"></u-input>
        <u-line color="rgba(255, 255, 255, 0.4)" margin="20rpx 0 0" />
      </view>

      <view class="mb-[30rpx] box-border flex w-full items-center justify-start pb-[44rpx] pl-3 pr-3 pt-[44rpx]">
        <image class="mr-1 block h-4 w-4" :src="$iconFormat('icon/add1.svg')" mode="scaleToFill" />
        <text class="font-Medium text-[28rpx] font-medium leading-[40rpx] text-w-60">添加票根</text>
      </view>

      <view class="flex w-full items-center justify-center">
        <view
          class="h-8 w-[192rpx] rounded-[10rpx] text-center font-Medium text-[26rpx] font-medium leading-[64rpx]"
          :class="addType === 0 ? 'bg-[#9C75D3] text-w-100' : 'bg-w-20 text-w-50'"
          @click="handleSwitchType(0)"
          >按条件分组</view
        >
        <view
          class="ml-2.5 h-8 w-[192rpx] rounded-[10rpx] text-center font-Medium text-[26rpx] font-medium leading-[64rpx]"
          :class="addType === 1 ? 'bg-[#9C75D3] text-w-100' : 'bg-w-20 text-w-50'"
          @click="handleSwitchType(1)"
          >手动添加</view
        >
      </view>

      <view class="mt-[50rpx] box-border w-full pl-3 pr-3" v-if="addType === 0">
        <veiw class="flex w-full items-center justify-start">
          <view class="mr-[10rpx] shrink-0 font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
            >信息包含</view
          >
          <u-input
            class="keyword"
            @blur="handleGetTicketList"
            @clear="handleGetTicketList"
            @confirm="handleGetTicketList"
            border="bottom"
            clearable
            inputAlign="right"
            placeholder="关键词最多10个，用空格分隔"
            v-model="searchKey"></u-input>
        </veiw>

        <veiw class="mt-2.5 flex w-full items-center justify-start">
          <view class="mr-[10rpx] shrink-0 font-Regular text-[30rpx] font-normal leading-[42rpx] text-w-100"
            >票根状态</view
          >
          <view class="flex grow items-start justify-end">
            <view
              class="ml-[30rpx] box-border h-[56rpx] w-[120rpx] rounded-[10rpx] border-[2rpx] border-solid text-center font-Regular text-[24rpx] font-normal leading-[52rpx]"
              :class="selType.includes(i.id) ? 'border-[#9C75D3] text-[#9C75D3]' : 'border-w-50 text-w-50'"
              :key="index"
              @click="hanldeSwitchStatus(i.id)"
              v-for="(i, index) in typeList"
              >{{ i.name }}</view
            >
          </view>
        </veiw>

        <view class="mt-[30rpx] box-border flex w-full flex-wrap items-start justify-start">
          <view
            class="mPatch1 relative mb-[30rpx] mr-2.5 h-[160rpx] w-[160rpx] rounded-[16rpx]"
            :key="index"
            v-for="(i, index) in formData.userTicketGroupList">
            <image class="blcok h-full w-full rounded-[16rpx]" :src="$picFormat(i.image)" mode="aspectFill" />
          </view>
        </view>
      </view>

      <view
        class="mt-[30rpx] box-border flex w-full flex-wrap items-start justify-start pl-3 pr-3"
        v-if="addType === 1">
        <view
          class="mPatch1 relative mb-[30rpx] mr-2.5 h-[160rpx] w-[160rpx] rounded-[16rpx]"
          :key="index"
          v-for="(i, index) in formData.userTicketGroupList">
          <image class="blcok h-full w-full rounded-[16rpx]" :src="$picFormat(i.image)" mode="aspectFill" />

          <image
            class="absolute left-[-6rpx] top-[-10rpx] z-10 block h-5 w-5"
            :src="$iconFormat('icon/close5.svg')"
            @click="handleDelTicket(i, index)"
            mode="scaleToFill" />
        </view>

        <view
          class="mb-[30rpx] flex h-[160rpx] w-[160rpx] flex-col items-center justify-center rounded-[20rpx] bg-w-20"
          @click="handleAddTicket">
          <u-icon name="plus" color="#ffffff" size="54rpx"></u-icon>
          <text class="mt-2 font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">上传票根</text>
        </view>
      </view>

      <!-- 保存 -->
      <customBtn class="!fixed bottom-10 left-0 right-0 m-auto" @tap="handleSave" text="保存" />
    </template>

    <!-- 添加票根 -->
    <template v-else-if="addTicket">
      <view class="box-border flex w-full flex-wrap items-start justify-start pb-[160rpx] pl-3 pr-3 pt-2.5">
        <view
          class="mPatch2 relative mb-[30rpx] mr-2.5 h-[220rpx] w-[220rpx] rounded-[16rpx]"
          :key="index"
          @click="i.selStatus = !i.selStatus"
          v-for="(i, index) in ticketList">
          <image class="blcok h-full w-full rounded-[16rpx]" :src="$picFormat(i.image)" mode="aspectFill" />

          <image
            class="absolute right-[10rpx] top-[10rpx] block h-5 w-5"
            :src="$iconFormat('icon/radioOn.svg')"
            mode="scaleToFill"
            v-if="i.selStatus" />
          <image
            class="absolute right-[10rpx] top-[10rpx] block h-5 w-5"
            :src="$iconFormat('icon/radioOff.svg')"
            mode="scaleToFill"
            v-else />
        </view>
      </view>

      <!-- 保存 -->
      <customBtn class="!fixed bottom-10 left-0 right-0 m-auto" @tap="handleSureAdd" text="确定" />
    </template>
  </view>

  <!-- 提示 -->
  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $electronicTicketByUserId, $ticketGroupAdd, $ticketGroupDetail, $ticketGroupUpdate } from '@/api/ticket'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $picFormat, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy

  const { userInfo, titleStyle1 } = storeToRefs(useGlobalStore())

  const addType = ref(1) // 添加类型
  const addTicket = ref(false) // 添加电子票

  const searchKey = ref('') // 搜索关键字
  const selType = ref<any>([1, 2, 4]) // 票根状态
  const typeList = ref([
    { id: 1, name: '未观看' },
    { id: 2, name: '已观看' },
    { id: 4, name: '非卖品' },
  ])

  const ticketList = ref<any>([]) // 用户电子票列表

  const formData = reactive<any>({
    name: '',
    userTicketGroupList: [],
  })

  onLoad(async (params: any) => {
    if (params.name) formData.name = params.name
    if (params.id) {
      formData.id = params.id
      formData.userTicketGroupList = (await $ticketGroupDetail({ id: params.id })).data
    }

    handleGetTicketList()
  })

  /* 切换添加类型 */
  const handleSwitchType = (type: number) => {
    switch (type) {
      case 0:
        addType.value = 0
        searchKey.value = ''
        selType.value = [1, 2, 4]
        ticketList.value = []
        formData.userTicketGroupList = []
        break
      case 1:
        addType.value = 1
        searchKey.value = ''
        selType.value = [1, 2, 4]
        ticketList.value = []
        formData.userTicketGroupList = []
        handleGetTicketList()
        break
    }
  }

  /* 获取电子票列表 */
  const handleGetTicketList = () => {
    let status: number = 0
    selType.value.map((i: any) => {
      status += i
    })

    let keyNameStr: any = ''
    if (searchKey.value) {
      keyNameStr = searchKey.value.replace(/\s/g, ',')
      keyNameStr = keyNameStr.split(',').splice(0, 10)
    }

    $electronicTicketByUserId({
      userId: userInfo.value.id,
      status,
      keyNameStr: searchKey.value ? keyNameStr.join(',') : undefined,
    }).then((res: any) => {
      if (addType.value === 0) {
        let temp: any = []

        res.data?.map((i: any) => {
          temp.push({
            userReceivingRecordsId: i.userReceivingRecordsId,
            repertoireId: i.repertoireId,
            image: i.image,
          })
        })

        formData.userTicketGroupList = temp
      } else if (addType.value === 1) {
        res.data?.map((i: any) => {
          i.selStatus = false

          formData.userTicketGroupList.map((j: any) => {
            if (i.userReceivingRecordsId === j.userReceivingRecordsId) i.selStatus = true
          })
        })

        ticketList.value = res.data || []
      }
    })
  }

  /* 添加电子票 */
  const handleAddTicket = () => {
    addTicket.value = true
  }

  /* 关闭添加电子票 */
  const handleCloseAddTicket = () => {
    addTicket.value = false
  }

  /* 确认添加电子票 */
  const handleSureAdd = () => {
    let temp: any = []

    ticketList.value.map((i: any) => {
      if (i.selStatus) {
        temp.push({
          userReceivingRecordsId: i.userReceivingRecordsId,
          repertoireId: i.repertoireId,
          image: i.image,
        })
      }
    })

    formData.userTicketGroupList = temp

    handleCloseAddTicket()
  }

  /* 删除选中的电子票 */
  const handleDelTicket = (i: any, index: number) => {
    formData.userTicketGroupList.splice(index, 1)

    ticketList.value.map((j: any) => {
      if (j.userReceivingRecordsId === i.userReceivingRecordsId) j.selStatus = false
    })
  }

  /* 切换票根状态 */
  const hanldeSwitchStatus = (id: number) => {
    if (selType.value.includes(id)) {
      selType.value.splice(selType.value.indexOf(id), 1)
    } else {
      selType.value.push(id)
    }

    handleGetTicketList()
  }

  /* 保存分类 */
  const handleSave = () => {
    if (!formData.name) {
      $toast(app, '分组名称不能为空')
      return
    }

    if (!formData.userTicketGroupList.length) {
      $toast(app, '票根不能为空')
      return
    }

    if (formData.id) {
      $ticketGroupUpdate({
        id: formData.id,
        name: formData.name,
        type: 1,
        userId: userInfo.value.id,
        userTicketGroupList: formData.userTicketGroupList,
      }).then((res: any) => {
        uni.$emit('updateGroupList')
        $back()
      })
    } else {
      $ticketGroupAdd({
        name: formData.name,
        type: 1,
        userId: userInfo.value.id,
        userTicketGroupList: formData.userTicketGroupList,
      }).then((res: any) => {
        uni.$emit('updateGroupList')
        $back()
      })
    }
  }
</script>

<style lang="scss" scoped>
  .nameBox {
    &:deep(.u-input__content) {
      .u-input__content__field-wrapper {
        .u-input__content__field-wrapper__field {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 30rpx !important;
          font-weight: 400 !important;
          color: #fff !important;
        }

        .input-placeholder {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 30rpx !important;
          font-weight: 400 !important;
          color: rgb(255 255 255 / 60%) !important;
        }
      }
    }
  }

  .keyword {
    padding: 0 20rpx !important;
    border-color: rgb(151 151 151 / 30%) !important;

    &:deep(.u-input__content) {
      padding-bottom: 12rpx;

      .u-input__content__field-wrapper {
        .u-input__content__field-wrapper__field {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 26rpx !important;
          font-weight: 400 !important;
          color: #fff !important;
        }

        .input-placeholder {
          font-family: PingFangSC, 'PingFang SC' !important;
          font-size: 26rpx !important;
          font-weight: 400 !important;
          color: rgb(255 255 255 / 60%) !important;
        }
      }
    }
  }

  .mPatch1 {
    &:nth-child(4n) {
      margin-right: 0;
    }
  }

  .mPatch2 {
    &:nth-child(3n) {
      margin-right: 0;
    }
  }
</style>
