<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="分组管理"
      :titleStyle="titleStyle1"
      @leftClick="$back"
      bgColor="#282142"
      leftIconColor="#FFFFFF"
      placeholder />
    <view class="w-full border-b border-solid border-w-10"></view>

    <view
      class="ml-auto mr-auto mt-2.5 box-border flex h-[128rpx] w-[702rpx] shrink-0 flex-col items-center justify-center rounded-[20rpx] border-[2rpx] border-dashed border-[#9C75D3] bg-purpleBg"
      @click="$push({ name: 'TicketGroupEdit' })"
      v-if="addCount > 0">
      <view class="flex items-center justify-center">
        <u-icon name="plus" color="#C59BFF" size="20rpx"></u-icon>
        <text class="ml-1 font-Regular text-[32rpx] font-normal leading-[44rpx] text-[#C59BFF]">创建新分组</text>
      </view>
      <view class="mt-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-50"
        >还可创建{{ addCount }}个分组</view
      >
    </view>

    <mescroll-uni
      class="mt-[50rpx] block h-0 w-full grow"
      :down="downOpt1"
      :fixed="false"
      :up="upOpt1"
      @down="upCallback"
      @init="mescrollInit">
      <view class="mb-[60rpx] box-border w-full pl-3 pr-3" :key="index" v-for="(i, index) in list">
        <view class="flex items-center justify-start">
          <view class="mr-[10rpx] font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{
            i.ticketGroupName
          }}</view>
          <view
            class="h-4 w-[100rpx] bg-w-20 text-center font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-50"
            v-if="i.type === 0"
            >系统分组</view
          >
          <view class="ml-[22rpx] font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-50"
            >共{{ i.number }}个剧目</view
          >
        </view>

        <view class="mt-2.5 flex items-start justify-between">
          <view class="flex items-start justify-start">
            <template :key="sonIndex" v-for="(j, sonIndex) in i.electronicTicketImageList">
              <image
                class="mr-2.5 block h-[160rpx] w-[160rpx] rounded-[16rpx] bg-b-50"
                :src="$picFormat(j)"
                mode="aspectFill"
                v-if="sonIndex < 3" />
            </template>
          </view>

          <view class="w-[128rpx]" v-if="i.type !== 0">
            <view
              class="mb-[18rpx] flex h-[64rpx] w-[128rpx] items-center justify-center rounded-[10rpx] bg-w-20"
              @click="handleEditGroup(i)">
              <image class="mr-1 block h-5 w-5" :src="$iconFormat('icon/edit.svg')" mode="scaleToFill" />
              <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-100">编辑</text>
            </view>

            <view
              class="flex h-[64rpx] w-[128rpx] items-center justify-center rounded-[10rpx] bg-w-20"
              @click="handleDelGroup(i)">
              <image class="mr-1 block h-5 w-5" :src="$iconFormat('icon/del2.svg')" mode="scaleToFill" />
              <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#EC2A2A]">删除</text>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>
  </view>

  <u-modal
    class="modalWrap"
    title="确定删除该分组？"
    :show="delModalController"
    @cancel="delModalController = false"
    @confirm="handleCheckDel"
    content="删除该分组将删除分组内所有添加过的剧目票根"
    showCancelButton
    width="640rpx"></u-modal>

  <!-- 提示 -->
  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $ticketGroupDelete, $ticketGroupList } from '@/api/ticket'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $back, $iconFormat, $picFormat, $push, $toast } from '@/utils/methods'

  const { titleStyle1 } = storeToRefs(useGlobalStore())

  const app: any = getCurrentInstance()?.proxy
  const { downOpt1, upOpt1 } = storeToRefs(useGlobalStore())
  const { mescrollInit } = useMescroll(onPageScroll, onReachBottom)
  const mescrollObj = ref() // 滚动obj

  /* 可以添加的榜单数量 */
  const addCount = computed(() => {
    let temp = 5

    list.value?.map((i: any) => {
      if (i.type === 1) temp--
    })

    return temp
  })

  const list = ref<any>([])

  const delModalController = ref(false) // 删除弹窗控制器
  const delId = ref()

  onLoad(() => {
    uni.$on('updateGroupList', () => {
      mescrollObj.value.triggerDownScroll()
    })
  })

  /* 数据加载 */
  const upCallback = async (mescroll: any) => {
    mescrollObj.value = mescroll

    $ticketGroupList()
      .then((res: any) => {
        list.value = res.data

        mescroll.endSuccess(1, false)
      })
      .catch((err: any) => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 编辑分组 */
  const handleEditGroup = (i: any) => {
    $push({ name: 'TicketGroupEdit', params: { id: i.ticketGroupId, name: i.ticketGroupName } })
  }

  /* 删除分组弹窗 */
  const handleDelGroup = (i: any) => {
    delId.value = i.ticketGroupId
    delModalController.value = true
  }

  /* 确认删除分组 */
  const handleCheckDel = () => {
    $ticketGroupDelete(delId.value).then((res: any) => {
      delModalController.value = false
      $toast(app, '删除成功')

      mescrollObj.value.triggerDownScroll()
    })
  }
</script>

<style lang="scss" scoped>
  .modalWrap {
    &:deep(.u-popup__content) {
      background: none;
    }

    &:deep(.u-modal) {
      background: #1e1e1e;
      border-radius: 24rpx;

      .u-modal__title {
        padding-top: 64rpx !important;
        font-family: PingFangSC, 'PingFang SC';
        font-size: 34rpx;
        font-weight: 500;
        line-height: 48rpx;
        color: #fff;
        text-align: center;
      }

      .u-modal__content {
        padding: 32rpx 48rpx 48rpx;

        .u-modal__content__text {
          font-family: PingFangSC, 'PingFang SC';
          font-size: 34rpx;
          font-weight: 400;
          line-height: 48rpx;
          color: rgb(255 255 255 / 60%);
          text-align: center;
        }
      }

      .u-line {
        border-color: rgb(255 255 255 / 10%) !important;
      }

      .u-modal__button-group {
        height: 110rpx;

        .u-modal__button-group__wrapper--cancel {
          height: 100%;

          &:active,
          &:focus,
          &:hover {
            background: none !important;
          }

          .u-modal__button-group__wrapper__text {
            font-family: PingFangSC, 'PingFang SC';
            font-size: 34rpx;
            font-weight: 500;
            line-height: 110rpx;
            color: rgb(255 255 255 / 50%) !important;
            letter-spacing: 1px;
          }
        }

        .u-modal__button-group__wrapper--confirm {
          height: 100%;

          &:active,
          &:focus,
          &:hover {
            background: none !important;
          }

          .u-modal__button-group__wrapper__text {
            font-family: PingFangSC, 'PingFang SC';
            font-size: 34rpx;
            font-weight: 500;
            line-height: 110rpx;
            color: #9c75d3 !important;
            letter-spacing: 1px;
          }
        }
      }
    }
  }
</style>
