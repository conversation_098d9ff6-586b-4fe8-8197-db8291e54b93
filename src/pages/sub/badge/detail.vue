<template>
  <view class="pageWrap min-h-screen w-screen bg-deepbg bg-fullAuto bg-no-repeat pb-[100rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder />

    <view class="relative mt-5 h-[540rpx] w-full">
      <model class="modelBox absolute left-0 top-[160rpx]" :url="detail.model" height="640rpx" v-if="detail.model" />
    </view>

    <view class="mb-2.5 w-full text-center font-Medium text-[40rpx] font-medium leading-[56rpx] text-w-100"
      >{{ detail.souvenirBadgeName || '-' }}徽章</view
    >
    <view class="mb-2.5 w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
      >专属编号：{{ detail.sendId || '铸造中' }}</view
    >
    <view class="mb-2.5 w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
      >发行总量：{{ detail.souvenirBadgeIssuedQuantity || '-' }}</view
    >
    <view class="mb-2.5 w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-60"
      >发行单位：{{ detail.issuerName || '-' }}</view
    >
    <view class="w-full text-center font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-40"
      >{{ detail.createTime || '-' }}获得</view
    >

    <view class="mt-[120rpx] flex items-center justify-center">
      <customBtn
        class="mr-3.5"
        type="3"
        @tap="$push({ name: 'BadgeShare', params: { id } })"
        text="分享一下"
        width="260" />
      <customBtn
        :text="detail.adorn > 0 ? '取消展示' : '纪念室展示'"
        :type="detail.adorn > 0 ? 4 : 1"
        @tap="handleShow"
        width="260" />
    </view>
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $souvenirDetails, $updateAdornStatus } from '@/api/badge'
  import customBtn from '@/components/Btn/CustomBtn.vue'
  import model from '@/components/Model.vue'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $picFormat, $push } from '@/utils/methods'
  import dayjs from 'dayjs'

  const globalStore = useGlobalStore()

  const id = ref() // 电子票id
  const detail = ref<any>('')

  onLoad((params: any) => {
    id.value = params.id

    handleGetDetail()
  })

  /* 获取纪念勋章详情 */
  const handleGetDetail = () => {
    $souvenirDetails(id.value).then((res: any) => {
      if (res.data.model) res.data.model = $picFormat(res.data.model)
      if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY年MM月DD日')

      detail.value = res.data
    })
  }

  /* 画廊展示 */
  const handleShow = () => {
    let data: any

    if (detail.value.adorn > 0) {
      data = {
        id: detail.value.adorn,
      }
    } else {
      data = {
        userReceivingRecordsId: Number(id.value),
        badgeType: 3,
        upgradeStatus: 0,
        adornImage: detail.value.coverPicture,
      }
    }

    $updateAdornStatus(data).then((res: any) => {
      handleGetDetail()
      uni.$emit('updateBadgeList')
    })
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/badgeBg.webp');

    .modelBox {
      @apply top-[-160rpx];
    }

    .infoBox {
      // background-image: url($icon + 'background/infoDailog2.webp');
    }
  }
</style>
