<template>
  <view
    class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar
      class="w-full shrink-0"
      title="纪念室"
      :titleStyle="titleStyle2"
      @leftClick="$back"
      bgColor="transparent"
      leftIconColor="#FFFFFF"
      titleWidth="574rpx" />
    <!-- 头像墙 -->
    <view class="relative h-[90vw] w-full shrink-0">
      <!-- 1 -->
      <view
        class="absolute left-[34.1248vw] top-[26.66vw] h-[240rpx] w-[240rpx]"
        @tap="$push({ name: 'BadgeDetail', params: { id: detail.id, relationId: detail.relationId } })">
        <!-- <model class="modelBox absolute left-0 top-[160rpx]" :url="detail.model" height="640rpx" v-if="detail.model" /> -->
        <u-image
          :src="$picFormat(detail.adornImage)"
          height="240rpx"
          mode="aspectFill"
          v-if="detail"
          width="240rpx"></u-image>
      </view>
    </view>

    <!-- 筛选 -->
    <view
      class="filterBar relative z-10 flex h-[84rpx] w-full items-center justify-start bg-fullAuto bg-no-repeat pl-3 pr-3">
      <!-- 搜索 -->
      <view
        class="searchWrap flex h-8 w-[410rpx] grow items-center justify-start rounded-full border-[2rpx] border-solid border-[#C5C5C5] pl-2.5 pr-2.5">
        <image class="mr-[6rpx] h-[34rpx] w-[34rpx] shrink-0" :src="$iconFormat('icon/search.svg')" />
        <u-input
          class="searchBox"
          @clear="handleSearch"
          @confirm="handleSearch"
          border="none"
          clearable
          placeholder="请输入关键字搜索  "
          v-model="keyword" />
      </view>
      <!-- 排序类型 -->
      <view
        class="relative ml-2.5 flex h-8 w-[188rpx] shrink-0 items-center justify-center rounded-full border-[2rpx] border-solid border-[#C5C5C5]"
        @tap="orderListController = !orderListController">
        <text class="w-[130rpx] text-center font-Regular text-[26rpx] font-normal leading-[32rpx] text-w-60">{{
          orderByTxt
        }}</text>
        <image class="mr-[-3rpx] block h-4 w-4 shrink-0" :src="$iconFormat('arrow/down3.svg')" mode="scaleToFill" />

        <u-transition :show="orderListController" mode="fade">
          <view
            class="absolute left-0 right-0 top-[80rpx] z-20 m-auto h-fit w-[160rpx] overflow-hidden rounded-[20rpx]">
            <view
              class="h-[60rpx] w-full bg-[#332F3B] text-center font-Regular text-[26rpx] font-normal leading-[60rpx] text-w-100"
              :key="index"
              @tap="handleSelOrderBy(item)"
              v-for="(item, index) in orderList"
              >{{ item.name }}</view
            >
          </view>
        </u-transition>
      </view>
      <!-- 列表样式切换 -->
      <view
        class="ml-2.5 flex h-8 w-8 shrink-0 items-center justify-center rounded-full border-[2rpx] border-solid border-[#C5C5C5]">
        <image
          class="block h-8 w-8"
          :src="$iconFormat('listType/list.svg')"
          @tap="listType = 2"
          mode="scaleToFill"
          v-if="listType == 1" />
        <image
          class="block h-8 w-8"
          :src="$iconFormat('listType/banner.svg')"
          @tap="listType = 1"
          mode="scaleToFill"
          v-else />
      </view>
    </view>

    <!-- 纪念徽章列表 -->
    <badgeList1 :isAsc="isAsc" :keyword="keyword" :orderBy="orderBy" ref="badgeList1Ref" v-if="listType === 1" />
    <badgeList2 :isAsc="isAsc" :keyword="keyword" :orderBy="orderBy" ref="badgeList2Ref" v-else-if="listType === 2" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import badgeList1 from './components/BadgeList1.vue'
  import badgeList2 from './components/BadgeList2.vue'
  import { $userAdornDigitalAvatar } from '@/api/avatar'
  import { $collectionListByPage } from '@/api/scan'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $picFormat, $push } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, titleStyle2 } = storeToRefs(useGlobalStore())

  const keyword = ref('') // 搜索关键字
  /* 排序字段列表 */
  const orderList = ref([
    { name: '按日期', string: 'time', isAsc: 'desc' },
    { name: '按剧场名称', string: 'theaterName', isAsc: 'asc' },
    { name: '按发行总量', string: 'no', isAsc: 'asc' },
    { name: '按城市', string: 'no', isAsc: 'asc' },
  ])
  const orderListController = ref(false) // 排序字段列表弹窗控制器
  const orderBy = ref('time') // 排序字段
  const orderByTxt = ref('按日期') // 排序字段文案
  const isAsc = ref('desc') // 排序顺序
  const listType = ref(1) // 列表模式

  const detail = ref<any>([]) // 纪念勋章
  const badgeList1Ref = ref()
  const badgeList2Ref = ref()

  onMounted(() => {
    uni.$on('updateBadgeList', handleGetAdorn)

    handleGetAdorn()
  })

  /* 获取展示纪念徽章 */
  const handleGetAdorn = () => {
    $userAdornDigitalAvatar(3).then((res: any) => {
      // let data: any = ''

      // if (res.data.rows && res.data.rows.length) {
      //   data = res.data.rows[0]
      // }

      detail.value = res.data[0]
    })
  }

  /* 搜索 */
  const handleSearch = () => {
    badgeList1Ref.value.handleRefresh()
    badgeList2Ref.value.handleRefresh()
  }

  /* 切换排序字段 */
  const handleSelOrderBy = (orderItem: any) => {
    orderBy.value = orderItem.string
    orderByTxt.value = orderItem.name

    badgeList1Ref.value.handleRefresh()
    badgeList2Ref.value.handleRefresh()
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/badge.webp');

    .modelBox {
      @apply top-[-160rpx];
    }

    .filterBar {
      background-image: url($icon + 'background/border.webp');
    }

    .borderWall {
      background-image: url($icon + 'background/border2.webp');
    }

    .avatarBox {
      background-image: url($icon + 'background/badgeWall.webp');

      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
</style>
