<template>
  <view class="pageWrap min-h-screen w-screen bg-deepbg bg-fullAuto bg-no-repeat pb-[180rpx]">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder />

    <view class="m-auto w-[680rpx]">
      <u-image :src="postImage" height="1186rpx" mode="aspectFill" width="680rpx"></u-image>
    </view>
  </view>

  <u-popup :closeOnClickOverlay="false" :overlay="false" :show="showMenu" round="30rpx">
    <view class="flex items-start justify-start pb-2.5 pl-[42rpx] pr-[42rpx] pt-2.5">
      <button class="btnDef mr-[80rpx] h-fit w-fit p-0" open-type="share">
        <view class="flex w-[104rpx] flex-col items-center justify-start">
          <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/wechat.svg')" mode="scaleToFill" />
          <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">微信好友</text>
        </view>
      </button>

      <view class="flex w-[104rpx] flex-col items-center justify-start" @click="handleSavePost">
        <image class="mb-2 block h-[60rpx] w-[60rpx]" :src="$iconFormat('icon/save.svg')" mode="scaleToFill" />
        <text class="font-Regular text-[26rpx] font-normal leading-[36rpx] text-[#242424]">保存图片</text>
      </view>
    </view>
  </u-popup>

  <l-painter
    :pixelRatio="2"
    @fail="handleGetFail"
    @success="handleGetPost"
    css="width: 680rpx; height: 1186rpx"
    hidden
    isCanvasToTempFilePath
    pathType="url"
    v-if="badgeDetail.modelUrl">
    <l-painter-view
      :css="`width: 680rpx; height: 1000rpx; background-image: url(${$iconFormat(
        'background/badgeBg2.webp'
      )}); border-radius: 20rpx;`">
      <!-- 基本信息 -->
      <l-painter-view css="width: 680rpx; padding: 30rpx 40rpx 176rpx;">
        <!-- 用户头像 -->
        <l-painter-image
          :src="userInfo.avatarUrl"
          css="width: 80rpx; height: 80rpx; object-fit: cover; margin: 0 16rpx 0 0; border-radius: 50%;" />

        <!-- 用户昵称 -->
        <l-painter-view css="display: inline-block; width: 406rpx; margin: 18rpx 16rpx 0 0;">
          <l-painter-text
            :text="userInfo.name"
            css="color: #FFFFFF; line-height: 44rpx; font-weight: normal; font-size: 32rpx; margin: 0 0 2rpx 0; display: block;" />
        </l-painter-view>

        <!-- 剧场二维码 -->
        <l-painter-image :src="badgeDetail.qrCode" css="width: 80rpx; height: 80rpx; object-fit: cover;" />
      </l-painter-view>

      <!-- 勋章封面 -->
      <l-painter-image
        :src="badgeDetail.modelUrl"
        css="width: 424rpx; height: 424rpx; object-fit: cover; margin: 0 auto 152rpx;" />

      <!-- 剧目信息 -->
      <l-painter-text
        :text="`${badgeDetail.theaterName || '-'}`"
        css="width: 100%; text-align: center; color: #FFFFFF; line-height: 50rpx; font-weight: normal; font-size: 36rpx; margin: 0 0 10rpx 0;" />

      <l-painter-text
        :text="`获得时间：${badgeDetail.createTime || '未知'}`"
        css="width: 100%; text-align: center; color: rgba(255, 255, 255, 0.7); line-height: 34rpx; font-weight: normal; font-size: 24rpx;" />
    </l-painter-view>

    <!-- 小程序二维码 -->
    <l-painter-image
      :src="$iconFormat('qrcode.png')"
      css="width: 120rpx; height: 120rpx; object-fit: cover; margin: 20rpx auto 12rpx;" />

    <!-- 提示 -->
    <l-painter-text
      :text="`长按识别二维码，来${wechatInfo.wechatName}看演出`"
      css="width: 100%; text-align: center; color: #FFFFFF; line-height: 34rpx; font-weight: normal; font-size: 24rpx;" />
  </l-painter>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
  import { $souvenirDetails } from '@/api/badge'
  import { $getWechatSetting } from '@/api/base'
  import network from '@/components/Network.vue'
  import { useGlobalStore } from '@/stores/global'
  import { $back, $iconFormat, $loading, $picFormat, $savePic, $toast } from '@/utils/methods'

  const app: any = getCurrentInstance()?.proxy
  const { userInfo } = storeToRefs(useGlobalStore())

  const id = ref() // 数字头像id
  const badgeDetail = ref<any>('') // 纪念徽章详情
  const postImage = ref('') // 海报地址
  const wechatInfo = ref<any>('')
  const showMenu = ref(true)

  onLoad((params: any) => {
    id.value = params.id

    /* 获取数字头像详情 */
    $souvenirDetails(id.value).then((res: any) => {
      $loading('生成海报中')

      if (res.data.coverPicture) res.data.modelUrl = $picFormat(res.data.coverPicture)
      if (res.data.qrCode) res.data.qrCode = $picFormat(res.data.qrCode)
      badgeDetail.value = res.data
    })

    $getWechatSetting().then((res: any) => {
      wechatInfo.value = res.data
    })
  })

  /* 获取生成的海报 */
  const handleGetPost = (val: any) => {
    postImage.value = val

    uni.$u.mpShare.imageUrl = val

    uni.hideLoading()
  }

  /* 生成失败 */
  const handleGetFail = (err: any) => {
    $toast(app, '生成海报失败')
    uni.hideLoading()
  }

  /* 保存海报 */
  const handleSavePost = () => {
    $savePic(app, postImage.value)
  }
</script>

<style lang="scss" scoped>
  .pageWrap {
    background-image: url($icon + 'background/avatarShare.webp');
  }
</style>
