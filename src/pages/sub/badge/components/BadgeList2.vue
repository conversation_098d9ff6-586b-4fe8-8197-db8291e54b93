<template>
  <mescroll-uni
    class="block h-0 w-screen grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="borderWall w-[748rpx] bg-fullAuto bg-no-repeat pb-[100rpx] pt-[10rpx]">
      <!-- 徽章 -->
      <view
        class="relative flex w-full items-start justify-start pl-2.5 pr-2.5"
        :key="index"
        :style="{ zIndex: list.length - index }"
        @tap="$push({ name: 'BadgeDetail', params: { id: i.id, relationId: i.relationId } })"
        v-for="(i, index) in list">
        <view class="avatarBox relative mr-2.5 h-[208rpx] w-[208rpx] bg-fullAuto bg-no-repeat" :style="{ zIndex: 2 }">
          <u-image
            class="avatar absolute"
            :src="$picFormat(i.coverPicture)"
            height="108rpx"
            mode="aspectFill"
            width="108rpx"></u-image>
        </view>

        <view class="grow pt-3" :style="{ zIndex: 2 }">
          <view class="mb-[10rpx] flex w-full items-center justify-between">
            <view class="font-Regular text-[28rpx] font-normal leading-[40rpx] text-w-100"
              >{{ i.souvenirBadgeName || '-' }}徽章</view
            >
            <view
              class="mr-3 rounded-full bg-w-50 pl-[10rpx] pr-[10rpx] font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-100"
              >{{ i.cityName || '-' }}</view
            >
          </view>

          <view class="mb-[10rpx] w-full font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
            >发行总量：{{ i.souvenirBadgeIssuedQuantity || '-' }}</view
          >
          <view class="w-full font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60"
            >{{ i.createTime || '-' }}获得</view
          >
        </view>

        <!-- 隔板 -->
        <image
          class="absolute left-0 right-0 block h-[60rpx] w-[748rpx]"
          :src="$iconFormat('background/border2.webp')"
          :style="{ top: '156rpx', zIndex: 1 }"
          mode="scaleToFill" />
      </view>

      <view
        class="relative w-full pl-2.5 pr-2.5"
        :key="i"
        :style="{ zIndex: list.length - index }"
        v-for="(i, index) in 3 - list.length">
        <!-- 隔板 -->
        <image
          class="absolute left-0 right-0 block h-[60rpx] w-[748rpx]"
          :src="$iconFormat('background/border2.webp')"
          :style="{ top: '156rpx', zIndex: 1 }"
          mode="scaleToFill"
      /></view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $collectionListByPage } from '@/api/scan'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'
  import dayjs from 'dayjs'

  const props = defineProps({
    keyword: { type: String, default: '' },
    orderBy: { type: String, default: '' },
    isAsc: { type: String, default: '' },
  })

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const upOpt = ref({ ...upOpt3.value, noMoreSize: 99999 })
  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 数字头像列表

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    // $collectionListByPage
    $collectionListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      badgeType: 3,
      userId: userInfo.value.id,
      keyword: props.keyword || undefined,
      orderByColumn: props.orderBy ? props.orderBy + ' ' + props.isAsc + ',id' : undefined,
      // isAsc: props.isAsc,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        curPageData.map((i: any) => {
          if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY年MM月DD日')
        })

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleRefresh = () => {
    mescrollObj.value.resetUpScroll()
  }

  defineExpose({ handleRefresh })
</script>

<style lang="scss" scoped>
  .avatarBox {
    background-image: url($icon + 'background/badgeWall.webp');

    .avatar {
      top: 36rpx;
      left: 56rpx;
    }

    &:nth-child(5n) {
      margin-right: 0;
    }
  }
</style>
