<template>
  <mescroll-uni
    class="block h-0 w-screen grow"
    :down="downOpt1"
    :fixed="false"
    :up="upOpt"
    @down="downCallback"
    @init="mescrollInit"
    @topclick="$topclick"
    @up="upCallback">
    <view class="borderWall w-full bg-fullAuto bg-no-repeat pb-[100rpx] pt-[10rpx]">
      <view
        class="relative flex w-full items-start justify-start pl-[48rpx] pr-[48rpx]"
        :key="index"
        :style="{ zIndex: groupList.length - index }"
        v-for="(i, index) in groupList">
        <!-- 徽章 -->
        <view
          class="avatarBox relative mr-[12rpx] h-[208rpx] w-[208rpx] bg-fullAuto bg-no-repeat"
          :key="jIndex"
          :style="{ zIndex: 2 }"
          @tap="$push({ name: 'BadgeDetail', params: { id: j.id, relationId: j.relationId } })"
          v-for="(j, jIndex) in i">
          <u-image
            class="avatar absolute"
            :src="$picFormat(j.coverPicture)"
            height="108rpx"
            mode="aspectFill"
            width="108rpx"></u-image>
        </view>

        <view class="h-[208rpx] w-[208rpx]" v-if="!i || !i.length"></view>

        <!-- 隔板 -->
        <image
          class="absolute left-0 right-0 block h-[60rpx] w-[748rpx]"
          :src="$iconFormat('background/border2.webp')"
          :style="{ top: '156rpx', zIndex: 1 }"
          mode="scaleToFill" />
      </view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts">
  export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
  import { $collectionListByPage } from '@/api/scan'
  import { useGlobalStore } from '@/stores/global'
  import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
  import { $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'

  const props = defineProps({
    keyword: { type: String, default: '' },
    orderBy: { type: String, default: '' },
    isAsc: { type: String, default: '' },
  })

  const app: any = getCurrentInstance()?.proxy
  const { userInfo, downOpt1, upOpt4 } = storeToRefs(useGlobalStore())
  const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

  const upOpt = ref({ ...upOpt4.value, noMoreSize: 99999 })
  const mescrollObj = ref() // 滚动obj

  const list = ref<any>([]) // 数字头像列表
  const groupList = ref<any>([]) // 分组头像

  /* 数据加载 */
  const upCallback = (mescroll: any) => {
    mescrollObj.value = mescroll

    // $collectionListByPage
    $collectionListByPage({
      pageNum: mescroll.num,
      pageSize: mescroll.size,
      badgeType: 3,
      userId: userInfo.value.id,
      keyword: props.keyword || undefined,
      orderByColumn: props.orderBy ? props.orderBy + ' ' + props.isAsc + ',id' : undefined,
      // isAsc: props.isAsc,
    })
      .then((res: any) => {
        const curPageData = res.data.rows || [] // 当前页数据

        if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

        list.value = list.value.concat(curPageData) //追加新数据

        let arr: any = [],
          oindex = 0
        list.value.map((i: any, index: number) => {
          if (arr[oindex]) arr[oindex].push(i)
          else arr[oindex] = [i]
          if ((index + 1) % 3 === 0) oindex++
        })

        if (arr.length < 4) {
          for (let index = 0; index < 4 - arr.length; index++) {
            arr.push([])
          }
        }

        groupList.value = arr

        mescroll.endBySize(curPageData.length, res.data.total)
      })
      .catch(() => {
        mescroll.endErr() // 请求失败, 结束加载
      })
  }

  /* 搜索 */
  const handleRefresh = () => {
    mescrollObj.value.resetUpScroll()
  }

  defineExpose({ handleRefresh })
</script>

<style lang="scss" scoped>
  .avatarBox {
    background-image: url($icon + 'background/badgeWall.webp');

    .avatar {
      top: 36rpx;
      left: 56rpx;
    }

    &:nth-child(3n) {
      margin-right: 0;
    }
  }
</style>
