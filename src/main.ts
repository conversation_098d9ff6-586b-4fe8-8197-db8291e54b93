import App from './App.vue'
import { setupRouter } from '@/router'
import uviewPlus from '@/uni_modules/uview-plus'
import mpShare from '@/utils/mpShare'
import setHttpConfig from '@/utils/request'
import * as dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import timezone from 'dayjs/plugin/timezone'
import { createPinia } from 'pinia'
import { createSSRApp } from 'vue'

export function createApp() {
  const app = createSSRApp(App)

  app.mixin(mpShare)

  dayjs.locale('zh-cn')
  dayjs.extend(timezone)
  dayjs.tz.setDefault('Asia/Shanghai')

  setupRouter(app)

  app.use(createPinia())

  app.use(uviewPlus)
  setHttpConfig()

  return { app }
}
