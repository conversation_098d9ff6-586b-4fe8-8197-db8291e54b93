import { useGlobalStore } from '@/stores/global'
import { $matchRoute, $push, $tab } from '@/utils/methods'
import uniCrazyRouter, { afterEach, afterNotNext, beforeEach, onError } from 'uni-crazy-router'
import type { App } from 'vue'

export function setupRouter(app: App<Element>) {
  // 接收vue3的实例，并注册uni-crazy-router
  app.use(uniCrazyRouter)

  beforeEach(async (to: any, from: any, next: any) => {
    // 逻辑代码
    const globalStore: any = useGlobalStore()
    const fromObj = $matchRoute(from.url)
    const toObj = $matchRoute(to.url)

    if (toObj.needLogin && !globalStore.token && toObj.name != 'Login' && fromObj.name != 'Login') {
      /* 未登录 */
      afterNotNext(() => {
        $push({ name: 'Login' })
      })
      return
    } else if (globalStore.token && toObj.name == 'Login') {
      /* 已登录 */
      afterNotNext(() => {
        $tab({ name: 'Index' })
      })
      return
    } else if (!globalStore.token && toObj.name == 'Login' && fromObj.name == 'Login') {
      /* 已经在登录页 */
      return
    } else if (
      toObj.name === fromObj.name &&
      toObj.name !== 'RepertoireDetail' &&
      fromObj.name !== 'RepertoireDetail'
    ) {
      /* 已经在当前页面了 */
      return
    }

    next()
  })

  afterEach(async (to, from) => {
    // 逻辑代码
  })

  onError((to, from) => {
    // 逻辑代码
  })
}
