.formWrap {
  width: 100%;

  .u-form-item {
    width: 100%;
    margin-bottom: 14rpx;

    &__body {
      padding: 20rpx 0 !important;

      /* label */
      &__left {
        flex-shrink: 0;
        width: fit-content !important;
        margin-right: 20rpx;

        &__content {
          padding: 0;

          &__label {
            width: fit-content;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 26rpx !important;
            font-weight: 400;
            line-height: 36rpx;
            color: rgb(255 255 255 / 80%) !important;
          }
        }
      }

      /* value */
      &__right {
        &__content {
          &__slot {
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;

            .u-input {
              &__content {
                &__field-wrapper {
                  &__field {
                    height: 42rpx;
                    font-family: PingFangSC-Medium, 'PingFang SC';
                    font-size: 30rpx !important;
                    font-weight: 500;
                    line-height: 42rpx !important;
                    color: #fff !important;
                  }

                  .input-placeholder {
                    font-family: PingFangSC-Regular, 'PingFang SC';
                    font-size: 30rpx;
                    font-weight: 400;
                    line-height: 42rpx;
                    color: rgb(255 255 255 / 40%) !important;
                  }
                }
              }
            }

            .u-textarea {
              padding: 0;
              background: transparent;

              &__field {
                height: 26rpx;
                font-family: PingFangSC-Medium, 'PingFang SC';
                font-size: 26rpx !important;
                font-weight: 500;
                line-height: 36rpx !important;
                color: #fff !important;
              }

              .input-placeholder {
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 26rpx;
                font-weight: 400;
                line-height: 36rpx !important;
                color: rgb(255 255 255 / 40%) !important;
              }
            }

            .commentBox {
              &.u-textarea {
                padding: 20rpx 20rpx 50rpx;

                .u-textarea__field {
                  font-family: PingFangSC-Regular, 'PingFang SC';
                  font-size: 26rpx !important;
                  font-weight: 400;
                  line-height: 36rpx !important;
                  color: #fff !important;
                  resize: none !important;
                }

                .input-placeholder {
                  font-family: PingFangSC-Regular, 'PingFang SC';
                  font-size: 26rpx;
                  font-weight: 400;
                  line-height: 36rpx !important;
                  color: rgb(255 255 255 / 40%) !important;
                }

                .u-textarea__count {
                  font-family: PingFangSC-Regular, 'PingFang SC';
                  font-size: 24rpx;
                  font-weight: 400;
                  line-height: 34rpx;
                  color: rgb(255 255 255 / 30%);
                  background: transparent !important;
                }
              }
            }

            .smallPatch {
              &.u-input {
                .u-input__content {
                  &__field-wrapper {
                    &__field {
                      height: 36rpx;
                      font-family: PingFangSC-Regular, 'PingFang SC';
                      font-size: 26rpx !important;
                      font-weight: 400;
                      line-height: 36rpx;
                      color: #fff !important;
                    }

                    .input-placeholder {
                      font-family: PingFangSC-Regular, 'PingFang SC';
                      font-size: 26rpx;
                      font-weight: 400;
                      line-height: 36rpx;
                      color: rgb(255 255 255 / 40%) !important;
                    }
                  }
                }
              }
            }

            .getCodeBtn {
              width: 200rpx;
              height: 64rpx;
              background: transparent;
              border: 2rpx solid #9c75d3;
              border-radius: 200rpx;

              .u-button__text {
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 28rpx;
                font-weight: 400;
                color: #9c75d3;
              }
            }

            .getCodeBtn2 {
              width: 200rpx;
              height: 64rpx;
              background: transparent;
              border: none;
              border-radius: 200rpx;

              .u-button__text {
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 28rpx;
                font-weight: 400;
                color: #9c75d3;
              }
            }
          }
        }
      }
    }

    &__body__right__message {
      margin-top: 10rpx;
    }

    &.borderPatch {
      border-bottom: 2rpx solid rgb(255 255 255 / 10%);
    }

    &.mbPatch {
      margin-bottom: 36rpx;
    }

    &.noPadding {
      .u-form-item__body {
        padding: 0 !important;
      }
    }

    &.flexpatch {
      .u-form-item__body {
        flex-direction: column !important;

        &__left {
          margin-bottom: 34rpx !important;
        }
      }
    }

    &.codeItem {
      margin-bottom: 120rpx;

      .u-line {
        border-color: rgb(255 255 255 / 10%) !important;
      }

      .u-form-item__body__right__message {
        margin-left: 0 !important;
        text-align: center;
      }
    }
  }
}
