/* AI剧目评论智能分析 */
export const $aiAnalyzeRepertoire = (data: any) => uni.$u.http.post('/ai/analyze/repertoire', data)

/* AI综合推荐分析 */
export const $aiAnalyzeRecommend = (data: any) => uni.$u.http.post('/ai/analyze/recommend', data)

/* 获取AI分析缓存结果 */
export const $aiAnalysisCache = (repertoireId: number, analysisType: string) =>
  uni.$u.http.get('/ai/analysis/cache', {
    params: { repertoireId, analysisType },
  })

/* 保存AI分析结果 */
export const $aiAnalysisSave = (data: any) => uni.$u.http.post('/ai/analysis/save', data)

/* 获取分析引用来源 */
export const $aiAnalysisReferences = (analysisId: number) =>
  uni.$u.http.get('/ai/analysis/references', { params: { analysisId } })

/* AI问答对话 */
export const $aiChat = (data: any) => uni.$u.http.post('/ai/chat', data)

/* 获取AI问答历史 */
export const $aiChatHistory = (userId: number, pageNum: number, pageSize: number) =>
  uni.$u.http.get('/ai/chat/history', { params: { userId, pageNum, pageSize } })

/* 清空AI问答历史 */
export const $aiChatHistoryClear = (userId: number) =>
  uni.$u.http.delete('/ai/chat/history/clear', { params: { userId } })
