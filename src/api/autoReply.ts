/* 查询剧目剧场自动回复表列表(分页) */
export const $autoReplyListByPage = (data: any) => uni.$u.http.post('/autoReply/listByPage', data)

/* 查询剧目剧场自动回复表列表 */
export const $autoReplyList = () => uni.$u.http.get('/autoReply/list')

/* 查询剧目剧场自动回复表详情 */
export const $autoReplyDetails = (id: number) => uni.$u.http.get('/autoReply/details', { params: { id } })

/* 添加剧目剧场自动回复表 */
export const $autoReplyAdd = (data: any) => uni.$u.http.post('/autoReply/add', data)

/* 修改剧目剧场自动回复表 */
export const $autoReplyUpdate = (data: any) => uni.$u.http.put('/autoReply/update', data)

/* 删除剧目剧场自动回复表 */
export const $autoReplyDelete = (ids: string) => uni.$u.http.delete(`/autoReply/delete/${ids}`)
