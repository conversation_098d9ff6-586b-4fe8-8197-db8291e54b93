/* 查询用户设置表详情 */
export const $userSetting = () => uni.$u.http.get('/userSetting/getUserSetting')

/* 添加用户设置表 */
export const $userSettingAdd = (data: any) => uni.$u.http.post('/userSetting/add', data)

/* 修改用户设置表 */
export const $userSettingUpdate = (data: any) => uni.$u.http.put('/userSetting/update', data)

/* 删除用户设置表 */
export const $userSettingDelete = (ids: string) => uni.$u.http.delete(`/userSetting/delete/${ids}`)
