/* 查询用户电子票 */
export const $electronicTicketByUserId = (data: any) =>
  uni.$u.http.post('/userReceivingRecords/findElectronicTicketByUserId', data)

/* 查询剧目电子票表详情 */
export const $ticketDetail = (id: number, relationId: number) =>
  uni.$u.http.get('/repertoireTicket/findRepertoireTicketInfo', { params: { id, relationId } })

/* 查询电子票分组列 */
export const $ticketGroupList = () => uni.$u.http.post('/ticketGroup/findTicketGroupList')

/* 查询电子票分组详情 */
export const $ticketGroupDetail = (data: any) => uni.$u.http.get('/ticketGroup/details', { params: data })

/* 查询用户电子票分组数量 */
export const $ticketGroupCount = (data: any) => uni.$u.http.get('/ticketGroup/findTicketGroupCount', { params: data })

/* 添加电子票分组 */
export const $ticketGroupAdd = (data: any) => uni.$u.http.post('/ticketGroup/add', data)

/* 修改电子票分组 */
export const $ticketGroupUpdate = (data: any) => uni.$u.http.put('/ticketGroup/update', data)

/* 删除电子票分组 */
export const $ticketGroupDelete = (id: any) => uni.$u.http.delete(`/ticketGroup/delete/${id}`)

/* 查询电子票分组下拉 */
export const $ticketGroupPull = () => uni.$u.http.get('/ticketGroup/pull')

/* 查询用户纸质票总金额 */
export const $sumPrice = (data: any) => uni.$u.http.post('/userReceivingRecords/findSumPrice', data)

/* 根据扫描记录查询电子票演员信息 */
export const $userReceivingRecordsText = (data: any) =>
  uni.$u.http.get('/userReceivingRecordsText/findUserReceivingRecordsText', { params: data })

/* 查询电子票演员信息详情 */
export const $userReceivingRecordsDetails = (data: any) =>
  uni.$u.http.get('/userReceivingRecordsText/details', { params: data })

/* 添加电子票演员信息 */
export const $userReceivingRecordsAdd = (data: any) => uni.$u.http.post('/userReceivingRecordsText/add', data)

/* 修改电子票演员信息 */
export const $userReceivingRecordsUpdate = (data: any) => uni.$u.http.put('/userReceivingRecordsText/update', data)

/* 删除电子票演员信息 */
export const $userReceivingRecordsDelete = (ids: any) => uni.$u.http.delete(`/userReceivingRecordsText/delete/${ids}`)
