/* 查询首页推荐剧目表列表(分页) */
export const $repertoireListByIndex = (data: any) => uni.$u.http.post('/repertoire/open/listByIndex', data)

/* 查询剧目表列表(分页) */
export const $repertoireListByPage = (data: any) => uni.$u.http.post('/repertoire/open/listByPage', data)

/* 查询剧目表列表 */
export const $repertoireList = () => uni.$u.http.get('/repertoire/list')

/* 查询剧目表详情 */
export const $repertoireDetails = (id: number) => uni.$u.http.get('/repertoire/details', { params: { id } })

/* 剧目详情 */
export const $repertoireDetails2 = (params: any) => uni.$u.http.get('/repertoire/open/details', { params })

/* 根据剧场查询剧目信息 */
export const $repertoireByTheaterId = (data: any) =>
  uni.$u.http.post('/repertoireInfo/open/findRepertoireByTheaterId', data)

/* **************************************************************************************************** */

/* 查询剧目剧场场次表列表(分页) */
export const $repertoireInfoDetailListByPage = (data: any) => uni.$u.http.post('/repertoireInfoDetail/listByPage', data)

/* 查询剧目剧场场次表列表 */
export const $repertoireInfoDetailList = () => uni.$u.http.get('/repertoireInfoDetail/list')

/* 查询剧目剧场场次表详情 */
export const $repertoireInfoDetailDetails = (id: number) =>
  uni.$u.http.get('/repertoireInfoDetail/details', { params: { id } })

/* 添加剧目剧场场次表 */
export const $repertoireInfoDetailAdd = (data: any) => uni.$u.http.post('/repertoireInfoDetail/add', data)

/* 修改剧目剧场场次表 */
export const $repertoireInfoDetailUpdate = (data: any) => uni.$u.http.put('/repertoireInfoDetail/update', data)

/* 删除剧目剧场场次表 */
export const $repertoireInfoDetailDelete = (ids: string) => uni.$u.http.delete(`/repertoireInfoDetail/delete/${ids}`)

/* **************************************************************************************************** */

/* 查询剧目标签表列表(分页) */
export const $repertoireLabelListByPage = (data: any) => uni.$u.http.post('/repertoireLabel/listByPage', data)

/* 查询剧目标签表列表 */
export const $repertoireLabelList = () => uni.$u.http.get('/repertoireLabel/list')

/* 查询剧目标签表详情 */
export const $repertoireLabelDetails = (id: number) => uni.$u.http.get('/repertoireLabel/details', { params: { id } })

/* 添加剧目标签表 */
export const $repertoireLabelAdd = (data: any) => uni.$u.http.post('/repertoireLabel/add', data)

/* 修改剧目标签表 */
export const $repertoireLabelUpdate = (data: any) => uni.$u.http.put('/repertoireLabel/update', data)

/* 删除剧目标签表 */
export const $repertoireLabelDelete = (ids: string) => uni.$u.http.delete(`/repertoireLabel/delete/${ids}`)

/* **************************************************************************************************** */

/* 查询剧目主创团队表列表(分页) */
export const $repertoireCreativeTeamListByPage = (data: any) =>
  uni.$u.http.post('/repertoireCreativeTeam/listByPage', data)

/* 查询剧目主创团队表列表 */
export const $repertoireCreativeTeamList = () => uni.$u.http.get('/repertoireCreativeTeam/list')

/* 查询剧目主创团队表详情 */
export const $repertoireCreativeTeamDetails = (id: number) =>
  uni.$u.http.get('/repertoireCreativeTeam/details', { params: { id } })

/* 添加剧目主创团队表 */
export const $repertoireCreativeTeamAdd = (data: any) => uni.$u.http.post('/repertoireCreativeTeam/add', data)

/* 修改剧目主创团队表 */
export const $repertoireCreativeTeamUpdate = (data: any) => uni.$u.http.put('/repertoireCreativeTeam/update', data)

/* 删除剧目主创团队表 */
export const $repertoireCreativeTeamDelete = (ids: string) =>
  uni.$u.http.delete(`/repertoireCreativeTeam/delete/${ids}`)
