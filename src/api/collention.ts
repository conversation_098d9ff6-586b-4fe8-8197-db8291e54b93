/* 查询剧目电子票表列表(分页) */
export const $ticketList = (data: any) => uni.$u.http.post('/repertoireTicket/listByPage', data)

/* 查询剧目电子票表详情 */
export const $getRepertoireTicketInfo = (params: any) =>
  uni.$u.http.get('/repertoireTicket/getRepertoireTicketInfo', { params })

/* 查询数字头像表列表(分页) */
export const $avatarList = (data: any) => uni.$u.http.post('/digitalAvatar/listByPage', data)

/* 查询数字头像表详情 */
export const $getDigitalAvatarInfo = (params: any) => uni.$u.http.get('/digitalAvatar/getDigitalAvatarInfo', { params })

/* 查询剧场纪念徽章表列表(分页) */
export const $souvenirBadgeList = (data: any) => uni.$u.http.post('/souvenirBadge/open/listByPage', data)

/* 查询剧场纪念徽章表详情 */
export const $souvenirBadgeDetail = (params: any) => uni.$u.http.get('/souvenirBadge/open/details', { params })

/* 查询数字商品升级信息 */
export const $upgradeInfo = (params: any) =>
  uni.$u.http.get('/userReceivingRecords/findReceivingRecordsByPortfolioNo', { params })
