/* 查询用户榜单列表 */
export const $leaderboardList = () => uni.$u.http.get('/leaderboard/findUserLeaderboardList')

/* 查询用户榜单最后修改时间 */
export const $leaderboardLastTime = () => uni.$u.http.get('/leaderboard/findUserLeaderboardLastTime')

/* 查询榜单表详情 */
export const $leaderboardDetails = (data: any) => uni.$u.http.get('/leaderboard/details', { params: data })

/* 查询用户榜单数量 */
export const $leaderboardCount = () => uni.$u.http.get('/leaderboard/findUserLeaderboardCount')

/* 添加榜单表 */
export const $leaderboardAdd = (data: any) => uni.$u.http.post('/leaderboard/add', data)

/* 修改榜单表 */
export const $leaderboardUpdate = (data: any) => uni.$u.http.put('/leaderboard/update', data)

/* 删除榜单表 */
export const $leaderboardDelete = (id: any) => uni.$u.http.delete(`/leaderboard/delete/${id}`)

/* ****************************************************************************************** */

/* 查询用户榜单评论列 */
export const $LeaderboardComment = () => uni.$u.http.get('/userLeaderboardComment/findUserLeaderboardComment')

/* 查询用户榜单评论详情 */
export const $LeaderboardCommentDetails = (data: any) =>
  uni.$u.http.get('/userLeaderboardComment/details', { params: data })

/* 添加用户榜单评论 */
export const $LeaderboardCommentAdd = (data: any) => uni.$u.http.post('/userLeaderboardComment/add', data)

/* 修改用户榜单评论 */
export const $LeaderboardCommentUpdate = (data: any) => uni.$u.http.put('/userLeaderboardComment/update', data)

/* 删除用户榜单评论 */
export const $LeaderboardCommentDelete = (id: any) => uni.$u.http.delete(`/userLeaderboardComment/delete/${id}`)
