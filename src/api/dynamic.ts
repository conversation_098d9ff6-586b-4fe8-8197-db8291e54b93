/* 获取用户动态信息 */
export const $dynamicInfo = (userId: number) => uni.$u.http.get('/dynamic/info', { params: { userId } })

/* 获取动态详情 */
export const $dynamicDetails = (id: number, userId: number) =>
  uni.$u.http.get('/dynamic/details', { params: { id, userId } })

/* 获取用户下场演出信息 */
export const $nextShowInfo = (userId: number) => uni.$u.http.get('/dynamic/nextShow', { params: { userId } })

/* 获取用户看剧基金信息 */
export const $theaterFundInfo = (userId: number) => uni.$u.http.get('/dynamic/theaterFund', { params: { userId } })

/* 更新动态信息设置 */
export const $updateDynamicSettings = (data: any) => uni.$u.http.put('/dynamic/settings', data)

/* 添加剧场动态点赞、踩、删除 */
export const $dynamicKudos = (data: any) => uni.$u.http.post('/dynamicKudos/save', data)
