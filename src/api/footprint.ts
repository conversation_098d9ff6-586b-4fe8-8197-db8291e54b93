/* 获取足迹广场列表 */
export const $footprintListByIndex = (data: any) => uni.$u.http.post('/footprint/listByIndex', data)

/* 获取足迹广场详情 */
export const $footprintDetails = (id: number, userId: number) =>
  uni.$u.http.get('/footprint/details', { params: { id, userId } })

/* 发布足迹内容 */
export const $footprintAdd = (data: any) => uni.$u.http.post('/footprint/add', data)

/* 更新足迹内容 */
export const $footprintUpdate = (data: any) => uni.$u.http.put('/footprint/update', data)

/* 删除足迹内容 */
export const $footprintDelete = (id: number) => uni.$u.http.delete('/footprint/delete', { params: { id } })

/* 点赞足迹 */
export const $footprintLike = (data: any) => uni.$u.http.post('/footprint/like', data)

/* 取消点赞 */
export const $footprintUnlike = (data: any) => uni.$u.http.post('/footprint/unlike', data)

/* 获取足迹评论列表 */
export const $footprintCommentList = (data: any) => uni.$u.http.post('/footprint/comment/list', data)

/* 添加足迹评论 */
export const $footprintCommentAdd = (data: any) => uni.$u.http.post('/footprint/comment/add', data)

/* 删除足迹评论 */
export const $footprintCommentDelete = (id: number) =>
  uni.$u.http.delete('/footprint/comment/delete', { params: { id } })
