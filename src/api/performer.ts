/* 查询剧目演员表列表(分页) */
export const $repertoireActorListByPage = (data: any) => uni.$u.http.post('/repertoireActor/listByPage', data)

/* 查询剧目演员表列表 */
export const $repertoireActorList = () => uni.$u.http.get('/repertoireActor/list')

/* 查询剧目演员表详情 */
export const $repertoireActorDetails = (id: number) => uni.$u.http.get('/repertoireActor/details', { params: { id } })

/* 添加剧目演员表 */
export const $repertoireActorAdd = (data: any) => uni.$u.http.post('/repertoireActor/add', data)

/* 修改剧目演员表 */
export const $repertoireActorUpdate = (data: any) => uni.$u.http.put('/repertoireActor/update', data)

/* 删除剧目演员表 */
export const $repertoireActorDelete = (ids: string) => uni.$u.http.delete(`/repertoireActor/delete/${ids}`)
