/* 数字头像（分组） */
export const $collectionListByDigitalAvatar = (data: any) =>
  uni.$u.http.post('/userReceivingRecords/listByDigitalAvatar', data)

/* 数字头像详情 */
export const $detailsByDigitalAvatar = (id: number, upgradeStatus?: number) =>
  uni.$u.http.get('/userReceivingRecords/detailsByDigitalAvatar', { params: { id, upgradeStatus } })

/* 查询用户数字头像领取记录列表(分页) */
export const $userAdornDigitalAvatar = (badgeType: any) =>
  uni.$u.http.get('/userAdornCollection/findUserAdornDigitalAvatar', { params: { badgeType } })

/* 查询用户数字头像展示列表 */
export const $userDigitalAvatar = (data: any) => uni.$u.http.post('/userReceivingRecords/findUserDigitalAvatar', data)

/* 修改商品佩戴状态 */
export const $updateAdornStatus = (data: any) => uni.$u.http.put('/userReceivingRecords/updateAdornStatus', data)
