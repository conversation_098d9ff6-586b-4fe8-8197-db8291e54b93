/* 查询用户推送未看条数 */
export const $userNotLookNotifyCount = () => uni.$u.http.get('/userNotify/findUserNotLookNotifyCount')

/* 查询用户动态未看条数 */
export const $dynamicCount = () => uni.$u.http.get('/userTreasure/findDynamicCount')

/* 查询用户等级勋章未查看条数 */
export const $notLookRankCount = () => uni.$u.http.get('/userReceivingRecords/findNotLookRankCount')

/* 查询用户评论条数 */
export const $commentCount = (data: any) => uni.$u.http.post('/comment/listByUserCount', data)

/* 查询用户未评论条数 （1、评论，2、评论点赞，3、提问，4、动态点赞） */
export const $receivingRecordsCount = (type: any) =>
  uni.$u.http.get('/userInteraction/findUserReceivingRecordsCount', { params: { type } })

/* 查询用户未查看消息条数 */
export const $userMessageNotifyCount = () => uni.$u.http.get('/userMessageInfo/findUserMessageNotifyCount')

/* 查询商品未查看条数 */
export const $findNotLookCount = (badgeType: any) =>
  uni.$u.http.get('/userReceivingRecords/findNotLookCount', { params: { badgeType } })

/* 查询回复我的评论条数 */
export const $replyByUserCount = () => uni.$u.http.get('/comment/findReplyByUserCount')
