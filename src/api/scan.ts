/* 百度AI图片识别（图片） */
export const $imgRecognition = (data: any) =>
  uni.$u.http.upload('/ai/imgRecognition', { ...data, custom: { skip: true } })

/* 查询用户数字商品领取记录列表(分页) */
export const $collectionListByPage = (data: any) => uni.$u.http.post('/userReceivingRecords/listByPage', data)

/* 查询用户数字商品领取记录列表 */
export const $collectionList = () => uni.$u.http.get('/userReceivingRecords/list')

/* 查询用户数字商品领取记录详情 */
export const $collectionDetails = (id: number) => uni.$u.http.get('/userReceivingRecords/details', { params: { id } })

/* 添加用户数字商品领取记录 */
export const $collectionAdd = (data: any) => uni.$u.http.post('/userReceivingRecords/add', data)
export const $collectionSave = (data: any) => uni.$u.http.post('/userReceivingRecords/save', data)

/* 修改用户数字商品领取记录 */
export const $collectionUpdate = (data: any) => uni.$u.http.put('/userReceivingRecords/update', data)

/* 根据用户ID+类型查询领取数量 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 */
export const $collectionNum = (params: any) => uni.$u.http.get('/userReceivingRecords/findGetNumberByUser', { params })

/* 根据用户ID+类型查询领取数量 2：数字头像 */
export const $digitalAvatarCount = () => uni.$u.http.get('/userReceivingRecords/findDigitalAvatarCountByUser')

/* 其他 *************************************************************************************************************************************** */

/* 查询用户推送商品表列表(分页) */
export const $userPushCollectionListByPage = (data: any) => uni.$u.http.post('/userPushCollection/listByPage', data)

/* 查询用户推送商品表详情(分页) */
export const $userPushCollectionDetail = (id: number) =>
  uni.$u.http.get('/userPushCollection/details', { params: { id } })

/* 添加用户数字头像 */
export const $userDigitalAvatarAdd = (data: any) => uni.$u.http.post('/userDigitalAvatar/add', data)
