export const baseUrl = import.meta.env.VITE_BASE_API

/* 获取参数配置列表 */
export const $config = () => uni.$u.http.get('/system/config/list')

/* 通用上传请求（单个） */
// header: { 'Content-Type': 'multipart/form-data' }
export const $upload = (data: any) => uni.$u.http.upload('/common/upload', { ...data })

/* 通用上传请求（多个） */
export const $uploads = (data: any) => uni.$u.http.upload('/common/uploads', { ...data })

/* 查询小程序设置表详情 */
export const $getWechatSetting = () => uni.$u.http.get('/wechatSetting/open/getWechatSetting')

/* 根据类型查询协议 */
export const $agreementByType = (type: number) =>
  uni.$u.http.get('/agreement/open/findAgreementByType', { params: { type } })

/* 查询广告轮播图表列表 */
export const $advertisingPicture = () => uni.$u.http.get('/advertisingPicture/list')

/* 添加评论点赞、踩、删除 */
export const $commentInfoSave = (data: any) => uni.$u.http.post('/commentInfo/save', data)

/* 剧场剧目关注添加或取消 */
export const $userTreasureSave = (data: any) => uni.$u.http.post('/userTreasure/save', data)

/* 剧场剧目关注列表 */
export const $userTreasureListByPage = (data: any) => uni.$u.http.post('/userTreasure/listByPage', data)
