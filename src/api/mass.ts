/* 查询用户会话表列表(分页) */
export const $userMessageListByPage = (data: any) => uni.$u.http.post('/userMessage/listByPage', data)

/* 查询用户会话表列表 */
export const $userMessageList = () => uni.$u.http.get('/userMessage/list')

/* 查询用户会话表详情 */
export const $userMessageDetails = (id: number) => uni.$u.http.get('/userMessage/details', { params: { id } })

/* 添加用户会话表 */
export const $userMessageAdd = (data: any) => uni.$u.http.post('/userMessageInfo/add', data)

/* 修改用户会话表 */
export const $userMessageUpdate = (data: any) => uni.$u.http.put('/userMessage/update', data)

/* 删除用户会话表 */
export const $userMessageDelete = (ids: string) => uni.$u.http.delete(`/userMessage/delete/${ids}`)
