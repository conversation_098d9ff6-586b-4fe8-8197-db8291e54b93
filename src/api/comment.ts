/* 查询剧目剧场评论列表(分页) */
export const $commentListByPage = (data: any) => uni.$u.http.post('/comment/listByPage', data)

/* 查询用户评论列表(分页) */
export const $commentListByUser = (data: any) => uni.$u.http.post('/comment/listByUser', data)

/* 查询用户评论数量 */
export const $commentListByUserCount = (data: any) => uni.$u.http.post('/comment/listByUserCount', data)

/* 查询用户回复列表(分页) */
export const $commentListReplyByUser = (data: any) => uni.$u.http.post('/comment/listReplyByUser', data)

/* 查询剧场、剧目对应评论（填加评论时的两段评论） */
export const $commentMappingComment = (id: number) => uni.$u.http.post(`/comment/findMappingComment?id=${id}`)

/* 查询评论详情 */
export const $commentDetail = (id: number, userId: number) =>
  uni.$u.http.get('/comment/details', { params: { id, userId } })

/* 添加剧目剧场评论 */
export const $commentAdd = (data: any) => uni.$u.http.post('/comment/add', data)

/* 回复评论 */
export const $commentReply = (data: any) => uni.$u.http.post('/comment/reply', data)

/* 修改剧目剧场评论 */
export const $commentUpdate = (data: any) => uni.$u.http.put('/comment/update', data)

/* 删除剧目剧场评论 */
export const $commentDelete = (ids: string) => uni.$u.http.delete(`/comment/delete/${ids}`)
