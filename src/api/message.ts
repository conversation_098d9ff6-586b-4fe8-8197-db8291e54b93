/* 查询用户推送信息表列表(分页) */
export const $userNotifyListByPage = (data: any) => uni.$u.http.post('/userNotify/listByPage', data)

/* 查询用户推送信息表列表 */
export const $userNotifyList = () => uni.$u.http.get('/userNotify/list')

/* 查询用户推送信息表详情 */
export const $userNotifyDetails = (id: number) => uni.$u.http.get('/userNotify/details', { params: { id } })

/* 添加用户推送信息表 */
export const $userNotifyAdd = (data: any) => uni.$u.http.post('/userNotify/add', data)

/* 修改用户推送信息表 */
export const $userNotifyUpdate = (data: any) => uni.$u.http.post('/userNotify/update', data)

/* 删除用户推送信息表 */
export const $userNotifyDelete = (ids: string) => uni.$u.http.delete(`/userNotify/delete/${ids}`)
