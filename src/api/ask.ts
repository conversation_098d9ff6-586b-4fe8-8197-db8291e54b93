/* 查询剧场下问答条数 */
export const $issueCountByTheater = (theaterId: number) =>
  uni.$u.http.get('/issue/findIssueCountByTheater', { params: { theaterId } })

/* 查询剧目下问答条数 */
export const $issueCountByRepertoire = (repertoireId: number) =>
  uni.$u.http.get('/issue/findIssueCountByRepertoire', { params: { repertoireId } })

/* 查询剧目剧场问答列表(父级) */
export const $issueList = (data: any) => uni.$u.http.post('/issue/listByPage', data)

/* 查询剧目剧场问答列表(子级) */
export const $issueListByParentId = (data: any) => uni.$u.http.post('/issue/listByParentId', data)

/* 查询剧目剧场问答表详情 */
export const $issueDetails = (id: number) => uni.$u.http.get('/issue/details', { params: { id } })

/* 添加剧目剧场问答表 */
export const $issueAdd = (data: any) => uni.$u.http.post('/issue/add', data)

/* 回复剧目剧场问答表 */
export const $issueReply = (data: any) => uni.$u.http.post('/issue/reply', data)
