/* 查询通知表列表(分页) */
export const $notifyListByPage = (data: any) => uni.$u.http.post('/notify/listByPage', data)

/* 查询通知表列表 */
export const $notifyList = () => uni.$u.http.get('/notify/list')

/* 查询通知表详情 */
export const $notifyDetails = (id: number) => uni.$u.http.get('/notify/details', { params: { id } })

/* 添加通知表 */
export const $notifyAdd = (data: any) => uni.$u.http.post('/notify/add', data)

/* 修改通知表 */
export const $notifyUpdate = (data: any) => uni.$u.http.put('/notify/update', data)

/* 删除通知表 */
export const $notifyDelete = (ids: string) => uni.$u.http.delete(`/notify/delete/${ids}`)
