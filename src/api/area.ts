/* 查询地区表列表(分页) */
export const $areaListByPage = () => uni.$u.http.post('/area/open/listByPage')

/* 查询地区表树形 */
export const $areaTree = () => uni.$u.http.get('/area/open/findAreaTree')

/* 查询地区表列表 */
export const $areaList = () => uni.$u.http.get('/area/open/list')

/* 查询地区表详情 */
export const $areaDetail = () => uni.$u.http.get('/area/open/details')

/* 查询地区市一级 */
export const $areaByCity = (keyword: string) => uni.$u.http.get('/area/open/findAreaByCity', { params: { keyword } })
