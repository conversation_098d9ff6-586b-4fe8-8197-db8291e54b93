/* 查询用户订单未支付条数 */
export const $userOrderNum = () => uni.$u.http.get('/userOrder/findUserOrderCount')

/* 查询用户订单表列表(分页) */
export const $userOrderList = (data: any) => uni.$u.http.post('/userOrder/listByPage', data)

/* 查询用户订单表详情 */
export const $userOrderDetail = (id: number) => uni.$u.http.get('/userOrder/details', { params: { id } })

/* 取消支付 */
export const $cancelOrder = (id: any) => uni.$u.http.post('/userOrder/cancelOrder/' + id)
