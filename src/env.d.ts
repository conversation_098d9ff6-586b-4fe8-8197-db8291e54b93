/// <reference types="vite/client" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/uni_modules/uview-plus'

declare module '@/uni_modules/uview-plus' {
  global {
    interface Uni {
      $u: any
    }
  }
}

declare module '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
declare module '@/utils/emoji.js'
declare module 'dayjs'
declare module '@/static/zuozuo.glb'
declare let wx: any
