import { $getWechatSetting } from '@/api/base'

export default {
  onLoad() {
    // 设置默认的转发参数
    $getWechatSetting().then((res: any) => {
      uni.$u.mpShare = {
        title: `快来${res.data.wechatName}一起看演出~`, // 默认为小程序名称，可自定义
        path: '/pages/homepage/index', // 默认为当前页面路径，一般无需修改，QQ小程序不支持
        imageUrl: '', // 分享图标，路径可以是本地文件路径、代码包文件路径或者网络图片路径。
        // 支持PNG及JPG，默认为当前页面的截图
      }
    })
  },
  onShareAppMessage() {
    return uni.$u.mpShare
  },
  onShareTimeline() {
    return uni.$u.mpShare
  },
}
