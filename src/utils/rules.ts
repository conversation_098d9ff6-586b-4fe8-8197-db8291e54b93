export const $editInfoRules: any = {
  name: {
    type: 'string',
    required: true,
    message: '请输入2-15位昵称',
    trigger: ['blur', 'change'],
    min: 2,
    max: 15,
  },
}

/* 问大家验证 */
export const $askRules: any = {
  content: {
    type: 'string',
    required: true,
    message: '请输入提问内容',
    trigger: ['blur', 'change'],
  },
}

/* 评论验证 */
export const $commentRules: any = {
  // content: { type: 'string', required: true, message: '请输入剧目评价', trigger: ['blur', 'change'] },
  // repertoireFlag: { type: 'number', required: true, message: '请选择剧目赞踩', trigger: ['blur', 'change'] },
  // theaterContent: { type: 'string', required: true, message: '请输入剧场评价', trigger: ['blur', 'change'] },
  // theaterFlag: { type: 'number', required: true, message: '请选择剧场赞踩', trigger: ['blur', 'change'] },
}

/* 手机号验证 */
export const $authenticationRules: any = {
  phone: [
    {
      required: true,
      message: '请输入新手机号',
      trigger: ['change', 'blur'],
    },
    {
      // 自定义验证函数，见上说明
      validator: (rule: any, value: any, callback: any) => {
        // 上面有说，返回true表示校验通过，返回false表示不通过
        // uni.$u.test.mobile()就是返回true或者false的
        return uni.$u.test.mobile(value)
      },
      message: '手机号码不正确',
      // 触发器可以同时用blur和change
      trigger: ['change', 'blur'],
    },
  ],
  code: { type: 'string', required: true, message: '请输入验证码', trigger: ['blur', 'change'] },
}

export const $pwdRules: any = {
  newPwd: {
    type: 'string',
    required: true,
    min: 6,
    max: 16,
    message: '请输入6~16位账号密码',
    trigger: ['blur', 'change'],
  },
  confrimPwd: {
    type: 'string',
    required: true,
    min: 6,
    max: 16,
    message: '两次输入的密码不一致',
    trigger: ['blur', 'change'],
  },
}
