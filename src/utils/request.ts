import { $replaceAll } from './methods'
import { useGlobalStore } from '@/stores/global'

export default function () {
  /* 初始化请求配置 */
  uni.$u.http.setConfig((config: any) => {
    /* config 为默认全局配置*/

    /* 请求base地址 */
    config.baseURL = import.meta.env.VITE_BASE_API
    config.header = { 'Content-Type': 'application/json;charset=utf-8' }

    return config
  })

  // 请求拦截
  uni.$u.http.interceptors.request.use(
    (config: any) => {
      const globalStore: any = useGlobalStore()

      config.data = config.data || {} // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}

      if (globalStore.token) config.header.Authorization = 'Bearer ' + globalStore.token

      return config
    },
    (config: any) => {
      uni.hideLoading()
      return Promise.reject(config)
    }
  )

  // 响应拦截
  uni.$u.http.interceptors.response.use(
    (response: any) => {
      const globalStore: any = useGlobalStore()
      const custom = response.config?.custom // 自定义参数
      const data = response.data

      if (data.code === 401) {
        uni.hideLoading()
        // uni.$u.toast(data.msg ? data.msg : '未登录或者登录超时')

        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('scanResult')
        globalStore.token = ''
        globalStore.userInfo = ''
        globalStore.scanResult = ''
        $replaceAll({ name: 'Login' })

        return Promise.reject(data)
        // if (custom?.catch) return Promise.reject(data)
        // else return new Promise(() => {})
      }

      if (response.config.custom.skip) {
        if (data.data === undefined || data.data === null) data.data = {}
        return data
      }

      if (data.code !== 200) {
        uni.hideLoading()
        uni.$u.toast(data.msg ? data.msg : '请求失败')

        return Promise.reject(data)
        // if (custom?.catch) return Promise.reject(data)
        // else return new Promise(() => {})
      }

      if (data.data === undefined || data.data === null) data.data = ''

      return data
    },
    (response: any) => {
      uni.hideLoading()
      uni.$u.toast('请求失败')
      return Promise.reject(response)
    }
  )
}
