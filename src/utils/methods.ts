import { pages, subPackages } from '@/pages.json'
import _ from 'lodash'

/* 线上图片format */
export const $iconFormat = (url: string) => {
  if (!url) return ''
  if (url.includes('http')) return url
  else return import.meta.env.VITE_ICON + url
}

/* 服务器图片format */
export const $picFormat = (url: string) => {
  if (!url) return ''
  if (url.includes('http')) return url
  else return import.meta.env.VITE_PIC + url
}

/* 匹配路由 */
export const $matchRoute: any = (name: any) => {
  let [routerObj]: any = pages.filter((i: any) => i.name === name || i.path === name)

  if (!routerObj) {
    routerObj = subPackages[0].pages.filter((i: any) => i.name === name || i.path === name)[0]

    if (routerObj) {
      const regRes = new RegExp(subPackages[0].root + '/').test(routerObj.path)
      if (!regRes) routerObj.path = subPackages[0].root + '/' + routerObj.path
    }
  }

  if (!routerObj) routerObj = $matchRoute('404')

  return routerObj
}
/* 路由跳转 */
export const $push = (route: any) => {
  const routerObj: any = { url: '/' + $matchRoute(route.name).path }
  if (route.params) routerObj.url += uni.$u.queryParams(route.params)
  if (route.routeParams) routerObj.routeParams = route.routeParams
  if (route.passedParams) routerObj.passedParams = route.passedParams

  uni.navigateTo(routerObj)
}

/* 路由替换 */
export const $replace = (route: any) => {
  const routerObj: any = { url: '/' + $matchRoute(route.name).path }
  if (route.params) routerObj.url += uni.$u.queryParams(route.params)
  if (route.routeParams) routerObj.routeParams = route.routeParams
  if (route.passedParams) routerObj.passedParams = route.passedParams

  uni.redirectTo(routerObj)
}
/* 关闭所有路由后，打开指定路由 */
export const $replaceAll = (route: any) => {
  const routerObj: any = { url: '/' + $matchRoute(route.name).path }
  if (route.params) routerObj.url += uni.$u.queryParams(route.params)
  if (route.routeParams) routerObj.routeParams = route.routeParams
  if (route.passedParams) routerObj.passedParams = route.passedParams

  uni.reLaunch(routerObj)
}
/* tabbar切换 */
export const $tab = (route: any) => {
  const routerObj: any = { url: '/' + $matchRoute(route.name).path }
  if (route.params) routerObj.url += uni.$u.queryParams(route.params)
  if (route.routeParams) routerObj.routeParams = route.routeParams
  if (route.passedParams) routerObj.passedParams = route.passedParams

  uni.switchTab(routerObj)
}
/* 后退 */
export const $back = (delta?: number) => {
  const pageArr: any = getCurrentPages()

  if (pageArr.length > 1) {
    uni.navigateBack({ delta: delta || 1 })
  } else {
    $replaceAll({ name: 'Homepage' })
  }
}

/* 一版提示 */
export const $toast = (app: any, message: string, complete?: any, params?: any) => {
  app.$refs.uToast.show({
    type: 'default',
    position: 'bottom',
    params,
    message,
    complete
  })
}

/* 成功提示 */
export const $success = (app: any, message: string, complete?: any, params?: any) => {
  app.$refs.uToast.show({
    type: 'success',
    position: 'bottom',
    params,
    message,
    complete
  })
}

/* 失败提示 */
export const $error = (app: any, message: string, complete?: any, params?: any) => {
  app.$refs.uToast.show({
    type: 'error',
    icon: false,
    position: 'bottom',
    params,
    message,
    complete
  })
}

/* 加载提示 */
export const $loading = (title = '') => {
  uni.showLoading({ title, mask: true })
}

/* mescroll返回顶部 */
export const $topclick = (mescroll: any) => {
  mescroll.scrollTo(0)
}

/* 隐藏手机号 */
export const $hidePhone = (phone: number) => {
  const reg = /^(\d{3})\d{4}(\d{4})$/
  if (phone) return phone.toString().replace(reg, '$1****$2')
  else return ''
}

/* 隐藏用户名 */
export const $hideName = (name: string) => {
  if (name) {
    const arr: string[] = name.split('')
    return _.head(arr) + '***' + _.last(arr)
  } else return ''
}

/* 按中文首字母排序分组 */
export const $citySort = (arr: any, objIndex: any) => {
  if (!String.prototype.localeCompare) return []

  const letters: string[] = 'ABCDEFGHJKLMNOPQRSTWXYZ'.split('')
  const zh: string[] = '阿八嚓哒妸发旮哈讥咔垃痳拏噢妑七呥扨它穵夕丫帀'.split('')
  const segs: object[] = []

  letters.forEach((item: any, i: number) => {
    const cur: any = { letter: item, data: [] }

    arr.forEach((item: any) => {
      if (item[objIndex].localeCompare(zh[i]) >= 0 && item[objIndex].localeCompare(zh[i + 1]) < 0) {
        cur.data.push(item)
      }
    })

    if (cur.data.length) {
      cur.data.sort((a: any, b: any) => {
        return a[objIndex].localeCompare(b[objIndex], 'zh')
      })

      segs.push(cur)
    }
  })

  return segs
}

/* 预览图片 */
export const $previewImage = (urls: string[], current = 0) => {
  uni.previewImage({ urls, current })
}

/* 判断点踩变化 */
export const $checkKudos = (i: any, type: any) => {
  i.likeCount = i.likeCount - 0
  i.dislikeCount = i.dislikeCount - 0

  switch (true) {
    // 点赞
    case type === 1 && !i.kudosStatus && i.kudosStatus !== 0:
      i.likeCount++
      break
    // 取消点赞
    case !type && type !== 0 && i.kudosStatus === 1:
      i.likeCount--
      break
    // 点赞转点踩
    case type === 1 && i.kudosStatus === 0:
      i.likeCount++
      i.dislikeCount--
      break
    // 点踩
    case type === 0 && !i.kudosStatus && i.kudosStatus !== 0:
      i.dislikeCount++
      break
    // 取消点踩
    case !type && type !== 0 && i.kudosStatus === 0:
      i.dislikeCount--
      break
    // 点踩转点赞
    case type === 0 && i.kudosStatus === 1:
      i.likeCount--
      i.dislikeCount++
      break
  }

  i.kudosStatus = type
}

/* 下载保存图片到相册 */
export const $downloadSavePic = (app: any, url: string) => {
  $loading('保存中')

  uni.downloadFile({
    url,
    success: (res: any) => {
      if (res.statusCode === 200) {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            $toast(app, '保存成功')
          },
          fail: err => {
            uni.hideLoading()
            if (err.errMsg == 'saveImageToPhotosAlbum:fail cancel') $toast(app, '用户取消保存')
            else $toast(app, '保存失败')
          }
        })
      }
    },
    fail: err => {
      uni.hideLoading()
      $toast(app, '保存失败')
    }
  })
}

/* 保存图片到相册 */
export const $savePic = (app: any, url: string) => {
  $loading('保存中')

  uni.saveImageToPhotosAlbum({
    filePath: url,
    success: () => {
      uni.hideLoading()
      $toast(app, '保存成功')
    },
    fail: err => {
      uni.hideLoading()
      if (err.errMsg == 'saveImageToPhotosAlbum:fail cancel') $toast(app, '用户取消保存')
      else $toast(app, '保存失败')
    }
  })
}
