module.exports = {
  // 只继承最基础的配置
  extends: ['stylelint-config-standard'],
  // 指定不同文件对应的解析器
  overrides: [
    {
      files: ['**/*.{vue,html}'],
      customSyntax: 'postcss-html'
    },
    {
      files: ['**/*.{css,scss}'],
      customSyntax: 'postcss-scss'
    }
  ],
  // 关闭所有严格的格式化规则
  rules: {
    // 关闭字体相关检查
    'font-family-no-missing-generic-family-keyword': null,
    'font-family-name-quotes': null,

    // 关闭导入相关检查
    'import-notation': null,

    // 关闭特异性检查
    'no-descending-specificity': null,
    'no-duplicate-selectors': null,
    'no-empty-source': null,

    // 关闭格式化相关规则
    indentation: null,
    'string-quotes': null,
    'color-hex-case': null,
    'color-hex-length': null,
    'number-leading-zero': null,
    'number-no-trailing-zeros': null,
    'length-zero-no-unit': null,
    'declaration-bang-space-after': null,
    'declaration-bang-space-before': null,
    'declaration-colon-space-after': null,
    'declaration-colon-space-before': null,
    'function-comma-space-after': null,
    'function-comma-space-before': null,
    'function-parentheses-space-inside': null,
    'media-feature-colon-space-after': null,
    'media-feature-colon-space-before': null,
    'block-closing-brace-newline-after': null,
    'block-closing-brace-newline-before': null,
    'block-opening-brace-newline-after': null,
    'block-opening-brace-space-before': null,
    'rule-empty-line-before': null,

    // SCSS相关规则
    'scss/at-import-partial-extension': null,
    'scss/load-partial-extension': null,
    'scss/dollar-variable-pattern': null,
    'scss/percent-placeholder-pattern': null,

    // 选择器相关
    'selector-class-pattern': null,
    'selector-id-pattern': null,
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep', 'global']
      }
    ],
    'selector-type-no-unknown': [
      true,
      {
        ignoreTypes: ['page', 'uni-page-body', 'uni-image', 'uni-cover-view', 'uni-view', 'uni-text']
      }
    ],

    // 单位相关
    'unit-no-unknown': [
      true,
      {
        ignoreUnits: ['rpx', 'rem', 'vw', 'vh', 'upx']
      }
    ]
  }
}
