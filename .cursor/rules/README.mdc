---
description: 
globs: 
alwaysApply: false
---
# 项目开发规则总览

## 项目简介
这是一个基于 uni-app 框架开发的微信小程序项目，主要用于剧院演出信息展示、票务预订等功能。

## 技术栈
- **前端框架**: uni-app + Vue 3 + TypeScript
- **状态管理**: Pinia
- **UI 组件**: uview-plus + 自定义组件
- **样式框架**: Tailwind CSS + SCSS
- **路由管理**: uni-crazy-router
- **工具库**: lodash, dayjs
- **构建工具**: Vite

## 规则文件说明

### 📁 [项目结构规则](mdc:.cursor/rules/project-structure.mdc)
- 项目目录结构说明
- 核心文件介绍
- 文件命名规范
- 模块组织方式

### 🛠️ [开发指南规则](mdc:.cursor/rules/development-guidelines.mdc)
- 编码规范和最佳实践
- Vue 组件开发规范
- TypeScript 使用规范
- 性能优化建议

### 📱 [uni-app 小程序规则](mdc:.cursor/rules/uniapp-miniprogram.mdc)
- 小程序开发基础
- 生命周期使用规范
- 小程序 API 调用规范
- 第三方组件使用指南

### 🌐 [API 开发规范](mdc:.cursor/rules/api-standards.mdc)
- API 接口组织结构
- 请求封装规范
- 错误处理机制
- 数据类型定义

### 🎨 [UI 组件规范](mdc:.cursor/rules/ui-component-guidelines.mdc)
- 组件库使用指南
- 自定义组件开发规范
- 样式开发规范
- 交互设计规范

### 🗃️ [状态管理规范](mdc:.cursor/rules/state-management.mdc)
- Pinia 状态管理
- Store 定义规范
- 持久化存储规范
- 状态同步机制

## 快速开始

### 开发环境设置
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 代码检查
npm run lint

# 样式检查
npm run stylelint
```

### 项目结构概览
```
MiniProgram/
├── src/
│   ├── api/                 # API 接口层
│   ├── components/          # 公共组件
│   ├── pages/              # 页面文件
│   ├── stores/             # 状态管理
│   ├── utils/              # 工具函数
│   ├── router/             # 路由配置
│   ├── static/             # 静态资源
│   └── uni_modules/        # 第三方组件
├── tailwind.config.js      # Tailwind 配置
├── package.json           # 项目配置
└── .cursor/rules/         # 开发规则
```

## 关键配置文件

### 主要配置文件
- [package.json](mdc:package.json) - 项目依赖和脚本
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS 配置
- [src/main.ts](mdc:src/main.ts) - 应用入口文件
- [src/App.vue](mdc:src/App.vue) - 应用根组件

### 核心功能模块
- **用户系统**: 登录、注册、个人信息管理
- **剧院管理**: 剧院信息展示、搜索、定位
- **票务系统**: 演出信息、票务预订、订单管理
- **社交功能**: 评论、关注、分享、互动
- **内容管理**: 动态发布、收藏、历史记录

## 开发规范要点

### 代码风格
- 使用 TypeScript 进行类型约束
- 遵循 ESLint 和 Prettier 配置
- 使用 Composition API 开发组件
- 优先使用 Tailwind CSS 进行样式开发

### 组件开发
- 组件名使用 PascalCase 命名
- 合理使用 props 和 emit
- 实现组件的可复用性和可测试性
- 遵循单一职责原则

### API 调用
- 统一的请求封装和错误处理
- 使用 TypeScript 定义接口类型
- 实现请求缓存和防重复机制
- 合理的加载状态处理

### 状态管理
- 使用 Pinia 进行状态管理
- 按业务模块组织 Store
- 实现状态的持久化存储
- 优化状态更新性能

## 最佳实践

### 性能优化
- 使用图片懒加载
- 实现组件懒加载
- 优化列表渲染性能
- 合理使用缓存策略

### 用户体验
- 实现loading状态提示
- 优化错误处理和提示
- 支持下拉刷新和上拉加载
- 提供友好的空状态展示

### 代码质量
- 编写清晰的代码注释
- 实现适当的错误边界
- 使用合适的设计模式
- 保持代码的可维护性

## 部署和发布

### 构建流程
```bash
# 生产环境构建
npm run build

# 类型检查
npm run type-check
```

### 发布流程
1. 使用微信开发者工具打开构建后的目录
2. 进行代码检查和预览测试
3. 上传代码到微信公众平台
4. 提交审核并发布

## 注意事项

### 小程序限制
- 页面栈最多 10 层
- 代码包大小限制
- API 调用频率限制
- 某些 HTML5 特性不支持

### 开发建议
- 定期更新依赖包
- 关注小程序平台更新
- 做好兼容性测试
- 注意用户隐私保护

---

遵循这些规则和最佳实践，可以确保项目的代码质量、可维护性和开发效率。如有疑问，请参考具体的规则文件获取详细信息。
