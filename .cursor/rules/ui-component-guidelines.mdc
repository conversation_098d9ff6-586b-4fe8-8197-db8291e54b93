---
description: 
globs: 
alwaysApply: false
---
# UI 组件开发规范

## 组件库使用规范

### 主要 UI 组件库
项目使用了以下 UI 组件库，位于 `/src/uni_modules/` 目录：

- **uview-plus**: 主要 UI 组件库，提供丰富的基础组件
- **uv-ui-tools**: UI 工具库，提供工具函数和样式
- **mescroll-uni**: 下拉刷新和上拉加载组件
- **uv-waterfall**: 瀑布流布局组件
- **lime-painter**: 海报生成组件
- **ksp-cropper**: 图片裁剪组件

### uview-plus 组件使用
```vue
<template>
  <!-- 按钮组件 -->
  <u-button 
    type="primary" 
    size="large"
    :loading="loading"
    @click="handleClick"
  >
    确认
  </u-button>
  
  <!-- 输入框组件 -->
  <u-input
    v-model="inputValue"
    placeholder="请输入内容"
    :clearable="true"
    @change="handleInputChange"
  />
  
  <!-- 弹窗组件 -->
  <u-modal
    v-model="showModal"
    title="提示"
    content="确认删除吗？"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
  
  <!-- 图片组件 -->
  <u-image
    :src="imageUrl"
    width="200rpx"
    height="200rpx"
    mode="aspectFill"
    :lazy-load="true"
    @click="previewImage"
  />
</template>
```

### 列表组件使用
```vue
<template>
  <!-- 下拉刷新上拉加载 -->
  <mescroll-uni
    ref="mescrollRef"
    @down="downCallback"
    @up="upCallback"
    :down="downOption"
    :up="upOption"
  >
    <view class="list-container">
      <view 
        v-for="item in dataList" 
        :key="item.id"
        class="list-item"
      >
        {{ item.title }}
      </view>
    </view>
  </mescroll-uni>
</template>

<script lang="ts" setup>
import type { MescrollInstance } from '@/uni_modules/mescroll-uni/types'

const mescrollRef = ref<MescrollInstance>()
const dataList = ref<any[]>([])

// 下拉刷新配置
const downOption = {
  use: true,
  auto: false
}

// 上拉加载配置
const upOption = {
  use: true,
  auto: true,
  page: {
    num: 0,
    size: 10
  }
}

// 下拉刷新回调
const downCallback = (mescroll: MescrollInstance) => {
  // 重置数据
  dataList.value = []
  // 重置上拉加载
  mescroll.resetUpScroll()
}

// 上拉加载回调
const upCallback = async (mescroll: MescrollInstance) => {
  try {
    const page = mescroll.num
    const data = await getListData({ page, pageSize: 10 })
    
    if (page === 1) {
      dataList.value = data.list
    } else {
      dataList.value.push(...data.list)
    }
    
    mescroll.endSuccess(data.list.length)
  } catch (error) {
    mescroll.endErr()
  }
}
</script>
```

## 自定义组件开发规范

### 组件文件结构
```
src/components/
├── CustomButton/
│   ├── CustomButton.vue      # 主组件文件
│   ├── types.ts             # 类型定义
│   └── index.ts             # 导出文件
└── UserAvatar/
    ├── UserAvatar.vue
    ├── types.ts
    └── index.ts
```

### 组件模板规范
```vue
<!-- CustomButton.vue -->
<template>
  <button 
    :class="buttonClass"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <uv-loading-icon 
      v-if="loading" 
      size="32rpx" 
      color="#fff"
    />
    <text v-if="!loading">{{ text }}</text>
  </button>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { ButtonProps, ButtonEmits } from './types'

// 组件属性定义
const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'default',
  size: 'medium',
  disabled: false,
  loading: false,
  block: false
})

// 事件定义
const emit = defineEmits<ButtonEmits>()

// 计算属性
const buttonClass = computed(() => {
  return [
    'custom-button',
    `custom-button--${props.type}`,
    `custom-button--${props.size}`,
    {
      'custom-button--disabled': props.disabled,
      'custom-button--loading': props.loading,
      'custom-button--block': props.block
    }
  ]
})

// 事件处理
const handleClick = (event: Event) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.custom-button {
  @apply inline-flex items-center justify-center rounded-lg font-Medium transition-all duration-200;
  
  &--default {
    @apply bg-w-10 text-w-100 border border-w-20;
  }
  
  &--primary {
    @apply bg-purple text-w-100;
  }
  
  &--small {
    @apply px-[24rpx] py-[12rpx] text-[24rpx];
  }
  
  &--medium {
    @apply px-[32rpx] py-[16rpx] text-[28rpx];
  }
  
  &--large {
    @apply px-[40rpx] py-[20rpx] text-[32rpx];
  }
  
  &--block {
    @apply w-full;
  }
  
  &--disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  &--loading {
    @apply cursor-wait;
  }
}
</style>
```

### 类型定义规范
```typescript
// types.ts
export interface ButtonProps {
  type?: 'default' | 'primary' | 'secondary'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  text?: string
}

export interface ButtonEmits {
  click: [event: Event]
}
```

### 导出文件规范
```typescript
// index.ts
import CustomButton from './CustomButton.vue'
export default CustomButton
export type { ButtonProps, ButtonEmits } from './types'
```

## 样式开发规范

### Tailwind CSS 类名使用
参考 [tailwind.config.js](mdc:tailwind.config.js) 中的自定义配置：

```scss
// 使用项目自定义的颜色系统
.component {
  @apply bg-deepbg text-w-100;
  
  &__header {
    @apply flex items-center justify-between p-[32rpx];
  }
  
  &__content {
    @apply flex-1 px-[32rpx] py-[24rpx];
  }
  
  &__footer {
    @apply flex items-center justify-center p-[32rpx] border-t border-w-10;
  }
}
```

### 响应式设计
```scss
// 使用 rpx 单位进行移动端适配
.responsive-component {
  @apply w-full;
  
  // 小屏幕
  @media (max-width: 375px) {
    @apply px-[24rpx];
  }
  
  // 大屏幕
  @media (min-width: 768px) {
    @apply px-[48rpx];
  }
}
```

### 主题色彩使用
```scss
// 使用项目定义的主题色
.themed-component {
  // 主要背景色
  @apply bg-deepbg;
  
  // 次要背景色
  @apply bg-bg;
  
  // 主题色
  @apply text-purple;
  
  // 白色文字
  @apply text-w-100;
  
  // 半透明白色
  @apply text-w-60;
  
  // 金色
  @apply text-glod;
}
```

## 组件交互规范

### 事件处理
```vue
<template>
  <view class="interactive-component">
    <!-- 点击事件 -->
    <view 
      class="clickable-item"
      @click="handleItemClick"
    >
      点击项
    </view>
    
    <!-- 长按事件 -->
    <view 
      class="longpress-item"
      @longpress="handleLongPress"
    >
      长按项
    </view>
    
    <!-- 滑动事件 -->
    <view 
      class="swipe-item"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      滑动项
    </view>
  </view>
</template>

<script lang="ts" setup>
// 点击事件处理
const handleItemClick = (event: Event) => {
  // 防止事件冒泡
  event.stopPropagation()
  
  // 触觉反馈
  uni.vibrateShort()
  
  // 业务逻辑
  console.log('项被点击')
}

// 长按事件处理
const handleLongPress = () => {
  uni.showActionSheet({
    itemList: ['编辑', '删除'],
    success: (res) => {
      console.log('选择了第' + res.tapIndex + '个选项')
    }
  })
}
</script>
```

### 加载状态处理
```vue
<template>
  <view class="loading-component">
    <!-- 加载中状态 -->
    <view v-if="loading" class="loading-container">
      <uv-loading-icon size="60rpx" />
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 内容区域 -->
    <view v-else-if="data.length > 0" class="content-container">
      <view v-for="item in data" :key="item.id">
        {{ item.title }}
      </view>
    </view>
    
    <!-- 空状态 -->
    <view v-else class="empty-container">
      <u-empty 
        text="暂无数据"
        icon="/static/image/empty/network.png"
      />
    </view>
  </view>
</template>
```

## 性能优化规范

### 组件懒加载
```vue
<script lang="ts" setup>
import { defineAsyncComponent } from 'vue'

// 异步加载大型组件
const HeavyComponent = defineAsyncComponent(() => 
  import('@/components/HeavyComponent/HeavyComponent.vue')
)
</script>
```

### 图片懒加载
```vue
<template>
  <!-- 使用 uv-image 组件的懒加载功能 -->
  <uv-image
    :src="imageUrl"
    :lazy-load="true"
    :fade-show="true"
    loading-icon="photo"
    error-icon="error-circle"
    width="200rpx"
    height="200rpx"
  />
</template>
```

### 虚拟滚动
```vue
<template>
  <!-- 使用 uv-waterfall 进行虚拟滚动 -->
  <uv-waterfall
    v-model="list"
    :column-count="2"
    :column-gap="20"
  >
    <template #default="{ item, index }">
      <view class="waterfall-item">
        {{ item.title }}
      </view>
    </template>
  </uv-waterfall>
</template>
```

## 可访问性规范

### 语义化标签
```vue
<template>
  <view class="accessible-component">
    <!-- 使用语义化的 role 属性 -->
    <view role="button" @click="handleClick">
      按钮
    </view>
    
    <!-- 使用 aria-label 提供辅助信息 -->
    <view 
      aria-label="用户头像"
      @click="showUserProfile"
    >
      <image :src="userAvatar" />
    </view>
    
    <!-- 使用 aria-hidden 隐藏装饰性元素 -->
    <view aria-hidden="true" class="decorative-icon">
      ✨
    </view>
  </view>
</template>
```

### 键盘导航支持
```vue
<template>
  <view 
    class="focusable-item"
    tabindex="0"
    @keydown="handleKeydown"
  >
    可聚焦项
  </view>
</template>

<script lang="ts" setup>
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    // 处理回车或空格键
    event.preventDefault()
    handleActivate()
  }
}
</script>
```
