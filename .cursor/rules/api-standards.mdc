---
description: 
globs: 
alwaysApply: false
---
# API 开发规范

## API 架构设计

### 接口组织结构
API 接口按业务模块组织在 `/src/api/` 目录下：

```
src/api/
├── account.ts          # 账户相关接口
├── area.ts            # 地区相关接口
├── ask.ts             # 问答相关接口
├── badge.ts           # 徽章相关接口
├── collection.ts      # 收藏相关接口
├── comment.ts         # 评论相关接口
├── dynamic.ts         # 动态相关接口
├── follow.ts          # 关注相关接口
├── history.ts         # 历史记录相关接口
├── interaction.ts     # 互动相关接口
├── mass.ts            # 群发相关接口
├── medal.ts           # 勋章相关接口
├── message.ts         # 消息相关接口
├── order.ts           # 订单相关接口
├── rank.ts            # 排行相关接口
├── repertoire.ts      # 剧目相关接口
├── scan.ts            # 扫码相关接口
├── share.ts           # 分享相关接口
├── tabulation.ts      # 统计相关接口
├── theater.ts         # 剧院相关接口
├── ticket.ts          # 票务相关接口
├── upload.ts          # 上传相关接口
└── user.ts            # 用户相关接口
```

### 接口命名规范
```typescript
// 获取数据 - get + 资源名
export const getUserInfo = (params: GetUserInfoParams) => { ... }

// 创建数据 - create + 资源名
export const createOrder = (data: CreateOrderData) => { ... }

// 更新数据 - update + 资源名
export const updateUserProfile = (data: UpdateUserProfileData) => { ... }

// 删除数据 - delete + 资源名
export const deleteComment = (id: number) => { ... }

// 列表数据 - get + 资源名 + List
export const getTheaterList = (params: GetTheaterListParams) => { ... }
```

## 请求封装规范

### 统一请求配置
参考 [src/utils/request.ts](mdc:src/utils/request.ts) 进行请求配置：

```typescript
import { Request } from '@/uni_modules/uv-ui-tools/libs/luch-request'

const request = new Request({
  baseURL: 'https://api.example.com',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加 token
    const token = uni.getStorageSync('token')
    if (token) {
      config.header.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response
    if (data.code === 200) {
      return data.data
    } else {
      handleApiError(data)
      return Promise.reject(data)
    }
  },
  (error) => {
    handleNetworkError(error)
    return Promise.reject(error)
  }
)
```

### 接口定义规范
```typescript
// 定义请求参数类型
interface GetUserInfoParams {
  userId: number
}

// 定义响应数据类型
interface UserInfo {
  id: number
  nickname: string
  avatar: string
  phone?: string
  email?: string
}

// 定义接口函数
export const getUserInfo = (params: GetUserInfoParams): Promise<UserInfo> => {
  return request<UserInfo>({
    url: '/user/info',
    method: 'GET',
    params
  })
}

// POST 请求示例
export const updateUserInfo = (data: Partial<UserInfo>): Promise<void> => {
  return request({
    url: '/user/update',
    method: 'POST',
    data
  })
}
```

## 错误处理规范

### 统一错误处理
```typescript
// 错误码定义
enum ErrorCode {
  SUCCESS = 200,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  SERVER_ERROR = 500
}

// 错误处理函数
const handleApiError = (error: any) => {
  const { code, message } = error
  
  switch (code) {
    case ErrorCode.UNAUTHORIZED:
      // 清除 token，跳转到登录页
      uni.removeStorageSync('token')
      uni.reLaunch({
        url: '/pages/login/index'
      })
      break
      
    case ErrorCode.FORBIDDEN:
      uni.showToast({
        title: '权限不足',
        icon: 'none'
      })
      break
      
    case ErrorCode.NOT_FOUND:
      uni.showToast({
        title: '资源不存在',
        icon: 'none'
      })
      break
      
    case ErrorCode.SERVER_ERROR:
      uni.showToast({
        title: '服务器错误',
        icon: 'none'
      })
      break
      
    default:
      uni.showToast({
        title: message || '请求失败',
        icon: 'none'
      })
  }
}

// 网络错误处理
const handleNetworkError = (error: any) => {
  uni.showToast({
    title: '网络连接失败',
    icon: 'none'
  })
}
```

### 页面中的错误处理
```typescript
// 在页面中使用 try-catch 处理异步请求
const loadUserInfo = async () => {
  try {
    loading.value = true
    const userInfo = await getUserInfo({ userId: 123 })
    // 处理成功响应
  } catch (error) {
    // 错误已在拦截器中处理，这里可以做额外处理
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}
```

## 数据类型定义

### 通用类型定义
```typescript
// 分页参数
interface PaginationParams {
  page: number
  pageSize: number
}

// 分页响应
interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用响应格式
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}
```

### 业务类型定义
```typescript
// 用户信息
interface UserInfo {
  id: number
  nickname: string
  avatar: string
  phone?: string
  email?: string
  createTime: string
  updateTime: string
}

// 剧院信息
interface TheaterInfo {
  id: number
  name: string
  address: string
  city: string
  province: string
  latitude: number
  longitude: number
  images: string[]
  description: string
}

// 剧目信息
interface RepertoireInfo {
  id: number
  title: string
  poster: string
  description: string
  duration: number
  category: string
  tags: string[]
  theater: TheaterInfo
  showTimes: ShowTime[]
}
```

## 接口调用最佳实践

### 页面数据加载
```typescript
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getUserInfo, getTheaterList } from '@/api'

const userInfo = ref<UserInfo>()
const theaterList = ref<TheaterInfo[]>([])
const loading = ref(false)

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadUserInfo(),
    loadTheaterList()
  ])
})

const loadUserInfo = async () => {
  try {
    const data = await getUserInfo({ userId: 123 })
    userInfo.value = data
  } catch (error) {
    // 错误处理
  }
}

const loadTheaterList = async () => {
  try {
    loading.value = true
    const data = await getTheaterList({
      page: 1,
      pageSize: 10
    })
    theaterList.value = data.list
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false
  }
}
</script>
```

### 表单提交
```typescript
const submitForm = async () => {
  try {
    // 表单验证
    if (!formData.value.name) {
      uni.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return
    }
    
    loading.value = true
    await updateUserInfo(formData.value)
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    // 返回上一页或刷新数据
    uni.navigateBack()
    
  } catch (error) {
    // 错误已在拦截器中处理
  } finally {
    loading.value = false
  }
}
```

## 缓存策略

### 数据缓存
```typescript
// 缓存用户信息
const getCachedUserInfo = async (userId: number) => {
  const cacheKey = `user_info_${userId}`
  const cached = uni.getStorageSync(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
    return cached.data
  }
  
  const data = await getUserInfo({ userId })
  uni.setStorageSync(cacheKey, {
    data,
    timestamp: Date.now()
  })
  
  return data
}
```

### 请求去重
```typescript
// 防止重复请求
const pendingRequests = new Map<string, Promise<any>>()

const request = <T>(config: RequestConfig): Promise<T> => {
  const key = `${config.method}_${config.url}_${JSON.stringify(config.params || config.data)}`
  
  if (pendingRequests.has(key)) {
    return pendingRequests.get(key)!
  }
  
  const promise = http(config).finally(() => {
    pendingRequests.delete(key)
  })
  
  pendingRequests.set(key, promise)
  return promise
}
```

## 性能优化

### 请求优化
- 使用适当的缓存策略
- 实现请求防抖和节流
- 合理设置请求超时时间
- 使用请求取消机制

### 数据处理优化
- 对大量数据进行分页处理
- 使用虚拟滚动处理长列表
- 实现数据懒加载
- 优化图片加载策略
