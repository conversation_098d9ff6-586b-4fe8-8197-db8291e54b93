---
description: 
globs: 
alwaysApply: false
---
# 开发指南规则

## 技术栈
- **框架**: uni-app (Vue 3 + TypeScript)
- **状态管理**: Pinia
- **UI 组件**: uview-plus
- **样式**: Tailwind CSS + SCSS
- **路由**: uni-crazy-router
- **工具库**: lodash, dayjs

## 编码规范

### Vue 组件规范
```vue
<template>
  <!-- 使用 Tailwind CSS 类名 -->
  <view class="flex flex-col items-center justify-center min-h-screen bg-deepbg">
    <!-- 组件内容 -->
  </view>
</template>

<script lang="ts" setup>
// 导入顺序：第三方库 -> 项目内部模块 -> 类型定义
import { ref, onMounted } from 'vue'
import { useGlobalStore } from '@/stores/global'
import type { UserInfo } from '@/types/user'

// 使用 Composition API
const globalStore = useGlobalStore()
const userInfo = ref<UserInfo>()

onMounted(() => {
  // 组件挂载后的逻辑
})
</script>

<style lang="scss" scoped>
// 使用 SCSS 和 Tailwind CSS
// 自定义样式应该最小化，优先使用 Tailwind 类
</style>
```

### TypeScript 规范
- 所有文件使用 TypeScript
- 定义明确的类型接口
- 使用泛型提高代码复用性
- 避免使用 `any` 类型

### API 调用规范
```typescript
// 在 /src/api/ 目录下按模块组织 API
export const getUserInfo = (params: GetUserInfoParams) => {
  return request<UserInfo>({
    url: '/user/info',
    method: 'GET',
    params
  })
}
```

## 样式开发规范

### Tailwind CSS 使用
参考 [tailwind.config.js](mdc:tailwind.config.js) 中的自定义配置：

- **颜色系统**: 使用项目定义的色彩变量
  - `bg-deepbg` - 深色背景 (#07011D)
  - `text-w-100` - 白色文字
  - `text-purple` - 紫色主题色

- **字体系统**: 
  - `font-Regular` - 常规字体
  - `font-Medium` - 中等粗细字体

### 移动端适配
- 使用 `rpx` 单位进行尺寸设置
- 遵循小程序设计规范
- 考虑不同设备的屏幕适配

## 组件开发规范

### 组件结构
```
components/
├── ComponentName/
│   ├── ComponentName.vue     # 主组件
│   ├── types.ts             # 类型定义
│   └── index.ts             # 导出文件
```

### 组件命名
- 使用 PascalCase 命名组件文件
- 组件名应该语义化，能够清楚表达组件功能
- 避免使用缩写，保持名称完整

### Props 定义
```typescript
interface Props {
  title: string
  visible?: boolean
  onConfirm?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})
```

## 页面开发规范

### 页面结构
- 使用 `<script setup>` 语法
- 合理使用生命周期钩子
- 状态管理使用 Pinia

### 页面路由
使用 uni-crazy-router 进行路由管理：
```typescript
// 页面跳转
uni.$u.route('/pages/user/profile', { id: 123 })

// 返回上一页
uni.$u.route('back')
```

## 性能优化

### 图片优化
- 使用 `uv-image` 组件进行图片懒加载
- 合理设置图片尺寸和格式
- 使用 CDN 加速图片加载

### 代码分割
- 按页面进行代码分割
- 合理使用动态导入
- 避免单个文件过大

## 调试和测试

### 开发调试
```bash
# 开发环境运行
npm run dev

# 代码检查
npm run lint

# 样式检查
npm run stylelint
```

### 错误处理
- 使用 try-catch 处理异步操作
- 统一的错误提示机制
- 记录关键操作日志

## 部署和构建

### 构建命令
```bash
# 生产环境构建
npm run build

# 类型检查
npm run type-check
```

### 代码质量
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 使用 Stylelint 进行样式检查
