---
description: 
globs: 
alwaysApply: false
---
# 项目结构规则

## 项目概览
这是一个基于 uni-app 框架的微信小程序项目，使用 Vue 3 + TypeScript + Tailwind CSS 开发。

## 核心文件说明

### 入口文件
- [src/main.ts](mdc:src/main.ts) - 应用主入口文件，负责应用初始化、插件注册
- [src/App.vue](mdc:src/App.vue) - 应用根组件，包含全局样式和应用生命周期
- [index.html](mdc:index.html) - HTML 模板文件

### 配置文件
- [package.json](mdc:package.json) - 项目依赖和脚本配置
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS 配置，包含自定义主题色彩和样式

## 目录结构说明

### `/src/api/` - API 接口层
包含所有后端接口调用的封装，按业务模块分类：
- [src/api/account.ts](mdc:src/api/account.ts) - 账户相关接口
- [src/api/area.ts](mdc:src/api/area.ts) - 地区相关接口
- [src/api/ask.ts](mdc:src/api/ask.ts) - 问答相关接口

### `/src/pages/` - 页面文件
主要页面结构：
- `homepage/` - 首页
- `account/` - 账户页面
- `collection/` - 收藏页面
- `sub/` - 子页面模块

### `/src/components/` - 公共组件
- `Ask/` - 问答组件
- `Btn/` - 按钮组件
- `Item/` - 列表项组件
- `List/` - 列表组件

### `/src/stores/` - 状态管理
使用 Pinia 进行状态管理：
- [src/stores/global.ts](mdc:src/stores/global.ts) - 全局状态

### `/src/utils/` - 工具函数
- [src/utils/methods.ts](mdc:src/utils/methods.ts) - 通用方法
- [src/utils/mpShare.ts](mdc:src/utils/mpShare.ts) - 小程序分享配置

### `/src/router/` - 路由配置
- [src/router/index.ts](mdc:src/router/index.ts) - 路由配置文件

### `/src/uni_modules/` - 第三方组件库
包含 uview-plus、mescroll-uni 等 uni-app 生态组件

## 开发规范

### 文件命名
- 页面文件：使用小写字母和连字符，如 `user-profile.vue`
- 组件文件：使用 PascalCase，如 `UserProfile.vue`
- 工具文件：使用 camelCase，如 `userUtils.ts`

### 组件开发
- 优先使用 Composition API
- 使用 TypeScript 进行类型约束
- 组件应该是可复用和可测试的

### 样式规范
- 使用 Tailwind CSS 进行样式开发
- 自定义样式放在 `/src/static/scss/` 目录
- 遵循移动端适配规范，使用 rpx 单位
