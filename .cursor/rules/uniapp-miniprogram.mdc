---
description: 
globs: 
alwaysApply: false
---
# uni-app 小程序开发规则

## 小程序开发基础

### 项目配置
- 使用 uni-app 框架进行跨平台开发
- 主要目标平台：微信小程序
- 开发工具：HBuilderX 或 VSCode

### 页面配置
每个页面都需要在 `pages.json` 中进行配置：
```json
{
  "pages": [
    {
      "path": "pages/homepage/index",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom"
      }
    }
  ]
}
```

## 生命周期规范

### 应用生命周期
在 [src/App.vue](mdc:src/App.vue) 中使用：
```typescript
onLaunch(() => {
  // 应用启动时的初始化逻辑
})

onShow(() => {
  // 应用显示时的逻辑
})

onHide(() => {
  // 应用隐藏时的逻辑
})
```

### 页面生命周期
```typescript
onLoad((options) => {
  // 页面加载时，获取页面参数
})

onShow(() => {
  // 页面显示时
})

onReady(() => {
  // 页面初次渲染完成时
})

onHide(() => {
  // 页面隐藏时
})

onUnload(() => {
  // 页面卸载时
})
```

## 小程序 API 使用规范

### 网络请求
使用统一的请求封装，参考 [src/utils/request.ts](mdc:src/utils/request.ts)：
```typescript
// 设置请求拦截器
uni.addInterceptor('request', {
  invoke(args) {
    // 请求前处理
    args.header = {
      ...args.header,
      'Authorization': `Bearer ${token}`
    }
  }
})
```

### 存储管理
```typescript
// 同步存储
uni.setStorageSync('key', value)
const value = uni.getStorageSync('key')

// 异步存储
uni.setStorage({
  key: 'key',
  data: value
})
```

### 路由跳转
使用 uni-crazy-router 进行路由管理：
```typescript
// 保留当前页面，跳转到应用内的某个页面
uni.$u.route('/pages/detail/index', { id: 123 })

// 关闭当前页面，跳转到应用内的某个页面
uni.$u.route('/pages/detail/index', { id: 123 }, 'redirectTo')

// 跳转到 tabBar 页面
uni.$u.route('/pages/homepage/index', {}, 'switchTab')
```

## 组件开发规范

### 自定义组件
```vue
<template>
  <view class="custom-component">
    <!-- 组件内容 -->
  </view>
</template>

<script lang="ts" setup>
// 组件属性定义
interface Props {
  title: string
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// 事件定义
const emit = defineEmits<{
  confirm: [value: string]
  cancel: []
}>()
</script>
```

### 第三方组件使用
项目使用了多个 uni-app 生态组件：

- **uview-plus**: 主要 UI 组件库
- **mescroll-uni**: 下拉刷新和上拉加载
- **uv-waterfall**: 瀑布流布局
- **lime-painter**: 海报生成

使用示例：
```vue
<template>
  <!-- uview-plus 组件 -->
  <u-button type="primary" @click="handleClick">点击按钮</u-button>
  
  <!-- 下拉刷新 -->
  <mescroll-uni @down="downCallback" @up="upCallback">
    <view>列表内容</view>
  </mescroll-uni>
</template>
```

## 样式开发规范

### rpx 单位使用
- 使用 `rpx` 作为主要尺寸单位
- 1rpx = 屏幕宽度 / 750
- 设计稿通常以 750px 为基准

### Tailwind CSS 配置
参考 [tailwind.config.js](mdc:tailwind.config.js) 中的小程序适配配置：
```javascript
module.exports = {
  content: ['./src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    // 自定义颜色系统
    colors: {
      deepbg: '#07011D',
      purple: '#9C75D3',
      // ... 其他颜色
    }
  }
}
```

### 全局样式
在 [src/App.vue](mdc:src/App.vue) 中定义全局样式：
```scss
// 页面背景色
page {
  background: #07011d;
}

// 自定义组件样式
.u-toast {
  .u-toast__content {
    @apply pb-[12rpx] pl-[20rpx] pr-[20rpx] pt-[12rpx] #{!important};
  }
}
```

## 性能优化

### 图片优化
```vue
<template>
  <!-- 使用 uv-image 进行图片懒加载 -->
  <uv-image
    :src="imageUrl"
    width="200rpx"
    height="200rpx"
    mode="aspectFill"
    :lazy-load="true"
  />
</template>
```

### 列表优化
```vue
<template>
  <!-- 使用 mescroll-uni 进行列表优化 -->
  <mescroll-uni
    ref="mescrollRef"
    @down="downCallback"
    @up="upCallback"
    :down="downOption"
    :up="upOption"
  >
    <view v-for="item in dataList" :key="item.id">
      {{ item.name }}
    </view>
  </mescroll-uni>
</template>
```

## 调试和测试

### 小程序调试
```typescript
// 开发环境日志
if (process.env.NODE_ENV === 'development') {
  console.log('调试信息')
}

// 使用 uni.showToast 进行调试提示
uni.showToast({
  title: '调试信息',
  icon: 'none'
})
```

### 真机调试
- 使用微信开发者工具进行预览
- 扫码在真机上测试
- 注意不同设备的兼容性

## 发布和部署

### 构建配置
```bash
# 构建微信小程序
npm run build

# 构建后的文件在 dist/build/mp-weixin 目录
```

### 上传发布
1. 使用微信开发者工具打开构建后的目录
2. 点击"上传"按钮
3. 填写版本号和项目备注
4. 在微信公众平台提交审核

## 常见问题和解决方案

### 路由问题
- 小程序页面栈最多 10 层
- 使用 `redirectTo` 避免页面栈溢出
- tabBar 页面只能使用 `switchTab` 跳转

### 样式问题
- 避免使用 `*` 选择器
- 注意 `view` 标签的默认样式
- 使用 `!important` 时要谨慎

### 兼容性问题
- 不同版本微信客户端的 API 差异
- 使用 `uni.canIUse()` 检查 API 兼容性
- 做好降级处理
