---
description: 
globs: 
alwaysApply: false
---
# 状态管理规范

## Pinia 状态管理

### 基础配置
项目使用 Pinia 作为状态管理工具，在 [src/main.ts](mdc:src/main.ts) 中已完成初始化：

```typescript
import { createPinia } from 'pinia'
import { createSSRApp } from 'vue'

export function createApp() {
  const app = createSSRApp(App)
  app.use(createPinia())
  return { app }
}
```

### Store 文件结构
```
src/stores/
├── global.ts           # 全局状态
├── user.ts            # 用户状态
├── theater.ts         # 剧院状态
├── order.ts           # 订单状态
└── index.ts           # 导出文件
```

## Store 定义规范

### 全局状态 Store
参考 [src/stores/global.ts](mdc:src/stores/global.ts) 的实现：

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useGlobalStore = defineStore('global', () => {
  // 状态定义
  const isLogin = ref(false)
  const userInfo = ref<UserInfo | null>(null)
  const systemInfo = ref<SystemInfo | null>(null)
  const currentCity = ref<CityInfo | null>(null)
  
  // 计算属性
  const isLoggedIn = computed(() => isLogin.value && userInfo.value !== null)
  const userName = computed(() => userInfo.value?.nickname || '未登录')
  
  // 方法定义
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    isLogin.value = true
    
    // 持久化存储
    uni.setStorageSync('userInfo', info)
    uni.setStorageSync('isLogin', true)
  }
  
  const logout = () => {
    userInfo.value = null
    isLogin.value = false
    
    // 清除存储
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('isLogin')
    uni.removeStorageSync('token')
  }
  
  const initializeApp = async () => {
    // 获取系统信息
    const sysInfo = uni.getSystemInfoSync()
    systemInfo.value = sysInfo
    
    // 恢复登录状态
    const savedUserInfo = uni.getStorageSync('userInfo')
    const savedIsLogin = uni.getStorageSync('isLogin')
    
    if (savedUserInfo && savedIsLogin) {
      userInfo.value = savedUserInfo
      isLogin.value = true
    }
    
    // 获取当前城市
    await getCurrentCity()
  }
  
  const getCurrentCity = async () => {
    try {
      const location = await getLocation()
      const city = await getCityByLocation(location)
      currentCity.value = city
    } catch (error) {
      console.error('获取城市信息失败:', error)
    }
  }
  
  return {
    // 状态
    isLogin,
    userInfo,
    systemInfo,
    currentCity,
    
    // 计算属性
    isLoggedIn,
    userName,
    
    // 方法
    setUserInfo,
    logout,
    initializeApp,
    getCurrentCity
  }
})
```

### 用户状态 Store
```typescript
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo, updateUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态定义
  const profile = ref<UserProfile | null>(null)
  const preferences = ref<UserPreferences>({
    theme: 'dark',
    language: 'zh-CN',
    notifications: true
  })
  const followList = ref<UserInfo[]>([])
  const fansList = ref<UserInfo[]>([])
  
  // 获取用户资料
  const fetchProfile = async (userId: number) => {
    try {
      const data = await getUserInfo({ userId })
      profile.value = data
      return data
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw error
    }
  }
  
  // 更新用户资料
  const updateProfile = async (data: Partial<UserProfile>) => {
    try {
      await updateUserInfo(data)
      if (profile.value) {
        Object.assign(profile.value, data)
      }
      
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw error
    }
  }
  
  // 设置用户偏好
  const setPreferences = (prefs: Partial<UserPreferences>) => {
    preferences.value = { ...preferences.value, ...prefs }
    uni.setStorageSync('userPreferences', preferences.value)
  }
  
  // 初始化用户偏好
  const initializePreferences = () => {
    const saved = uni.getStorageSync('userPreferences')
    if (saved) {
      preferences.value = saved
    }
  }
  
  return {
    // 状态
    profile,
    preferences,
    followList,
    fansList,
    
    // 方法
    fetchProfile,
    updateProfile,
    setPreferences,
    initializePreferences
  }
})
```

## 组合式 Store 使用规范

### 页面中使用 Store
```vue
<template>
  <view class="user-page">
    <view v-if="globalStore.isLoggedIn" class="user-info">
      <image :src="userStore.profile?.avatar" class="avatar" />
      <text class="nickname">{{ globalStore.userName }}</text>
    </view>
    
    <view v-else class="login-prompt">
      <u-button type="primary" @click="goToLogin">
        请先登录
      </u-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { useGlobalStore } from '@/stores/global'
import { useUserStore } from '@/stores/user'

const globalStore = useGlobalStore()
const userStore = useUserStore()

onMounted(async () => {
  if (globalStore.isLoggedIn) {
    await userStore.fetchProfile(globalStore.userInfo!.id)
  }
})

const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index'
  })
}
</script>
```

### 在组件中使用 Store
```vue
<template>
  <view class="theater-list">
    <view v-for="theater in theaterStore.list" :key="theater.id">
      {{ theater.name }}
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { useTheaterStore } from '@/stores/theater'

const theaterStore = useTheaterStore()

onMounted(() => {
  theaterStore.fetchList()
})
</script>
```

## 持久化存储规范

### 自动持久化
```typescript
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useSettingsStore = defineStore('settings', () => {
  const settings = ref<AppSettings>({
    theme: 'dark',
    language: 'zh-CN',
    autoPlay: true
  })
  
  // 监听状态变化，自动保存
  watch(
    settings,
    (newSettings) => {
      uni.setStorageSync('appSettings', newSettings)
    },
    { deep: true }
  )
  
  // 初始化时恢复数据
  const initializeSettings = () => {
    const saved = uni.getStorageSync('appSettings')
    if (saved) {
      settings.value = saved
    }
  }
  
  return {
    settings,
    initializeSettings
  }
})
```

### 手动持久化
```typescript
export const useOrderStore = defineStore('order', () => {
  const orders = ref<Order[]>([])
  const currentOrder = ref<Order | null>(null)
  
  // 保存到本地存储
  const saveToStorage = () => {
    uni.setStorageSync('orders', orders.value)
    if (currentOrder.value) {
      uni.setStorageSync('currentOrder', currentOrder.value)
    }
  }
  
  // 从本地存储恢复
  const restoreFromStorage = () => {
    const savedOrders = uni.getStorageSync('orders')
    const savedCurrentOrder = uni.getStorageSync('currentOrder')
    
    if (savedOrders) {
      orders.value = savedOrders
    }
    
    if (savedCurrentOrder) {
      currentOrder.value = savedCurrentOrder
    }
  }
  
  const addOrder = (order: Order) => {
    orders.value.push(order)
    saveToStorage()
  }
  
  return {
    orders,
    currentOrder,
    addOrder,
    saveToStorage,
    restoreFromStorage
  }
})
```

## 状态同步规范

### Store 间通信
```typescript
// 在一个 Store 中使用另一个 Store
export const useOrderStore = defineStore('order', () => {
  const globalStore = useGlobalStore()
  const userStore = useUserStore()
  
  const createOrder = async (orderData: CreateOrderData) => {
    // 检查登录状态
    if (!globalStore.isLoggedIn) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }
    
    // 获取用户信息
    const userId = globalStore.userInfo!.id
    const userProfile = userStore.profile
    
    // 创建订单逻辑
    const order = await createOrderAPI({
      ...orderData,
      userId,
      userInfo: userProfile
    })
    
    return order
  }
  
  return {
    createOrder
  }
})
```

### 响应式状态共享
```typescript
// 创建可共享的响应式状态
import { reactive } from 'vue'

// 全局事件总线
export const eventBus = reactive<{
  events: Record<string, any>
}>({
  events: {}
})

// 发送事件
export const emit = (event: string, data?: any) => {
  eventBus.events[event] = data
}

// 监听事件
export const on = (event: string, callback: (data: any) => void) => {
  watch(
    () => eventBus.events[event],
    (newValue) => {
      if (newValue !== undefined) {
        callback(newValue)
      }
    }
  )
}
```

## 性能优化规范

### 按需加载 Store
```typescript
// 在需要时才创建 Store 实例
const useTheaterStore = () => {
  // 只在需要时创建
  return defineStore('theater', () => {
    // Store 定义
  })()
}
```

### 状态重置
```typescript
export const useDataStore = defineStore('data', () => {
  const data = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 重置状态
  const reset = () => {
    data.value = []
    loading.value = false
    error.value = null
  }
  
  // 页面卸载时清理
  const cleanup = () => {
    reset()
  }
  
  return {
    data,
    loading,
    error,
    reset,
    cleanup
  }
})
```

### 状态缓存
```typescript
export const useCacheStore = defineStore('cache', () => {
  const cache = ref<Map<string, any>>(new Map())
  
  const set = (key: string, value: any, ttl: number = 5 * 60 * 1000) => {
    cache.value.set(key, {
      value,
      expireTime: Date.now() + ttl
    })
  }
  
  const get = (key: string) => {
    const item = cache.value.get(key)
    if (!item) return null
    
    if (Date.now() > item.expireTime) {
      cache.value.delete(key)
      return null
    }
    
    return item.value
  }
  
  const clear = () => {
    cache.value.clear()
  }
  
  return {
    set,
    get,
    clear
  }
})
```

## 类型定义规范

### Store 类型定义
```typescript
// 用户信息类型
export interface UserInfo {
  id: number
  nickname: string
  avatar: string
  phone?: string
  email?: string
}

// 用户资料类型
export interface UserProfile extends UserInfo {
  gender: 'male' | 'female' | 'unknown'
  birthday?: string
  city?: string
  signature?: string
}

// 用户偏好类型
export interface UserPreferences {
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  notifications: boolean
  autoPlay?: boolean
}

// 系统信息类型
export interface SystemInfo {
  platform: string
  system: string
  version: string
  screenWidth: number
  screenHeight: number
  statusBarHeight: number
  safeArea: {
    top: number
    bottom: number
    left: number
    right: number
  }
}
```
