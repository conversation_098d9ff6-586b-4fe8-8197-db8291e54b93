/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    backgroundSize: {
      fullAuto: '100% auto',
    },
    boxShadow: {
      btnCol1N: '0 10rpx #272555',
      btnCol1A: '0 0rpx #272555',

      btnCol2N: '0 10rpx #615D97',
      btnCol2A: '0 0rpx #615D97',

      btnCol1O: '16rpx 24rpx 30rpx rgba(192,165,235,0.4)',
      btnCol2O: '16rpx 24rpx 30rpx rgba(213,213,213,0.4)',
      btnCol3O: '16rpx 24rpx 30rpx rgba(244,204,139,0.4)',
      btnCol4O: '16rpx 24rpx 30rpx rgba(38,36,84,0.4)',
      boxShadow: 'inset 0rpx 0rpx 4rpx 0rpx rgba(255,255,255,0.5)',
    },
    colors: {
      transparent: 'transparent',
      b: {
        0: 'rgb(0 0 0 / 0%)',
        10: 'rgb(0 0 0 / 10%)',
        20: 'rgb(0 0 0 / 20%)',
        40: 'rgb(0 0 0 / 40%)',
        50: 'rgb(0 0 0 / 50%)',
        60: 'rgb(0 0 0 / 60%)',
        70: 'rgb(0 0 0 / 70%)',
        80: 'rgb(0 0 0 / 80%)',
        100: 'rgb(0 0 0 / 100%)',
      },
      bg: '#1B1629',
      bgE: '#0A0713',
      bgS: '#1E1733',
      black: '#000000',
      blue: '#3391FF',
      boxBg: 'rgba(201, 191, 255, 0.30)',
      deepPurple: '#272555',
      deepbg: '#07011D',
      deepbg2: 'rgb(7 1 29 / 50%)',
      deepbg3: 'rgb(7 1 29 / 20%)',
      glod: '#A55824',
      glod2: '#FA980F',
      lightGlod: {
        70: 'rgba(233, 196, 157, 0.7)',
        100: '#E9C49D',
      },
      gray: '#999999',
      gray1: '#A9A9A9',
      lightPurple: '#9592A0',
      purple: '#9C75D3',
      bgPurple: 'rgba(92 78 141 / 20%)',
      purpleBg: 'rgba(156, 117, 211, 0.3)',
      purpleS: '#A37BDD',
      purpleE: '#4641A4',
      red: '#EC2A2A',
      w: {
        5: 'rgb(255 255 255 / 5%)',
        10: 'rgb(255 255 255 / 10%)',
        20: 'rgb(255 255 255 / 20%)',
        30: 'rgb(255 255 255 / 30%)',
        40: 'rgb(255 255 255 / 40%)',
        50: 'rgb(255 255 255 / 50%)',
        60: 'rgb(255 255 255 / 60%)',
        70: 'rgb(255 255 255 / 70%)',
        80: 'rgb(255 255 255 / 80%)',
        100: '#FFFFFF',
        f: 'rgba(44, 35, 70, 1)',
        t: 'rgba(12, 8, 23, 1)',
        se: 'rgb(102 102 102 / 60%)',
      },
      wl: { 60: 'rgb(102 102 102 / 60%)' },
    },
    fontFamily: {
      Medium: ['PingFangSC-Medium', 'PingFang SC'],
      Regular: ['PingFangSC-Regular', 'PingFang SC'],
      emojifont: ['emojifont'],
    },
    extend: {},
  },
  plugins: [],
}
